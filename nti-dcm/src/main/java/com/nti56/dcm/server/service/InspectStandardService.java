package com.nti56.dcm.server.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;

import cn.hutool.core.collection.CollectionUtil;

import com.nti56.dcm.server.domain.InspectStandard;
import com.nti56.dcm.server.domain.MaintenanceStandard;
import com.nti56.dcm.server.domain.enums.FileBizTypeEnum;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.domain.enums.StandardRecordTypeEnum;
import com.nti56.dcm.server.domain.enums.StandardTypeEnum;
import com.nti56.dcm.server.entity.DeviceBomEntity;
import com.nti56.dcm.server.entity.FileEntity;
import com.nti56.dcm.server.entity.InspectStandardEntity;
import com.nti56.dcm.server.entity.InspectStandardItemEntity;
import com.nti56.dcm.server.entity.MaintenanceStandardEntity;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.mapper.FileMapper;
import com.nti56.dcm.server.mapper.InspectStandardItemMapper;
import com.nti56.dcm.server.mapper.InspectStandardMapper;
import com.nti56.dcm.server.model.dto.InspectStandardDto;
import com.nti56.dcm.server.model.dto.StandardCopyDto;
import com.nti56.dcm.server.model.dto.StandardImportDto;
import com.nti56.dcm.server.model.dto.StandardOverrideDto;
import com.nti56.dcm.server.model.vo.CountVo;
import com.nti56.dcm.server.model.vo.ImportFailInfo;
import com.nti56.dcm.server.model.vo.InspectStandardItemVo;
import com.nti56.dcm.server.model.vo.InspectStandardVo;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 点巡检标准表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Service
@Slf4j
public class InspectStandardService {

    @Autowired
    private InspectStandardMapper mapper;

    @Autowired
    private InspectStandardItemMapper inspectStandardItemMapper;

    @Autowired
    private SerialNumberService serialNumberService;

    @Autowired
    private FileMapper fileMapper;

    @Autowired
    private IDeviceBomService deviceBomService;

    @Autowired
    private IInspectStandardModuleService inspectStandardModuleService;

    public Result<InspectStandardEntity> create(Long tenantId, Integer idType, InspectStandardEntity entity) {
        entity.setTenantId(tenantId);
        entity.setIdType(idType);
        String standardName = entity.getStandardName();
        if(standardName == null){
            return Result.error("标准名称不能为空");
        }
        if(standardName.length() > 128){
            return Result.error("标准名称长度不能超过128");
        }
        // 标准名不能重复
        List<InspectStandardEntity> sameNameList = mapper.listByName(tenantId, idType, standardName);
        if(sameNameList != null && sameNameList.size() > 0){
            return Result.error("创建失败，标准名已存在");
        }
        LocalDate today = LocalDate.now();
        String serialNumber = serialNumberService.getNext(tenantId, today, InspectStandard.serialNumber);
        entity.setStandardNumber(serialNumber);
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    public Result<Page<InspectStandardVo>> getPage(Long tenantId,  Integer idType, InspectStandardDto dto, Page<InspectStandardEntity> page) {
        dto.setTenantId(tenantId);
        dto.setIdType(idType);
        Page<InspectStandardVo> list = mapper.pageStandardByParam(page, dto);
        // 统计项目数
        List<InspectStandardVo> voRecords = list.getRecords();
        if(voRecords != null && voRecords.size() > 0){
            List<Long> ids = voRecords.stream().map(InspectStandardEntity::getId).collect(Collectors.toList());
            Map<String, CountVo> countMap = inspectStandardItemMapper.countByStandardIds(ids);
            voRecords.forEach(vo -> {
                if(countMap != null){
                    CountVo countVo = countMap.get(vo.getId().toString());
                    if(countVo != null){
                        vo.setItemCount(countVo.getValue());
                    }else{
                        vo.setItemCount(0);
                    }
                }else{
                    vo.setItemCount(0);
                }
            });
        }

        Page<InspectStandardVo> voList = new Page<>();
        BeanUtils.copyProperties(list, voList);
        voList.setRecords(voRecords);
        return Result.ok(voList);
    }

    public Result<List<InspectStandardEntity>> list(Long tenantId, InspectStandardEntity entity) {
        List<InspectStandardEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    public Result<Void> update(Long tenantId, Integer idType, InspectStandardEntity entity) {
        entity.setTenantId(null);
        entity.setIdType(null);
        entity.setStandardNumber(null);
        String standardName = entity.getStandardName();
        // 标准名不能重复
        List<InspectStandardEntity> sameNameList = mapper.listByName(tenantId, idType, standardName);
        if(sameNameList != null && sameNameList.size() > 0){
            List<InspectStandardEntity> collect = sameNameList.stream().filter(t -> {
                return !t.getId().equals(entity.getId());
            }).collect(Collectors.toList());
            if(collect != null && collect.size() > 0){
                return Result.error("创建失败，标准名已存在");
            }
        }

        if (mapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    public Result<Void> deleteById(Long tenantId, Long entityId) {
        if (mapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    public Result<InspectStandardEntity> getById(Long tenantId, Long entityId) {
        InspectStandardEntity entity = mapper.selectById(entityId);
        return Result.ok(entity);
    }

    @Transactional
    public Result<Void> copy(Long tenantId, Integer idType, StandardCopyDto dto) {
        Long copyStandardId = dto.getCopyStandardId();
        String standardName = dto.getStandardName();
        if(standardName == null){
            return Result.error("标准名称不能为空");
        }
        if(standardName.length() > 128){
            return Result.error("标准名称长度不能超过128");
        }
        InspectStandardEntity copyStandard = mapper.selectById(copyStandardId);
        if(copyStandard == null){
            return Result.error("复制失败，标准不存在");
        }

        // 标准名不能重复
        List<InspectStandardEntity> sameNameList = mapper.listByName(tenantId, idType, standardName);
        if(sameNameList != null && sameNameList.size() > 0){
            return Result.error("创建失败，标准名已存在");
        }

        // 复制主表
        InspectStandardEntity newStandard = new InspectStandardEntity();

        LocalDate today = LocalDate.now();
        String serialNumber = serialNumberService.getNext(tenantId, today, InspectStandard.serialNumber);
        newStandard.setStandardNumber(serialNumber);

        newStandard.setTenantId(tenantId);
        newStandard.setStandardName(standardName);
        newStandard.setRequireDesc(dto.getRequireDesc());
        newStandard.setIdType(idType);
        newStandard.setType(copyStandard.getType());
        newStandard.setDeviceTypeId(copyStandard.getDeviceTypeId());
        newStandard.setSuitDeviceType(copyStandard.getSuitDeviceType());
        mapper.insert(newStandard);

        // 复制子表
        List<InspectStandardItemEntity> items = inspectStandardItemMapper.listByStandardId(copyStandardId);
        if(items != null && items.size() > 0){
            items.forEach(item -> {
                InspectStandardItemEntity newItem = new InspectStandardItemEntity();
                
                newItem.setTenantId(tenantId);
                newItem.setInspectStandardId(newStandard.getId());
                newItem.setItemName(item.getItemName());
                newItem.setStandardDesc(item.getStandardDesc());
                newItem.setInspectMethod(item.getInspectMethod());
                newItem.setRecordType(item.getRecordType());
                newItem.setOptions(item.getOptions());
                newItem.setNormalOption(item.getNormalOption());
                newItem.setErrorOption(item.getErrorOption());
                newItem.setMaxValue(item.getMaxValue());
                newItem.setMinValue(item.getMinValue());
                newItem.setRequiredInspect(item.getRequiredInspect());
                newItem.setRequiredPicture(item.getRequiredPicture());
                newItem.setFullBomName(item.getFullBomName());

                inspectStandardItemMapper.insert(newItem);

                // 复制图片
                List<FileEntity> files = fileMapper.listByBizId(
                    FileBizTypeEnum.INSPECT_STANDARD_ITEM_PICTURE.getValue(), 
                    item.getId()
                );
                if(files != null && files.size() > 0){
                    files.forEach(file -> {
                        FileEntity newFile = new FileEntity();
                        newFile.setBizType(FileBizTypeEnum.INSPECT_STANDARD_ITEM_PICTURE.getValue());
                        newFile.setBizId(item.getId());
                        newFile.setTenantId(tenantId);

                        newFile.setFileType(file.getFileType());
                        newFile.setFileName(file.getFileName());
                        newFile.setFileDesc(file.getFileDesc());
                        newFile.setFileSuffix(file.getFileSuffix());
                        newFile.setPath(file.getPath());
                        newFile.setUrl(file.getUrl());
                        newFile.setFileSize(file.getFileSize());
                        newFile.setBizType(file.getBizType());
                        newFile.setBizId(file.getBizId());
                        newFile.setUploadTime(file.getUploadTime());
                        newFile.setUploader(file.getUploader());
                        newFile.setIdType(file.getIdType());

                        fileMapper.insert(newFile);
                    });
                }
            });
        }

        return Result.ok();
    }

    public void exportStandard(Long tenantId, Long standardId, HttpServletResponse response) throws IOException {
        InspectStandardEntity standard = mapper.selectById(standardId);
        if(standard == null){
            throw new BizException("导出失败，标准不存在");
        }
        String standardName = standard.getStandardName();

        List<InspectStandardItemVo> items = inspectStandardItemMapper.listVoByStandardId(standardId);
        if(items != null){
            items.forEach(t -> {
                StandardRecordTypeEnum recordType = StandardRecordTypeEnum.typeOfValue(t.getRecordType());
                if(recordType != null){
                    t.setRecordTypeStr(recordType.getNameDesc());
                }
            });
        }
        
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = standardName + ".xlsx";
        String encodeFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + encodeFileName
                + ";filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));
        
        EasyExcel.write(response.getOutputStream(), InspectStandardItemVo.class)
            .sheet(standardName)
            .doWrite(items);
        
    }

    private static final String KEY_SPLITER = "____";

    @Transactional
    public Result<List<ImportFailInfo>> overrideStandard(
        Long tenantId, Integer idType, StandardOverrideDto dto, MultipartFile file
    ) throws IOException {
        Long standardId = dto.getStandardId();
        InspectStandardEntity entity = mapper.selectById(standardId);
        if(entity == null){
            return Result.error("检查标准不存在");
        }
        
        List<InspectStandardItemEntity> items = inspectStandardItemMapper.listByStandardId(standardId);
        Map<String, InspectStandardItemEntity> itemMap = new HashMap<>();
        items.stream().forEach(t -> {
            String fullBomName = t.getFullBomName();
            if(fullBomName != null){
                fullBomName = fullBomName.trim();
                if("".equals(fullBomName)){
                    fullBomName = null;
                }
            }
            itemMap.put(fullBomName + KEY_SPLITER + t.getItemName(), t);
        });

        List<ImportFailInfo> resultList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), InspectStandardItemVo.class, new ReadListener<InspectStandardItemVo>() {

            @Override
            public void invoke(InspectStandardItemVo record, AnalysisContext context) {
                record.setInspectStandardId(standardId);
                
                String fullBomName = record.getFullBomName();
                if(fullBomName == null || "".equals(fullBomName)){
                    resultList.add(new ImportFailInfo(fullBomName, "模块名称不能为空", "模块名称不能为空"));
                    return;
                }
                fullBomName = fullBomName.trim();
                record.setFullBomName(fullBomName);
                
                TenantIsolation tenant = new TenantIsolation();
                tenant.setTenantId(tenantId);
                tenant.setIdType(idType);
                if(StandardTypeEnum.SPECIAL.getValue().equals(entity.getType())){
                    DeviceBomEntity deviceBomEntity = deviceBomService.getBomByFullName(tenant, entity.getDeviceTypeId(), fullBomName);
                    if(deviceBomEntity == null){
                        resultList.add(new ImportFailInfo(fullBomName, "未找到匹配的部件", "未找到匹配的部件"));
                        return;
                    }
                } else if(StandardTypeEnum.COMMON.getValue().equals(entity.getType())) {
                    Long moduleId = inspectStandardModuleService.getModuleIdByModuleName(tenantId, standardId, fullBomName);
                    record.setInspectStandardModuleId(moduleId);
                }
                if(record.getItemName() == null || "".equals(record.getItemName())){
                    resultList.add(new ImportFailInfo(fullBomName, "项目名称不能为空", "项目名称不能为空"));
                    return;
                }
                if(record.getRecordTypeStr() == null || "".equals(record.getRecordTypeStr())){
                    resultList.add(new ImportFailInfo(fullBomName, "记录类型不能为空", "记录类型不能为空"));
                    return;
                }
                StandardRecordTypeEnum recordType = StandardRecordTypeEnum.typeOfNameDesc(record.getRecordTypeStr());
                if(recordType == null){
                    resultList.add(new ImportFailInfo(fullBomName, "记录类型错误", "记录类型错误"));
                    return;
                }
                record.setRecordType(recordType.getValue());

                String itemName = record.getItemName();
                String key = fullBomName + KEY_SPLITER + itemName;
                InspectStandardItemEntity item = itemMap.get(key);
                if(item != null){
                    // 覆盖
                    try {
                        record.setId(item.getId());
                        inspectStandardItemMapper.updateById(record);
                    } catch (Exception e) {
                        resultList.add(new ImportFailInfo(fullBomName, "更新标准项异常", e.getMessage()));
                    }
                }else{
                    // 插入
                    try {
                        inspectStandardItemMapper.insert(record);
                    } catch (Exception e) {
                        resultList.add(new ImportFailInfo(fullBomName, "插入标准项异常", e.getMessage()));
                    }
                }
            }
        
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("override finish");
            }

            @Override
            public  void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                Set<String> expectedHeaders = new HashSet<>();
                expectedHeaders.add("模块名称");
                expectedHeaders.add("项目名称");
                expectedHeaders.add("备注");
                expectedHeaders.add("记录类型");
                expectedHeaders.add("正常选项");
                expectedHeaders.add("异常选项");
                expectedHeaders.add("数值上限");
                expectedHeaders.add("数值下限");
                Set<String> headers = headMap.values().stream().map(ReadCellData::getStringValue).collect(Collectors.toSet());
                if(!expectedHeaders.equals(headers)){
                    throw new BizException("导入失败，请使用正确的模板导入");
                }
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) {
                if (exception instanceof ExcelDataConvertException) {
                    // 数据转换异常，可以选择记录日志或忽略
                    log.error("Error at row " + context.readRowHolder().getRowIndex() + ": " + exception.getMessage());
                    resultList.add(new ImportFailInfo("第" + context.readRowHolder().getRowIndex() + "行异常", "数据转换异常", exception.getMessage()));
                } else if (exception instanceof BizException) {
                    throw new RuntimeException(exception.getMessage());
                } else {
                    resultList.add(new ImportFailInfo("导入异常", "其他异常", exception.getMessage()));
                }
            }
            
        }).sheet().doRead();
        return Result.ok(resultList);
    }

    @Transactional
    public Result<List<ImportFailInfo>> importStandard(Long tenantId, Integer idType, StandardImportDto dto, MultipartFile file) throws IOException {
        
        InspectStandardEntity entity = new InspectStandardEntity();
        entity.setStandardName(dto.getStandardName());
        entity.setType(dto.getType());
        entity.setDeviceTypeId(dto.getDeviceTypeId());

        Result<InspectStandardEntity> createResult = create(tenantId, idType, entity);
        if(!createResult.getSignal()){
            return Result.error(createResult.getMessage());
        }
        Long standardId = entity.getId();
        List<ImportFailInfo> resultList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), InspectStandardItemVo.class, new ReadListener<InspectStandardItemVo>() {

            @Override
            public void invoke(InspectStandardItemVo record, AnalysisContext context) {
                record.setInspectStandardId(standardId);
                
                String fullBomName = record.getFullBomName();
                if(fullBomName == null || "".equals(fullBomName)){
                    resultList.add(new ImportFailInfo(fullBomName, "模块名称不能为空", "模块名称不能为空"));
                    return;
                }
                fullBomName = fullBomName.trim();
                record.setFullBomName(fullBomName);
                
                TenantIsolation tenant = new TenantIsolation();
                tenant.setTenantId(tenantId);
                tenant.setIdType(idType);
                if(StandardTypeEnum.SPECIAL.getValue().equals(dto.getType())){
                    DeviceBomEntity deviceBomEntity = deviceBomService.getBomByFullName(tenant, dto.getDeviceTypeId(), fullBomName);
                    if(deviceBomEntity == null){
                        resultList.add(new ImportFailInfo(fullBomName, "未找到匹配的部件", "未找到匹配的部件"));
                        return;
                    }
                } else if(StandardTypeEnum.COMMON.getValue().equals(dto.getType())) {
                    Long moduleId = inspectStandardModuleService.getModuleIdByModuleName(tenantId, standardId, fullBomName);
                    record.setInspectStandardModuleId(moduleId);
                }
                if(record.getItemName() == null || "".equals(record.getItemName())){
                    resultList.add(new ImportFailInfo(fullBomName, "项目名称不能为空", "项目名称不能为空"));
                    return;
                }
                if(record.getRecordTypeStr() == null || "".equals(record.getRecordTypeStr())){
                    resultList.add(new ImportFailInfo(fullBomName, "记录类型不能为空", "记录类型不能为空"));
                    return;
                }
                StandardRecordTypeEnum recordType = StandardRecordTypeEnum.typeOfNameDesc(record.getRecordTypeStr());
                if(recordType == null){
                    resultList.add(new ImportFailInfo(fullBomName, "记录类型错误", "记录类型错误"));
                    return;
                }
                record.setRecordType(recordType.getValue());
                try {
                    inspectStandardItemMapper.insert(record);
                } catch (Exception e) {
                    resultList.add(new ImportFailInfo(fullBomName, "插入标准项异常", e.getMessage()));
                }
            }
        
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("import finish");
            }

            @Override
            public  void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                Set<String> expectedHeaders = new HashSet<>();
                expectedHeaders.add("模块名称");
                expectedHeaders.add("项目名称");
                expectedHeaders.add("备注");
                expectedHeaders.add("记录类型");
                expectedHeaders.add("正常选项");
                expectedHeaders.add("异常选项");
                expectedHeaders.add("数值上限");
                expectedHeaders.add("数值下限");
                Set<String> headers = headMap.values().stream().map(ReadCellData::getStringValue).collect(Collectors.toSet());
                if(!expectedHeaders.equals(headers)){
                    throw new BizException("导入失败，请使用正确的模板导入");
                }
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) {
                if (exception instanceof ExcelDataConvertException) {
                    // 数据转换异常，可以选择记录日志或忽略
                    log.error("Error at row " + context.readRowHolder().getRowIndex() + ": " + exception.getMessage());
                    resultList.add(new ImportFailInfo("第" + context.readRowHolder().getRowIndex() + "行异常", "数据转换异常", exception.getMessage()));
                } else if (exception instanceof BizException) {
                    throw new RuntimeException(exception.getMessage());
                } else {
                    resultList.add(new ImportFailInfo("导入异常", "其他异常", exception.getMessage()));
                }
            }
            
        }).sheet().doRead();
        return Result.ok(resultList);
    }

    public void downloadTemplate(HttpServletResponse response, Integer type) throws IOException {
        
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "";
        if (StandardTypeEnum.COMMON.getValue().equals(type)) {
            fileName = "巡检标准通用型模版.xlsx";
        } else if (StandardTypeEnum.SPECIAL.getValue().equals(type)) {
            fileName = "巡检标准设备型模版.xlsx";
        }
        String encodeFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + encodeFileName);
        
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("templates/" + fileName);
        StreamUtils.copy(inputStream, response.getOutputStream());

    }

    public void initTenantStandard(Long tenantId) {
        List<InspectStandardEntity> standardList = mapper.selectList(Wrappers.<InspectStandardEntity>lambdaQuery().eq(InspectStandardEntity::getTenantId, tenantId));
        if (CollectionUtil.isEmpty(standardList)) {
            InspectStandardEntity entity = new InspectStandardEntity();
            Long id = IdGenerator.generateId();
            entity.setId(id);
            entity.setStandardName("堆垛机巡检标准模板（案例）");
            String standardNumber = serialNumberService.getNext(tenantId, LocalDate.now(), InspectStandard.serialNumber);
            entity.setStandardNumber(standardNumber);
            entity.setType(StandardTypeEnum.COMMON.getValue());
            entity.setIdType(IdTypeEnum.CUSTOMER.getValue());
            entity.setDeviceTypeId(0L);
            entity.setCreator("admin");
            entity.setUpdator("admin");
            if (mapper.insert(entity) == 1) {
                inspectStandardItemMapper.initTenantStandardItem(tenantId, id);
            }
        }
    }

}
