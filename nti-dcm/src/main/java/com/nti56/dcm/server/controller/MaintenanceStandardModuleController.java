package com.nti56.dcm.server.controller;

import com.nti56.dcm.server.entity.MaintenanceStandardModuleEntity;
import com.nti56.dcm.server.service.IMaintenanceStandardModuleService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 保养标准模块表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-07-04 09:39:39
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/maintenanceStandardModule")
@Tag(name = "保养标准模块表模块")
public class MaintenanceStandardModuleController {

    @Autowired
    IMaintenanceStandardModuleService service;

    @GetMapping("listByStandardId/{standardId}")
    @Operation(summary = "获取列表" )
    public R listByStandardId(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long standardId){
        Result<List<MaintenanceStandardModuleEntity>> result = service.listByStandardId(tenantIsolation, standardId);
        return R.result(result);
    }

    @PostMapping("create")
    @Operation(summary ="创建对象")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody MaintenanceStandardModuleEntity entity){
        return R.result(service.create(tenantIsolation, entity));
    }

    @PutMapping("update")
    @Operation(summary ="更新")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody MaintenanceStandardModuleEntity entity){
        Result<Void> result = service.update(tenantIsolation, entity);
        return R.result(result);
    }

    @DeleteMapping("delete/{entityId}")
    @Operation(summary ="删除对象")
    public R delete(@PathVariable Long entityId){
        Result<Void> result = service.deleteById(entityId);
        return R.result(result);
    }
    
}
