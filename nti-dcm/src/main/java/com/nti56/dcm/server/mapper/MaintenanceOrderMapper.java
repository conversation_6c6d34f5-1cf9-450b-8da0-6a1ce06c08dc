package com.nti56.dcm.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.DeviceRepairEntity;
import com.nti56.dcm.server.entity.MaintenanceOrderEntity;
import com.nti56.dcm.server.model.dto.AuthDto;
import com.nti56.dcm.server.model.dto.QueryMaintenanceOrderDto;
import com.nti56.dcm.server.model.vo.MaintenanceOrderDetailVo;
import com.nti56.dcm.server.model.vo.MaintenanceOrderVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 保养工单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01 16:28:19
 */
public interface MaintenanceOrderMapper extends BaseMapper<MaintenanceOrderEntity> {

    Page<MaintenanceOrderVo> pageMaintenanceOrder(@Param("dto") QueryMaintenanceOrderDto dto,
                                                  @Param("page") Page<MaintenanceOrderVo> page);

    List<MaintenanceOrderVo> listMaintenanceOrder(@Param("dto") QueryMaintenanceOrderDto dto);

    Page<MaintenanceOrderVo> pageWaitProcessOrderByAuth(@Param("executeUserId") Long executeUserId,
                                                        @Param("dto") QueryMaintenanceOrderDto dto,
                                                        @Param("page") Page<MaintenanceOrderVo> page);

    Page<MaintenanceOrderVo> pageProcessOrder(@Param("dto") QueryMaintenanceOrderDto dto,
                                              @Param("page") Page<MaintenanceOrderVo> page,
                                              @Param("currentUserId") Long currentUserId);

    Page<MaintenanceOrderVo> pageSupplierReceiveOrder(@Param("dto") QueryMaintenanceOrderDto dto,
                                                  @Param("page") Page<MaintenanceOrderVo> page);

    MaintenanceOrderDetailVo getOrderDetailVo(@Param("id") Long id);

    List<MaintenanceOrderEntity> listOrderByDate(@Param("tenantId") Long tenantId, @Param("idType") Integer idType,
                                                 @Param("begin") LocalDateTime begin, @Param("end") LocalDateTime end,
                                                 @Param("customerIds") List<Long> customerIds);

    Page<MaintenanceOrderVo> pageOrderByDevice(@Param("dto") QueryMaintenanceOrderDto dto,
                                                  @Param("page") Page<MaintenanceOrderVo> page);

    void timeoutBatchById(@Param("idList") List<Long> idList, @Param("now") LocalDateTime now);

    @Update("UPDATE maintenance_order SET timeout = 2, updator_id = 1, updator = 'admin', update_time = now() WHERE id = #{orderId}")
    Integer orderTimeout(@Param("orderId") Long orderId);

    @Update("UPDATE maintenance_order SET process_instance_id = #{processInstanceId} WHERE id = #{maintenanceOrderId}")
    Integer updateProcessInstanceIdById(@Param("maintenanceOrderId") Long maintenanceOrderId, @Param("processInstanceId") String processInstanceId);

    @Select("SELECT count(*) FROM maintenance_order WHERE maintenance_plan_id = #{maintenancePlanId} AND deleted = 0 AND status IN (1,2,3,4)")
    Integer countUnfinishOrderByPlanId(@Param("maintenancePlanId")Long maintenancePlanId);

    @Update("UPDATE maintenance_order SET support_supplier_id = #{supportSupplierId} WHERE id = #{id}")
    Integer updateSupportSupplierIdById(@Param("id") Long id, @Param("supportSupplierId") Long supportSupplierId);
    List<MaintenanceOrderEntity> listEndMaintenance(@Param("startDate")LocalDateTime startDate, @Param("endDate")LocalDateTime endDate,@Param("tenantId") Long tenantId);
}
