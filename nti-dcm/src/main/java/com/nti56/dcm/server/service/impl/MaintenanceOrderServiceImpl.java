package com.nti56.dcm.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.Lists;
import com.google.common.collect.Multiset;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.MaintenanceOrder;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.*;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.mapper.*;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.*;
import com.nti56.dcm.server.service.*;
import com.nti56.flowable.common.dto.R;
import com.nti56.flowable.common.dto.TaskDto;
import com.nti56.flowable.common.dto.flow.SelectValue;
import com.nti56.flowable.common.service.biz.IRemoteService;
import com.nti56.flowable.common.utils.JsonUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.nti56.flowable.common.constants.ProcessInstanceConstant.VariableKey.AUTO_DISPATCH_FLAG;

/**
 * <p>
 * 保养工单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 16:28:19
 * @since JDK 1.8
 */
@Slf4j
@Service
public class MaintenanceOrderServiceImpl extends ServiceImpl<MaintenanceOrderMapper, MaintenanceOrderEntity>
        implements MaintenanceOrderService {

    @Autowired
    private MaintenanceItemService maintenanceItemService;

    @Autowired
    private MaintenanceOrderProgressService maintenanceOrderProgressService;

    @Autowired
    private MaintenanceStandardItemService maintenanceStandardItemService;

    @Autowired
    private UserRoleAuthService userRoleAuthService;

    @Autowired
    private DeviceEmpowerService deviceEmpowerService;

    @Autowired
    private MaintenanceStandardService maintenanceStandardService;

    @Autowired
    private MaintenanceOrderMapper mapper;

    @Autowired
    private FileMapper fileMapper;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private MaintenanceResultMapper maintenanceResultMapper;

    @Autowired
    private MaintenanceResultItemMapper maintenanceResultItemMapper;

    @Autowired
    private MaintenanceResultStandardMapper maintenanceResultStandardMapper;

    @Autowired
    private MaintenanceStandardItemMapper maintenanceStandardItemMapper;

    @Autowired
    private MaintenanceOrderProgressMapper maintenanceOrderProgressMapper;

    @Autowired
    private TenantInfoService tenantInfoService;

    @Autowired
    private WorkloadMapper workloadMapper;

    @Autowired
    private TimeoutConfigService timeoutConfigService;

    @Autowired
    private IRemoteService remoteService;

    @Autowired
    private IMessageService messageService;

    @Autowired
    private OrderProcessService orderProcessService;

    @Autowired
    private AlarmRecordService alarmRecordService;

    @Autowired
    private SerialNumberService serialNumberService;

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private AsyncSendNoticeService asyncSendNoticeService;

    @Autowired
    private MessageNoticeStrategyMapper messageNoticeStrategyMapper;

    @Autowired
    private AlarmRecordMapper alarmRecordMapper;

    @Autowired
    private RuntimeService runtimeService;

    private static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private void saveMaintenanceItem(MaintenanceOrderEntity maintenanceOrder) {
        if (StringUtils.isNotEmpty(maintenanceOrder.getMaintenanceStandardId())) {
            String[] standardIds = maintenanceOrder.getMaintenanceStandardId().split(",");
            List<MaintenanceStandardItemEntity> standardItemList = maintenanceStandardItemService.list(Wrappers.<MaintenanceStandardItemEntity>lambdaQuery()
                    .in(MaintenanceStandardItemEntity::getMaintenanceStandardId, standardIds));
            List<MaintenanceItemEntity> maintenanceItemList = Lists.newLinkedList();
            standardItemList.forEach(item -> {
                MaintenanceItemEntity maintenanceItem = new MaintenanceItemEntity();
                maintenanceItem.setId(IdGenerator.generateId());
                maintenanceItem.setMaintenanceOrderId(maintenanceOrder.getId());
                maintenanceItem.setPosition(item.getPosition());
                maintenanceItem.setStandardDesc(item.getStandardDesc());
                maintenanceItem.setStatus(CompleteEnum.NO.getValue());
                maintenanceItem.setTenantId(maintenanceOrder.getTenantId());
                maintenanceItemList.add(maintenanceItem);
            });
            maintenanceItemService.saveBatch(maintenanceItemList);
        }
    }

    private void saveMaintenanceResult(MaintenanceOrderEntity maintenanceOrder, List<OrderDeviceDto> orderDeviceList) {
        // 复制标准，创建工单结果
        // 先查出计划涉及的设备和标准
        if(orderDeviceList != null){
            // 每个设备建一个工单结果
            orderDeviceList.forEach(orderDevice -> {
                MaintenanceResultEntity maintenanceResultEntity = new MaintenanceResultEntity();
                maintenanceResultEntity.setTenantId(maintenanceOrder.getTenantId());
                maintenanceResultEntity.setMaintenanceOrderId(maintenanceOrder.getId());
                maintenanceResultEntity.setDeviceId(orderDevice.getDeviceId());
                maintenanceResultEntity.setStatus(MaintenanceResultStatusEnum.NOT_MAINTENANCE.getValue());
                maintenanceResultMapper.insert(maintenanceResultEntity);
                Long resultId = maintenanceResultEntity.getId();
                // 每个设备对应多个标准
                List<IdNameDto> standardList = orderDevice.getStandardList();
                if(standardList == null){
                    return;
                }
                standardList.forEach(standard -> {
                    MaintenanceResultStandardEntity resultStandardEntity = new MaintenanceResultStandardEntity();
                    resultStandardEntity.setTenantId(maintenanceOrder.getTenantId());
                    resultStandardEntity.setMaintenanceResultId(resultId);
                    resultStandardEntity.setMaintenanceStandardId(standard.getId());
                    maintenanceResultStandardMapper.insert(resultStandardEntity);

                    // 根据标准创建工单结果项
                    List<MaintenanceStandardItemEntity> standardItemList = maintenanceStandardItemMapper.listByStandardId(standard.getId());
                    if(standardItemList == null){
                        return;
                    }
                    standardItemList.forEach(standardItem -> {
                        MaintenanceResultItemEntity resultItemEntity = new MaintenanceResultItemEntity();
                        resultItemEntity.setTenantId(maintenanceOrder.getTenantId());
                        resultItemEntity.setMaintenanceResultId(resultId);
                        resultItemEntity.setMaintenanceStandardItemId(standardItem.getId());
                        maintenanceResultItemMapper.insert(resultItemEntity);
                    });
                });
            });
        }
    }

    @Override
    @Transactional
    public Result save(TenantIsolation tenantIsolation, MaintenanceOrderDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        //委外工单 先判断设备是否授权
        if (OutsourceEnum.YES.getValue().equals(dto.getOutsource())) {
            if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                if (ObjectUtil.isNull(dto.getSupplierId()) || StringUtils.isEmpty(dto.getSupplierName())) {
                    return Result.error("委外创建保养工单时，请传入供应商id和名称");
                }
//            boolean haveDeviceAuth = deviceEmpowerService.haveDeviceAuth(tenantIsolation.getTenantId(), dto.getSupplierId(), dto.getDeviceId());
//            if (!haveDeviceAuth) {
//                return Result.error("设备未授权给此供应商:" + dto.getSupplierName() + "，请先进行授权");
//            }
            }
            if (IdTypeEnum.SUPPLIER.getValue().equals(idType) && !ObjectUtil.equals(YesNoEnum.YES.getValue(),dto.getAggregatorOutsource())  ) {
                if (ObjectUtil.isNull(dto.getCustomerId()) || StringUtils.isEmpty(dto.getCustomerName())) {
                    return Result.error("委外创建保养工单时，请传入客户id和名称!");
                }
            }
        }

        Result<String> tenantNameResult = userCenterService.getTenantNameById(tenantId);
        if(!tenantNameResult.getSignal()){
            return Result.error(tenantNameResult.getMessage());
        }
        String tenantName = tenantNameResult.getResult();

        MaintenanceOrderEntity maintenanceOrder = BeanUtilsIntensifier.copyBean(dto, MaintenanceOrderEntity.class);
        long orderId = IdGenerator.generateId();
        maintenanceOrder.setId(orderId);
        String orderNumber = serialNumberService.getNext(tenantId, LocalDate.now(), MaintenanceOrder.serialNumber);
        maintenanceOrder.setOrderNumber(orderNumber);
        maintenanceOrder.setMaintenanceMode(MaintenanceModeEnum.ONCE.getValue());
        maintenanceOrder.setStatus(OrderStatusEnum.WAIT_DISPATCH.getValue());
        maintenanceOrder.setOverdue(OverdueEnum.NO.getValue());
        maintenanceOrder.setTimeout(TimeoutEnum.NORMAL.getValue());
        Integer outsource = maintenanceOrder.getOutsource();
        maintenanceOrder.setOutsource(outsource);
        maintenanceOrder.setCreateSource(CreateSourceEnum.CUSTOMER.getValue());

        boolean isDataRecord = false;
        if(OutsourceEnum.YES.getValue().equals(outsource)){
            maintenanceOrder.setSupplierId(maintenanceOrder.getSupplierId());
            maintenanceOrder.setSupplierName(maintenanceOrder.getSupplierName());

            if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                maintenanceOrder.setCreateSource(CreateSourceEnum.CUSTOMER.getValue());
                //客户为供应商创建工单
                Result<Integer> result = customerRelationService.getTenantType(tenantId, maintenanceOrder.getSupplierId(), idType);
                if (result.getSignal()) {
                    Integer type = result.getResult();
                    if (type != null && SupplierTypeEnum.SELF_DEFINING.getValue().equals(type)) {
                        isDataRecord = true;
                    }

                    maintenanceOrder.setOutsourceSupplierType(type);
                    maintenanceOrder.setOutsourceCustomerType(SupplierTypeEnum.TENANT.getValue());
                }
            } else if (IdTypeEnum.SUPPLIER.getValue().equals(idType)) {
                maintenanceOrder.setCreateSource(CreateSourceEnum.SUPPLIER.getValue());
                //供应商为客户创建工单
                Result<Integer> result = customerRelationService.getTenantType(dto.getCustomerId(), tenantId, idType);
                if (result.getSignal()) {
                    Integer type = result.getResult();
                    if (type != null && SupplierTypeEnum.SELF_DEFINING.getValue().equals(type)) {
                        isDataRecord = true;
                    }

                    maintenanceOrder.setOutsourceCustomerType(type);
                    maintenanceOrder.setOutsourceSupplierType(SupplierTypeEnum.TENANT.getValue());
                }
            }
        }
        maintenanceOrder.setTenantId(tenantId);
        maintenanceOrder.setCustomerName(tenantName);
        maintenanceOrder.setPlanMaintenanceTime(dto.getPlanMaintenanceTime());

        //供应商/集成商为客户创建工单   如果不是集成商委外给供应商 才进行身份对调
        if (!ObjectUtil.equals(dto.getAggregatorOutsource(), YesNoEnum.YES.getValue()) && IdTypeEnum.SUPPLIER.getValue().equals(dto.getCreateSource())) {
            // 供应商创建外委工单，查询客户id是否是pmo租户，如果是，需要查询客户所属pmo租户的实际租户id
            TenantInfoEntity tenantInfo = tenantInfoService.getByPmoTenantId( dto.getCustomerId());
            Long pmoSourceTenantId = Optional.ofNullable(tenantInfo).map(BaseEntity::getTenantId).orElse(null);

            maintenanceOrder.setTenantId(pmoSourceTenantId!=null?pmoSourceTenantId:dto.getCustomerId());
            maintenanceOrder.setCustomerName(dto.getCustomerName());
            maintenanceOrder.setSupplierId(tenantIsolation.getTenantId());
            maintenanceOrder.setSupplierName(tenantName);
        }

        this.save(maintenanceOrder);

        // 启动工单流程
        Result<StartFlowDto> startFlowResult = null;
        try {
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                if (isDataRecord) {
                    startFlowResult = orderProcessService.startFlow(
                            tenantId,
                            idType,
                            OrderTypeEnum.MAINTENANCE.getValue(),
                            OutsourceEnum.YES.getValue().equals(outsource),
                            isDataRecord,
                            orderId,
                            ResponsibleDto.builder().id(JwtUserInfoUtils.getUserId()).name(JwtUserInfoUtils.getUserName()).build()
                    );
                } else {
                    startFlowResult = orderProcessService.startFlow(
                            maintenanceOrder.getTenantId(),
                            OrderTypeEnum.MAINTENANCE.getValue(),
                            orderId,
                            ResponsibleDto.builder().id(JwtUserInfoUtils.getUserId()).name(JwtUserInfoUtils.getUserName()).build(),
                            maintenanceOrder.getSupplierId(),
                            isDataRecord,
                            dto.getAggregatorOutsource()
                    );
                }
            }else{
                startFlowResult = orderProcessService.startFlow(
                        tenantId,
                        idType,
                        OrderTypeEnum.MAINTENANCE.getValue(),
                        OutsourceEnum.YES.getValue().equals(outsource),
                        isDataRecord,
                        orderId,
                        ResponsibleDto.builder().id(JwtUserInfoUtils.getUserId()).name(JwtUserInfoUtils.getUserName()).build()
                );
            }
        } catch (Exception e) {
            throw new RuntimeException("调用启动流程异常", e);
        }

        if(!startFlowResult.getSignal()){
            throw new BizException(startFlowResult.getMessage());
        }
        String processInstanceId = startFlowResult.getResult().getProcessInstanceId();
//        Integer count = mapper.updateProcessInstanceIdById(orderId, processInstanceId);
        maintenanceOrder.setProcessInstanceId(processInstanceId);
        if (startFlowResult.getResult().getIsAutoDispatch()){
            maintenanceOrder.setStatus(OrderStatusEnum.WAIT_RECEIVE.getValue());
        }
        mapper.updateById(maintenanceOrder);
        //保存工单结果
        saveMaintenanceResult(maintenanceOrder, dto.getDeviceList());

        // 如果是告警记录创建的工单，需要更新告警记录的工单id
        if (Objects.nonNull(dto.getAlarmRecordId())) {
            alarmRecordMapper.bindOrder(dto.getAlarmRecordId(), maintenanceOrder.getId(), maintenanceOrder.getOrderNumber(),
                    OrderTypeEnum.MAINTENANCE.getValue(), startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE.getValue():OrderStatusEnum.WAIT_DISPATCH.getValue());
        }

        // 发送通知
        asyncSendNoticeService.sendOrderNoticeSingle(processInstanceId, maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantId, idType, OrderTypeEnum.MAINTENANCE,startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE: OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);

        return Result.ok();
    }

    private List<String> listOrderOperations(MaintenanceOrderEntity orderEntity,
                                             Integer idType,
                                             boolean todo,
                                             boolean canUndo,
                                             boolean canForward,
                                             Long userId,
                                             Boolean isSuperRole,
                                             Map<String, Boolean> acceptMap){
        List<String> operations = new ArrayList<>();
        // 状态对应按钮
        Integer status = orderEntity.getStatus();

        Boolean isOutsource = OutsourceEnum.YES.getValue().equals(orderEntity.getOutsource());
        Boolean isCustomer = IdTypeEnum.CUSTOMER.getValue().equals(idType);

        OrderStatusEnum statusEnum = OrderStatusEnum.typeOfValue(status);

        switch (statusEnum) {
            case WAIT_DISPATCH:{
                // 管理员权限
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(isDataRecord) {
                                operations.add(OperationEnum.DISPATCH.getName());
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                if (IdTypeEnum.CUSTOMER.getValue().equals(orderEntity.getCreateSource())){
                                    operations.add(OperationEnum.REVOKE.getName());
                                }
                                break;
                            }
                        } else {
                            operations.add(OperationEnum.DISPATCH.getName());
                            if (IdTypeEnum.SUPPLIER.getValue().equals(orderEntity.getCreateSource())){
                                operations.add(OperationEnum.REVOKE.getName());
                            }
                            break;
                        }
                    } else {
                        // 内部工单
                        operations.add(OperationEnum.DISPATCH.getName());
                        operations.add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                // 待分派状态，显示分派按钮
                if (todo) {
                    operations.add(OperationEnum.DISPATCH.getName());
                }
                if (userId.equals(orderEntity.getCreatorId())) {
                    operations.add(OperationEnum.REVOKE.getName());
                }
                break;
            }
            case WAIT_RECEIVE:{
                // 管理员权限
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(isDataRecord) {
                                operations.add(OperationEnum.RECEIVE.getName());
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                if (IdTypeEnum.CUSTOMER.getValue().equals(orderEntity.getCreateSource())){
                                    operations.add(OperationEnum.REVOKE.getName());
                                }
                                break;
                            }
                        } else {
                            operations.add(OperationEnum.RECEIVE.getName());
                            if (IdTypeEnum.SUPPLIER.getValue().equals(orderEntity.getCreateSource())){
                                operations.add(OperationEnum.REVOKE.getName());
                            }
                            break;
                        }
                    } else {
                        // 内部工单
                        operations.add(OperationEnum.RECEIVE.getName());
                        operations.add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                // 待分派状态，显示分派按钮
                if (todo) {
                    operations.add(OperationEnum.RECEIVE.getName());
                }
                if (userId.equals(orderEntity.getCreatorId())) {
                    operations.add(OperationEnum.REVOKE.getName());
                }
                break;
            }
            case EXECUTING:{
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(!isDataRecord) {
                                // 当前是客户&&委外&&非数据型
                                break;
                            }
                        }
                    }
                    operations.add(OperationEnum.EXECUTE.getName());
                    operations.add(OperationEnum.FORWARD.getName());
                    operations.add(OperationEnum.UNDO_DISPATCH.getName());
                    break;
                }
                // 执行中状态，显示执行按钮
                if (todo) {
                    operations.add(OperationEnum.EXECUTE.getName());
                    if (canForward) {
                        operations.add(OperationEnum.FORWARD.getName());
                    }
                }
                if (canUndo) {
                    operations.add(OperationEnum.UNDO_DISPATCH.getName());
                }
                break;
            }
            case WAIT_ACCEPT:{
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(!isDataRecord) {
                                // 当前是客户&&委外&&非数据型
                                operations.add(OperationEnum.ACCEPT.getName());
                                operations.add(OperationEnum.REJECT.getName());

                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i->i.get(orderEntity.getProcessInstanceId())).orElse(false) ){
                                    operations.clear();
                                }
                                break;
                            }
                        } else {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceCustomerType());
                            if(!isDataRecord) {
                                // 当前是供应商&&委外&&非数据型
                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i->i.get(orderEntity.getProcessInstanceId())).orElse(false) ){
                                    break;
                                }
                            }
                        }
                    }
                    operations.add(OperationEnum.ACCEPT.getName());
                    operations.add(OperationEnum.REJECT.getName());
                    operations.add(OperationEnum.UNDO_EXECUTE.getName());
                    break;
                }
                // 待验收状态，显示验收按钮
                if (todo) {
                    operations.add(OperationEnum.ACCEPT.getName());
                    operations.add(OperationEnum.REJECT.getName());
                }
                if (canUndo) {
                    operations.add(OperationEnum.UNDO_EXECUTE.getName());
                }
                break;
            }
            default:
                break;
        }

        return operations;
    }

    private List<Long> getRelationTenantIds(Integer idType, Long tenantId){
        List<Long> relationIds = new ArrayList<>();
        if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
            relationIds = customerRelationService.list(Wrappers.<CustomerRelationEntity>lambdaQuery()
                    .eq(CustomerRelationEntity::getCustomerId, tenantId)
                    .eq(CustomerRelationEntity::getStatus, 1)
                    .eq(CustomerRelationEntity::getIdType, idType))
                    .stream().map(CustomerRelationEntity::getSupplierId).collect(Collectors.toList());

        } else {
            relationIds = customerRelationService.list(Wrappers.<CustomerRelationEntity>lambdaQuery()
                    .eq(CustomerRelationEntity::getSupplierId, tenantId)
                    .eq(CustomerRelationEntity::getStatus, 1)
                    .eq(CustomerRelationEntity::getIdType, idType))
                    .stream().map(CustomerRelationEntity::getCustomerId).collect(Collectors.toList());;
        }
        return relationIds;
    }

    @Override
    public Result<MaintenanceOrderPage> getPage(TenantIsolation tenantIsolation,
                                                    QueryMaintenanceOrderDto dto,
                                                    Page<MaintenanceOrderVo> page) {
        Integer idType = tenantIsolation.getIdType();
        Long tenantId = tenantIsolation.getTenantId();
        dto.setTenantId(tenantId);
        dto.setIdType(idType);
        Page<MaintenanceOrderVo> voPage = mapper.pageMaintenanceOrder(dto, page);
        if(voPage == null){
            return null;
        }

        Long currentUserId = JwtUserInfoUtils.getUserId();

        List<MaintenanceOrderVo> records = voPage.getRecords();
        if(records != null){
            if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                records.forEach(i->{i.setSupportSupplierId(null);});
            }
            // 查询当前用户所有待办任务信息
            Long userId = JwtUserInfoUtils.getUserId();
            Result<List<TaskDto>> listResult = orderProcessService.queryTodoTaskList(userId);
            if (!listResult.getSignal()) {
                return Result.error(listResult.getMessage());
            }
            List<TaskDto> todoTaskDtoList = listResult.getResult();
            List<String> todoProcessInstanceIdList = todoTaskDtoList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
            // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
            List<String> processInstanceIdList = records.stream().map(i -> i.getProcessInstanceId()).collect(Collectors.toList());
            Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(processInstanceIdList);
            if (!mapResult.getSignal()) {
                return Result.error(mapResult.getMessage());
            }
            Map<String, String> lastProcessMap = mapResult.getResult();
            // 查询转交权限
            Result<Map<String, Boolean>> forwardMapResult = orderProcessService.queryForwardPermissionByProcessInstances(processInstanceIdList);
            if (!forwardMapResult.getSignal()) {
                return Result.error(forwardMapResult.getMessage());
            }
            Map<String, Boolean> forwardMap = forwardMapResult.getResult();

            // 是否是超级角色
            Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
            Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
            if (!haveAuthResult.getSignal()) {
                throw new BizException(haveAuthResult.getMessage());
            }
            Boolean isReportHandleRole = haveAuthResult.getResult();
            Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;

            Result<Map<String, Boolean>> acceptMapResult = orderProcessService.queryAcceptStateByProcessInstances(processInstanceIdList, idType);
            if (!acceptMapResult.getSignal()) {
                throw new BizException(acceptMapResult.getMessage());
            }
            Map<String, Boolean> acceptMap = acceptMapResult.getResult();

            records.forEach(record -> {
                String lastUserId = lastProcessMap.get(record.getProcessInstanceId());

                record.setOperations(listOrderOperations(
                        record,
                        idType,
                        todoProcessInstanceIdList.contains(record.getProcessInstanceId()),
                        ObjectUtil.equals(lastUserId, tenantIsolation.getTenantId() + ":" + currentUserId.toString()),
                        forwardMap.getOrDefault(record.getProcessInstanceId(), false),
                        currentUserId,
                        isSuperRole,
                        acceptMap)
                );
            });
        }

        MaintenanceOrderPage p = new MaintenanceOrderPage();
        p.setRecords(voPage.getRecords());
        p.setSize(voPage.getSize());
        p.setTotal(voPage.getTotal());
        p.setCurrent(voPage.getCurrent());

        dto.setStatusList(null);

        List<MaintenanceOrderVo> voList = mapper.listMaintenanceOrder(dto);
        Multiset<Integer> counter = HashMultiset.create();
        if(voList != null){
            voList.forEach(t -> {
                counter.add(t.getStatus());
            });
        }
        OrderStatusCountVo countVo = new OrderStatusCountVo();
        countVo.setWaitDispatchCount(counter.count(OrderStatusEnum.WAIT_DISPATCH.getValue()));
        countVo.setWaitReceiveCount(counter.count(OrderStatusEnum.WAIT_RECEIVE.getValue()));
        countVo.setExecutingCount(counter.count(OrderStatusEnum.EXECUTING.getValue()));
        countVo.setWaitAcceptCount(counter.count(OrderStatusEnum.WAIT_ACCEPT.getValue()));
        countVo.setAcceptedCount(counter.count(OrderStatusEnum.ACCEPTED.getValue()));
        countVo.setRejectCount(counter.count(OrderStatusEnum.REJECT.getValue()));
        countVo.setRevokeCount(counter.count(OrderStatusEnum.REVOKE.getValue()));
        p.setOrderStatusCount(countVo);

        return Result.ok(p);
    }

    @Override
    public Result<Page<MaintenanceOrderVo>> pageWaitProcessOrder(TenantIsolation tenantIsolation,
                                                                 QueryMaintenanceOrderDto dto,
                                                                 Page<MaintenanceOrderVo> page) {
        Integer idType = tenantIsolation.getIdType();
        Long tenantId = tenantIsolation.getTenantId();
        // 当前用户id
        Long currentUserId = JwtUserInfoUtils.getUserId();

        Page<MaintenanceOrderVo> voPage = new Page<>();

        // 查询用户待办工单流程ID
        Result<List<TaskDto>> todoTaskResult = orderProcessService.queryTodoTaskList(currentUserId);
        if(!todoTaskResult.getSignal()){
            throw new BizException(todoTaskResult.getMessage());
        }
        List<TaskDto> todoTaskList = todoTaskResult.getResult();
        if (CollectionUtil.isEmpty(todoTaskList)) {
            return Result.ok(voPage);
        }
        Set<String> todoProcessInstanceIdList = todoTaskList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toSet());
        dto.setProcessInstanceIdList(todoProcessInstanceIdList);

        dto.setTenantId(tenantId);
        dto.setIdType(idType);
        voPage = mapper.pageWaitProcessOrderByAuth(currentUserId, dto, page);

        if(voPage != null){
            List<MaintenanceOrderVo> records = voPage.getRecords();
            if(records != null){
                if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                    records.forEach(i->{i.setSupportSupplierId(null);});
                }
                // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
                List<String> processInstanceIdList = records.stream().map(i -> i.getProcessInstanceId()).collect(Collectors.toList());
                Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(processInstanceIdList);
                if (!mapResult.getSignal()) {
                    return Result.error(mapResult.getMessage());
                }
                Map<String, String> lastProcessMap = mapResult.getResult();

                // 查询转交权限
                Result<Map<String, Boolean>> forwardMapResult = orderProcessService.queryForwardPermissionByProcessInstances(processInstanceIdList);
                if (!forwardMapResult.getSignal()) {
                    return Result.error(forwardMapResult.getMessage());
                }
                Map<String, Boolean> forwardMap = forwardMapResult.getResult();

                records.forEach(record -> {
                    String lastUserId = lastProcessMap.get(record.getProcessInstanceId());

                    record.setOperations(listOrderOperations(
                            record,
                            idType,
                            true,
                            ObjectUtil.equals(lastUserId, tenantIsolation.getTenantId() + ":" + currentUserId.toString()),
                            forwardMap.getOrDefault(record.getProcessInstanceId(), false),
                            currentUserId,
                            false,
                            null)
                    );
                });
            }
        }
        return Result.ok(voPage);
    }

    private void validProcessOrderRevokeAuth(Long currentUserId, MaintenanceOrderVo vo, TenantIsolation tenantIsolation) {
        MaintenanceOrderEntity entity = BeanUtilsIntensifier.copyBean(vo, MaintenanceOrderEntity.class);
        Result result = validRevokeAuth(currentUserId, entity, tenantIsolation);
        List<String> authList = new ArrayList<>();
        vo.setOperations(authList);
        if (result.getSignal()) {
            authList.add(OperationEnum.REVOKE.getName());
        }
    }

    @Override
    public Result<Page<MaintenanceOrderVo>> pageProcessOrder(TenantIsolation tenantIsolation,
                                                             QueryMaintenanceOrderDto dto,
                                                             Page<MaintenanceOrderVo> page) {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        Long currentUserId = JwtUserInfoUtils.getUserId();
        dto.setTenantId(tenantId);
        dto.setIdType(idType);
        Page<MaintenanceOrderVo> voPage = mapper.pageProcessOrder(dto, page, currentUserId);

        if(voPage != null){
            List<MaintenanceOrderVo> records = voPage.getRecords();
            if(records != null){
                if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                    records.forEach(i->{i.setSupportSupplierId(null);});
                }
                // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
                List<String> processInstanceIdList = records.stream().map(i -> i.getProcessInstanceId()).collect(Collectors.toList());
                Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(processInstanceIdList);
                if (!mapResult.getSignal()) {
                    return Result.error(mapResult.getMessage());
                }
                Map<String, String> lastProcessMap = mapResult.getResult();

                records.forEach(record -> {
                    String lastUserId = lastProcessMap.get(record.getProcessInstanceId());

                    record.setOperations(listOrderOperations(
                            record,
                            idType,
                            false,
                            ObjectUtil.equals(lastUserId, tenantIsolation.getTenantId() + ":" + currentUserId.toString()),
                            false,
                            currentUserId,
                            false,
                            null)
                    );
                });
            }
        }
        return Result.ok(voPage);
    }

    @Override
    public Result<Page<MaintenanceOrderVo>> pageSupplierReceiveOrder(TenantIsolation tenantIsolation,
                                                                     QueryMaintenanceOrderDto dto,
                                                                     Page<MaintenanceOrderVo> page) {
        dto.setTenantId(tenantIsolation.getTenantId());
        Page<MaintenanceOrderVo> voPage = mapper.pageSupplierReceiveOrder(dto, page);

        return Result.ok(voPage);
    }

    @Override
    @Transactional
    public Result dispatchOrder(TenantIsolation tenantIsolation, MaintenanceOrderDispatchDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        Long orderId = dto.getMaintenanceOrderId();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        if (!OrderStatusEnum.WAIT_DISPATCH.getValue().equals(maintenanceOrder.getStatus())) {
            if (OrderStatusEnum.REVOKE.getValue().equals(maintenanceOrder.getStatus())){
                return Result.error("派单失败，工单已撤销");
            }
            return Result.error("派单失败，工单已分派");
        }

        Long receiveUserId = dto.getReceiveUserId();

        //更新工单状态、接收人
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.EXECUTING.getValue())
                .set(MaintenanceOrderEntity::getExecuteUserId, receiveUserId)
                .eq(MaintenanceOrderEntity::getId, orderId));

        boolean innerOrderFlag = OutsourceEnum.NO.getValue().equals(maintenanceOrder.getOutsource());
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        // 派工工单流程
        ResponsibleDto targetUser = new ResponsibleDto();
        targetUser.setId(receiveUserId);
        targetUser.setName(dto.getReceiveUserName() + "(" + dto.getWorkGroupName() + ")");
        Result<Void> dispatchResult = orderProcessService.dispatch(tenantId, orderId, maintenanceOrder.getProcessInstanceId(), targetUser, OrderTypeEnum.MAINTENANCE.getValue(), isSuperRole);
        if(!dispatchResult.getSignal()){
            throw new BizException(dispatchResult.getMessage());
        }

        // 发送通知
        asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantId, idType, OrderTypeEnum.MAINTENANCE, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    @Transactional
    public Result withdrawDispatch(TenantIsolation tenantIsolation, Long id) {
        MaintenanceOrderEntity maintenanceOrder = this.getById(id);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        if (!OrderStatusEnum.EXECUTING.getValue().equals(maintenanceOrder.getStatus())) {
            return Result.error("撤回派工失败，工单不是执行中状态");
        }

        Boolean isAutoDispatch = false;
        Object variable = runtimeService.getVariable(maintenanceOrder.getProcessInstanceId(), AUTO_DISPATCH_FLAG);
        if (variable != null) {
            List<SelectValue> selectValues = JsonUtil.parseArray(JsonUtil.toJSONString(variable), SelectValue.class);
            if (CollUtil.isNotEmpty(selectValues)){
                isAutoDispatch = ObjectUtil.equals(selectValues.get(0).getValue(),"1");
            }
        }
        //更新工单状态
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus,isAutoDispatch? OrderStatusEnum.WAIT_RECEIVE.getValue(): OrderStatusEnum.WAIT_DISPATCH.getValue())
                .eq(MaintenanceOrderEntity::getId, id));

        // 撤回派工工单流程
        Result<Void> undoResult = orderProcessService.withdrawTask(
                tenantIsolation.getTenantId(),
                id,
                maintenanceOrder.getProcessInstanceId(),
                null,
                OrderTypeEnum.MAINTENANCE.getValue(),
                isAutoDispatch?OrderProcessOperationEnum.WITHDRAW_RECEIVE.getValue() :OrderProcessOperationEnum.WITHDRAW_DISPATCH.getValue()
        );
        if(!undoResult.getSignal()){
            throw new BizException(undoResult.getMessage());
        }

        // 发送通知
        asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.MAINTENANCE,isAutoDispatch? OrderStatusEnum.WAIT_RECEIVE: OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);

        return Result.ok();
    }

    @Autowired
    private IWorkGroupUserService  workGroupUserService;

    @Override
    @Transactional
    public Result receiveOrder(TenantIsolation tenantIsolation, OrderReceiveDto dto) {

        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        Long orderId = dto.getId();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        if (!OrderStatusEnum.WAIT_RECEIVE.getValue().equals(maintenanceOrder.getStatus())) {
            if (OrderStatusEnum.REVOKE.getValue().equals(maintenanceOrder.getStatus())){
                return Result.error("接单失败，工单已撤销");
            }
            return Result.error("接单失败，工单已被接收");
        }

        Long userId = JwtUserInfoUtils.getUserId();
        String userName = JwtUserInfoUtils.getRealname();

        //更新工单状态、接收人
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.EXECUTING.getValue())
                .set(MaintenanceOrderEntity::getExecuteUserId, userId)
                .eq(MaintenanceOrderEntity::getId, orderId));

        boolean innerOrderFlag = OutsourceEnum.NO.getValue().equals(maintenanceOrder.getOutsource());
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        // 派工工单流程
        String workGroupNameByUserId = workGroupUserService.getWorkGroupNameByUserId(OrderTypeEnum.INSPECT.getValue(), userId, tenantIsolation);
        if (StrUtil.isNotBlank(workGroupNameByUserId)) {
            userName = userName + "(" +workGroupNameByUserId +")";
        }
        ResponsibleDto targetUser = new ResponsibleDto();
        targetUser.setId(userId);
        targetUser.setName(userName);
        Result<Void> dispatchResult = orderProcessService.receiveOrder(tenantId, orderId, maintenanceOrder.getProcessInstanceId(), targetUser, OrderTypeEnum.MAINTENANCE.getValue(), isSuperRole);
        if(!dispatchResult.getSignal()){
            throw new BizException(dispatchResult.getMessage());
        }

        // 发送通知
        asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantId, idType, OrderTypeEnum.MAINTENANCE, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    @Transactional
    public Result executeOrder(TenantIsolation tenantIsolation, MaintenanceOrderExecuteDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        Result tempResult = tempExecuteOrder(tenantIsolation, dto);
        if(!tempResult.getSignal()) {
            return Result.error(tempResult.getMessage());
        }

        Long orderId = dto.getMaintenanceOrderId();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        boolean innerOrderFlag = OutsourceEnum.NO.getValue().equals(maintenanceOrder.getOutsource());
        //更新工单状态、保养时间
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.WAIT_ACCEPT.getValue())
                .eq(MaintenanceOrderEntity::getId, orderId));
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        // 执行工单流程
        Result<Void> executeResult = orderProcessService.doTask(
                tenantIsolation.getTenantId(),
                orderId,
                maintenanceOrder.getProcessInstanceId(),
                OrderTypeEnum.MAINTENANCE.getValue(), isSuperRole
        );
        if(!executeResult.getSignal()){
            throw new BizException(executeResult.getMessage());
        }

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantId, idType, OrderTypeEnum.MAINTENANCE, OrderStatusEnum.WAIT_ACCEPT,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);
        return Result.ok();
    }

    @Override
    @Transactional
    public Result withdrawExecute(TenantIsolation tenantIsolation, Long id) {
        MaintenanceOrderEntity maintenanceOrder = this.getById(id);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        if (!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(maintenanceOrder.getStatus())) {
            return Result.error("撤回执行失败，工单不是待验收状态");
        }

        //更新工单状态
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.EXECUTING.getValue())
                .eq(MaintenanceOrderEntity::getId, id));

        // 撤回执行工单流程
        Result<Void> undoResult = orderProcessService.withdrawTask(
                tenantIsolation.getTenantId(),
                id,
                maintenanceOrder.getProcessInstanceId(),
                null,
                OrderTypeEnum.MAINTENANCE.getValue(),
                OrderProcessOperationEnum.WITHDRAW_EXECUTE.getValue()
        );
        if(!undoResult.getSignal()){
            throw new BizException(undoResult.getMessage());
        }

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.MAINTENANCE, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    @Transactional
    public Result tempExecuteOrder(TenantIsolation tenantIsolation, MaintenanceOrderExecuteDto dto) {
        Long orderId = dto.getMaintenanceOrderId();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        if (!OrderStatusEnum.EXECUTING.getValue().equals(maintenanceOrder.getStatus())) {
            return Result.error("执行工单失败，工单不是保养中状态");
        }

        // 删除人员工作量
        Integer deleteCount = workloadMapper.deleteByOrderId(OrderTypeEnum.MAINTENANCE.getValue(), orderId);

        // 插入人员工作量
        List<WorkloadEntity> workloadList = dto.getWorkloadList();
        for(WorkloadEntity t:workloadList){
            t.setId(null);
            t.setOrderId(orderId);
            t.setOrderType(OrderTypeEnum.MAINTENANCE.getValue());
            LocalDateTime beginTime = t.getBeginTime();
            LocalDateTime endTime = t.getEndTime();
            if(beginTime != null && endTime != null){
                if(beginTime.isAfter(endTime)){
                    throw new RuntimeException("开始时间不能大于结束时间");
                }
                Long minutes = ChronoUnit.MINUTES.between(beginTime, endTime);
                t.setCostTime(minutes.intValue());
            }
            workloadMapper.insert(t);
        }
        if (dto.getSupportSupplierId()!=null){
            mapper.updateSupportSupplierIdById(maintenanceOrder.getId(),dto.getSupportSupplierId());
        }
        return Result.ok();
    }

    @Override
    @Transactional
    public Result withdrawOrder(TenantIsolation tenantIsolation, MaintenanceOrderWithdrawDto dto) {
        Long orderId = dto.getMaintenanceOrderId();
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        // 检查工单是否是待验收状态
        if(!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(maintenanceOrder.getStatus())){
            return Result.error("驳回失败，工单不是待验收状态");
        }

        //更新工单状态
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.EXECUTING.getValue())
                .eq(MaintenanceOrderEntity::getId, orderId));

        Integer outsource = maintenanceOrder.getOutsource();
        Boolean isDataRecord = false;
        if(IdTypeEnum.CUSTOMER.getValue().equals(idType)){
            isDataRecord = ObjectUtil.equals(maintenanceOrder.getOutsourceSupplierType(), SupplierTypeEnum.SELF_DEFINING.getValue());
        } else if (IdTypeEnum.SUPPLIER.getValue().equals(idType)) {
            isDataRecord = ObjectUtil.equals(maintenanceOrder.getOutsourceCustomerType(), SupplierTypeEnum.SELF_DEFINING.getValue());
        }

        OrderStatusEnum orderStatusEnum = OrderStatusEnum.EXECUTING;
        // 驳回工单流程
        if(OutsourceEnum.YES.getValue().equals(outsource) && !isDataRecord){
            Result<Void> undoResult = orderProcessService.rejectedTask(
                    maintenanceOrder.getSupplierId(),
                    orderId,
                    maintenanceOrder.getProcessInstanceId(),
                    dto.getReason(),
                    OrderTypeEnum.MAINTENANCE.getValue(),
                    true,
                    IdTypeEnum.typeOfValue(idType)
            );
            if(!undoResult.getSignal()){
                throw new BizException(undoResult.getMessage());
            }

            Result<Boolean> r = orderProcessService.queryVendorAutoAcceptByProcessInstanceId(maintenanceOrder.getProcessInstanceId());
            if(!r.getSignal()){
                throw new BizException(r.getMessage());
            }
            Boolean isAuto = r.getResult();
            if(!isAuto && IdTypeEnum.CUSTOMER.getValue().equals(idType)){
                //更新工单状态
                this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                        .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.WAIT_ACCEPT.getValue())
                        .eq(MaintenanceOrderEntity::getId, orderId));
                orderStatusEnum = OrderStatusEnum.WAIT_ACCEPT;
            }
        }else{
            Result<Void> undoResult = orderProcessService.rejectedTask(
                    tenantId,
                    orderId,
                    maintenanceOrder.getProcessInstanceId(),
                    dto.getReason(),
                    OrderTypeEnum.MAINTENANCE.getValue()
            );
            if(!undoResult.getSignal()){
                throw new BizException(undoResult.getMessage());
            }
        }


        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantId, idType, OrderTypeEnum.MAINTENANCE, orderStatusEnum,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    public Result acceptOrder(TenantIsolation tenantIsolation, MaintenanceOrderAcceptDto dto) {
        Long orderId = dto.getMaintenanceOrderId();
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        Integer status = null;

        if (!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(maintenanceOrder.getStatus())) {
            return Result.error("验收失败，工单不是待验收状态");
        }
        if (YesNoEnum.YES.getValue().equals(dto.getAcceptResult())) {
            status = OrderStatusEnum.ACCEPTED.getValue();
        } else {
            status = OrderStatusEnum.REJECT.getValue();
        }

        //更新工单状态
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus, status)
                .set(MaintenanceOrderEntity::getMaintenanceEnd, LocalDateTime.now())
                .eq(MaintenanceOrderEntity::getId, orderId));

        Integer outsource = maintenanceOrder.getOutsource();
        Boolean isDataRecord = false;
        if(IdTypeEnum.CUSTOMER.getValue().equals(idType)){
            isDataRecord = ObjectUtil.equals(maintenanceOrder.getOutsourceSupplierType(), SupplierTypeEnum.SELF_DEFINING.getValue());
        } else if (IdTypeEnum.SUPPLIER.getValue().equals(idType)) {
            isDataRecord = ObjectUtil.equals(maintenanceOrder.getOutsourceCustomerType(), SupplierTypeEnum.SELF_DEFINING.getValue());
        }
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        if(OutsourceEnum.YES.getValue().equals(outsource)){
            if (isDataRecord) {
                // 数据型，验收
                Result<Void> completeResult = orderProcessService.complete(
                        tenantId,
                        orderId,
                        maintenanceOrder.getProcessInstanceId(),
                        YesNoEnum.YES.getValue().equals(dto.getAcceptResult()),
                        dto.getAcceptDesc(),
                        OrderTypeEnum.MAINTENANCE.getValue(),
                        isSuperRole
                );
                if(!completeResult.getSignal()){
                    throw new BizException(completeResult.getMessage());
                }
            } else {
                if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
                    this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                            .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.WAIT_ACCEPT.getValue())
                            .eq(MaintenanceOrderEntity::getId, orderId));
                    // 供应商验收
                    Result<Void> vendorAcceptanceResult = orderProcessService.vendorAcceptance(
                            maintenanceOrder.getSupplierId(),
                            orderId,
                            maintenanceOrder.getProcessInstanceId(),
                            dto.getAcceptDesc(),
                            OrderTypeEnum.MAINTENANCE.getValue(), isSuperRole
                    );
                    if(!vendorAcceptanceResult.getSignal()){
                        throw new BizException(vendorAcceptanceResult.getMessage());
                    }
                    // 发送站内信通知
                    asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                            maintenanceOrder.getOutsource(), tenantId, idType, OrderTypeEnum.MAINTENANCE, OrderStatusEnum.WAIT_ACCEPT,
                            TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()),null);
                }else{
                    // 客户验收
                    Result<Void> completeResult = orderProcessService.complete(
                            maintenanceOrder.getSupplierId(),
                            orderId,
                            maintenanceOrder.getProcessInstanceId(),
                            YesNoEnum.YES.getValue().equals(dto.getAcceptResult()),
                            dto.getAcceptDesc(),
                            OrderTypeEnum.MAINTENANCE.getValue(), isSuperRole
                    );
                    if(!completeResult.getSignal()){
                        throw new BizException(completeResult.getMessage());
                    }
                }
            }
        }else{
            Result<Void> completeResult = orderProcessService.complete(
                    tenantId,
                    orderId,
                    maintenanceOrder.getProcessInstanceId(),
                    YesNoEnum.YES.getValue().equals(dto.getAcceptResult()),
                    dto.getAcceptDesc(),
                    OrderTypeEnum.MAINTENANCE.getValue(), isSuperRole
            );
            if(!completeResult.getSignal()){
                throw new BizException(completeResult.getMessage());
            }
        }

        // 处理工单告警记录
        alarmRecordService.finishAlarm(orderId, OrderTypeEnum.MAINTENANCE.getValue());

        return Result.ok();
    }

    private Result validRevokeAuth(Long currentUserId, MaintenanceOrderEntity maintenanceOrder, TenantIsolation tenantIsolation) {
        //我创建的且待分派的订单才允许撤销
        if (!currentUserId.equals(maintenanceOrder.getCreatorId())) {
            return Result.error("撤销失败，不是工单创建人，无法撤销工单");
        }

        boolean innerOrderFlag = OutsourceEnum.NO.getValue().equals(maintenanceOrder.getOutsource());
        if (!innerOrderFlag && IdTypeEnum.CUSTOMER.getValue().equals(maintenanceOrder.getCreateSource())) {
            if (!ObjectUtil.equals(maintenanceOrder.getTenantId(), tenantIsolation.getTenantId()) || !ObjectUtil.equals(tenantIsolation.getIdType(), DictConstant.ID_TYPE_CUSTOMER)) {
                return Result.error("供应商身份无法撤销外委工单!");
            }
        }

        if (!OrderStatusEnum.WAIT_DISPATCH.getValue().equals(maintenanceOrder.getStatus()) && !OrderStatusEnum.WAIT_RECEIVE.getValue().equals(maintenanceOrder.getStatus())) {
            return Result.error("撤销失败，工单已分派或已被接收");
        }
        return Result.ok();
    }

    @Override
    @Transactional
    public Result revokeOrder(TenantIsolation tenantIsolation, Long id) {
        MaintenanceOrderEntity maintenanceOrder = this.getById(id);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        validRevokeAuth(currentUserId, maintenanceOrder, tenantIsolation);

        //更新工单状态
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getStatus, OrderStatusEnum.REVOKE.getValue())
                .eq(MaintenanceOrderEntity::getId, id));

        // 撤回工单流程
        Result<Void> undoResult = orderProcessService.undo(
                tenantIsolation.getTenantId(),
                id,
                maintenanceOrder.getProcessInstanceId(),
                OrderTypeEnum.MAINTENANCE.getValue()
        );
        if(!undoResult.getSignal()){
            throw new BizException(undoResult.getMessage());
        }

        // 处理工单告警记录
        alarmRecordService.finishAlarm(id, OrderTypeEnum.MAINTENANCE.getValue());

        return Result.ok();
    }

    @Override
    @Transactional
    public Result forwardOrder(TenantIsolation tenantIsolation, MaintenanceOrderForwardDto dto) {
        Long orderId = dto.getMaintenanceOrderId();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        // 检查工单是否是执行中状态
        if(!OrderStatusEnum.EXECUTING.getValue().equals(maintenanceOrder.getStatus())){
            return Result.error("转交失败，工单不是执行中状态");
        }

        // 检查接收人是否有工单执行权限
        Long receiveUserId = dto.getReceiveUserId();

        // 修改接收人
        this.update(Wrappers.<MaintenanceOrderEntity>lambdaUpdate()
                .set(MaintenanceOrderEntity::getExecuteUserId, receiveUserId)
                .eq(MaintenanceOrderEntity::getId, orderId));

        // 转交工单流程
        ResponsibleDto targetUser = new ResponsibleDto();
        targetUser.setId(receiveUserId);
        targetUser.setName(dto.getReceiveUserName() + "(" + dto.getWorkGroupName() + ")");
        Result<Void> dispatchResult = orderProcessService.forward(
                tenantIsolation.getTenantId(),
                orderId,
                maintenanceOrder.getProcessInstanceId(),
                targetUser,
                dto.getRemark(),
                OrderTypeEnum.MAINTENANCE.getValue()
        );
        if(!dispatchResult.getSignal()){
            throw new BizException(dispatchResult.getMessage());
        }

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(maintenanceOrder.getProcessInstanceId(), maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.MAINTENANCE, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), OutsourceEnum.YES.getValue().equals(maintenanceOrder.getOutsource()) ? maintenanceOrder.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    public Result<Void> deleteById(@NotNull Long entityId) {
        if (mapper.deleteById(entityId) > 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    /*@Override
    public Result<MaintenanceOrderDetailVo> getById(TenantIsolation tenantIsolation, Long entityId) {
        MaintenanceOrderEntity maintenanceOrder = this.getById(entityId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        MaintenanceOrderDetailVo vo = mapper.getOrderDetailVo(entityId);

        if (StringUtils.isNotEmpty(maintenanceOrder.getMaintenanceStandardId())) {
            String[] standardIds = maintenanceOrder.getMaintenanceStandardId().split(",");
            List<MaintenanceStandardEntity> standardList = maintenanceStandardService.list(Wrappers.<MaintenanceStandardEntity>lambdaQuery()
                    .select(MaintenanceStandardEntity::getStandardName)
                    .in(MaintenanceStandardEntity::getId, standardIds));
            String standardName = standardList.stream().map(MaintenanceStandardEntity::getStandardName).collect(Collectors.joining(","));
            vo.setStandardName(standardName);
        }

        List<FileVo> maintenanceFileList = fileMapper.listByBizIds(FileBizTypeEnum.MAINTENANCE_EXECUTE_DOC.getValue(), Arrays.asList(entityId));
        vo.setMaintenanceFiles(maintenanceFileList);

        //查询保养项
        List<MaintenanceItemEntity> maintenanceItemList = maintenanceItemService.list(Wrappers.<MaintenanceItemEntity>lambdaQuery()
                .eq(MaintenanceItemEntity::getMaintenanceOrderId, entityId));
        List<MaintenanceItemVo> maintenanceItemVoList = BeanUtilsIntensifier.copyBeanList(maintenanceItemList, MaintenanceItemVo.class);
        List<Long> itemIds = maintenanceItemVoList.stream().map(MaintenanceItemVo::getId).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(itemIds)) {
            List<FileVo> fileList = fileMapper.listByBizIds(FileBizTypeEnum.MAINTENANCE_ITEM_PICTURE.getValue(), itemIds);
            if(!CollectionUtil.isEmpty(fileList)){
                // bizId -> fileList
                Map<Long, List<FileVo>> map = fileList.stream().collect(Collectors.groupingBy(FileVo::getBizId));
                maintenanceItemVoList.forEach(t -> {
                    Long bizId = t.getId();
                    List<FileVo> list = map.get(bizId);
                    t.setMaintenanceItemImages(list);
                });
            }
        }

        List<MaintenanceOrderProgressEntity> orderProgressList = maintenanceOrderProgressService.list(Wrappers.<MaintenanceOrderProgressEntity>lambdaQuery()
                .eq(MaintenanceOrderProgressEntity::getMaintenanceOrderId, entityId));
        vo.setMaintenanceItemList(maintenanceItemVoList);
        vo.setMaintenanceOrderProgressList(orderProgressList);
        return Result.ok(vo);
    }*/

    @Override
    public Result<MaintenanceOrderDetailVo> getOrderDetailById(TenantIsolation tenantIsolation, Long entityId) {
        MaintenanceOrderEntity maintenanceOrder = this.getById(entityId);
        if (maintenanceOrder == null) {
            return Result.error("保养工单不存在");
        }

        MaintenanceOrderDetailVo orderVo = BeanUtilsIntensifier.copyBean(maintenanceOrder, MaintenanceOrderDetailVo.class);

        //获取工单图片
        List<FileVo> maintenanceFileList = fileMapper.listByBizIds(FileBizTypeEnum.MAINTENANCE_ORDER_PICTURE.getValue(), Arrays.asList(entityId));
        orderVo.setOrderFileList(maintenanceFileList);

        // 获取工单设备列表
        List<MaintenanceResultVo> resultList = maintenanceResultMapper.listbyOrderIdWithDeviceInfo(entityId);
        orderVo.setDeviceList(resultList);

        // 获取工单结果设备附件
        if(resultList != null && resultList.size() > 0){
            List<Long> bizIds = resultList.stream().map(MaintenanceResultVo::getId).collect(Collectors.toList());
            if(bizIds != null && bizIds.size() > 0){
                List<FileVo> fileList = fileMapper.listByBizIds(FileBizTypeEnum.MAINTENANCE_RESULT_PICTURE.getValue(), bizIds);
                if(fileList != null && fileList.size() > 0){
                    // bizId -> fileList
                    Map<Long, List<FileVo>> map = fileList.stream().collect(Collectors.groupingBy(FileVo::getBizId));
                    resultList.forEach(t -> {
                        Long bizId = t.getId();
                        List<FileVo> list = map.get(bizId);
                        t.setResultFileList(list);
                    });
                }
            }
        }

        // 获取工单检查项列表
        if(resultList != null){
            Integer maintenanceDeviceCount = 0;
            Integer notMaintenanceDeviceCount = 0;
            Integer errorDeviceCount = 0;
            Integer errorItemCount = 0;

            for(MaintenanceResultVo vo:resultList){
                List<String> errorItemDescList = new ArrayList<>();
                Long maintenanceResultId = vo.getId();
                Integer status = vo.getStatus();
                if(status == null){
                    notMaintenanceDeviceCount++;
                }else{
                    if(MaintenanceResultStatusEnum.MAINTENANCE.getValue().equals(status)){
                        maintenanceDeviceCount++;
                    }else{
                        notMaintenanceDeviceCount++;
                    }
                }
                List<DeviceMaintenanceItemDto> itemList = maintenanceResultItemMapper.listByResultIdWithDetail(maintenanceResultId);
                if(itemList == null){
                    itemList = new ArrayList<>();
                }
                vo.setTotalItemCount(itemList.size());
                vo.setUnFinishItemCount(itemList.stream().filter(i -> YesNoEnum.NO.getValue().equals(i.getStatus())).collect(Collectors.toList()).size());

                Long errorCount = itemList.stream().filter(t -> {
                    if(t.getIsError() == null){
                        return false;
                    }
                    if(YesNoEnum.YES.getValue().equals(t.getIsError())){
                        String s = "部位：" + (StringUtils.isEmpty(t.getFullBomName())?"通用":t.getFullBomName()) + "；" + "保养项目：" + t.getPosition() + "；" + "备注：" + (StringUtils.isNotEmpty(t.getRemark()) ? t.getRemark() : "");
                        errorItemDescList.add(s);
                        return true;
                    }
                    return false;
                }).count();
                errorItemCount += errorCount.intValue();
                vo.setErrorItemCount(errorCount.intValue());
                vo.setErrorItemDescList(errorItemDescList);

                List<MaintenanceResultStandardVo> standardList = maintenanceResultStandardMapper.listStandardByResultId(vo.getId());
                if(standardList != null && standardList.size() > 0){
                    List<Long> standardIdList = new ArrayList<>();
                    List<String> standardNameList = new ArrayList<>();
                    standardList.forEach(t -> {
                        standardIdList.add(t.getMaintenanceStandardId());
                        standardNameList.add(t.getStandardName());
                    });
                    vo.setStandardIdList(standardIdList);
                    vo.setStandardNameList(standardNameList);
                }

                Integer isError = vo.getIsError();
                if(isError != null && YesNoEnum.YES.getValue().equals(isError)){
                    errorDeviceCount++;
                }
            }
            orderVo.setTotalDeviceCount(resultList.size());
            orderVo.setMaintenanceDeviceCount(maintenanceDeviceCount);
            orderVo.setNotMaintenanceDeviceCount(notMaintenanceDeviceCount);
            orderVo.setErrorItemCount(errorItemCount);
            orderVo.setErrorDeviceCount(errorDeviceCount);
        }else{
            orderVo.setTotalDeviceCount(0);
            orderVo.setMaintenanceDeviceCount(0);
            orderVo.setNotMaintenanceDeviceCount(0);
            orderVo.setErrorItemCount(0);
            orderVo.setErrorDeviceCount(0);
        }

        // 当前用户id
        Long currentUserId = JwtUserInfoUtils.getUserId();

        // 查询当前用户所有待办任务信息
        Result<List<TaskDto>> listResult = orderProcessService.queryTodoTaskList(currentUserId);
        if (!listResult.getSignal()) {
            return Result.error(listResult.getMessage());
        }
        List<TaskDto> todoTaskDtoList = listResult.getResult();
        List<String> todoProcessInstanceIdList = todoTaskDtoList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());

        // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
        String lastUserId = null;
        String processInstanceId = maintenanceOrder.getProcessInstanceId();
        if (StringUtils.isNotEmpty(processInstanceId)) {
            Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(Arrays.asList(processInstanceId));
            if (!mapResult.getSignal()) {
                return Result.error(mapResult.getMessage());
            }
            Map<String, String> lastProcessMap = mapResult.getResult();
            lastUserId = lastProcessMap.get(maintenanceOrder.getProcessInstanceId());
        }

        // 查询转交权限
        Result<Map<String, Boolean>> forwardMapResult = orderProcessService.queryForwardPermissionByProcessInstances(Arrays.asList(processInstanceId));
        if (!forwardMapResult.getSignal()) {
            return Result.error(forwardMapResult.getMessage());
        }
        Map<String, Boolean> forwardMap = forwardMapResult.getResult();

        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;

        Result<Map<String, Boolean>> acceptMapResult = orderProcessService.queryAcceptStateByProcessInstances(Arrays.asList(processInstanceId), tenantIsolation.getIdType());
        if (!acceptMapResult.getSignal()) {
            throw new BizException(acceptMapResult.getMessage());
        }
        Map<String, Boolean> acceptMap = acceptMapResult.getResult();

        orderVo.setOperations(listOrderOperations(
                maintenanceOrder,
                tenantIsolation.getIdType(),
                todoProcessInstanceIdList.contains(maintenanceOrder.getProcessInstanceId()),
                ObjectUtil.equals(lastUserId, tenantIsolation.getTenantId() + ":" + currentUserId.toString()),
                forwardMap.getOrDefault(processInstanceId, false),
                currentUserId,
                isSuperRole,
                acceptMap)
        );

        List<MaintenanceOrderProgressVo> orderProgressList = maintenanceOrderProgressMapper.listProgressbyOrderId(entityId);
        orderVo.setMaintenanceOrderProgressList(orderProgressList);

        // 人员工作量
        List<WorkloadEntity> workloadList = workloadMapper.listByOrderId(OrderTypeEnum.MAINTENANCE.getValue(), entityId);
        orderVo.setWorkloadList(workloadList);
        return Result.ok(orderVo);
    }

    @Override
    @Transactional
    public Result quikOutsourceOrder(TenantIsolation tenantIsolation, QuickMaintenanceOrderDto dto) {
        Long orderId = dto.getOrderId();
        MaintenanceOrderEntity maintenanceOrder = this.getById(orderId);
        if (maintenanceOrder == null) {
            return Result.error("选择的客户保养工单不存在");
        }
        List<Integer> handleStatusList = Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(),
                OrderStatusEnum.WAIT_RECEIVE.getValue(), OrderStatusEnum.EXECUTING.getValue(),
                OrderStatusEnum.WAIT_ACCEPT.getValue());
        if (!handleStatusList.contains(maintenanceOrder.getStatus())) {
            return Result.error("快捷外委工单失败，工单不是待处理状态");
        }

        //工单是否已经外委并且未完结
        List<MaintenanceOrderEntity> existOrderList = this.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                .eq(MaintenanceOrderEntity::getOutsourceOriginId, orderId)
                .in(MaintenanceOrderEntity::getStatus, handleStatusList));
        if (CollectionUtil.isNotEmpty(existOrderList)) {
            return Result.error("当前工单正在被外委中，不可重复外委");
        }

        //新建外委工单
        MaintenanceOrderDto outsourceOrder = BeanUtilsIntensifier.copyBean(maintenanceOrder, MaintenanceOrderDto.class);

        outsourceOrder.setOutsourceOriginId(orderId);//原始工单ID
        outsourceOrder.setOutsource(OutsourceEnum.YES.getValue());
        outsourceOrder.setSupplierId(dto.getSupplierId());
        outsourceOrder.setSupplierName(dto.getSupplierName());
        outsourceOrder.setAggregatorOutsource(YesNoEnum.YES.getValue());
        outsourceOrder.setCreateSource(IdTypeEnum.SUPPLIER.getValue());
        List<OrderDeviceDto> deviceList = new ArrayList<>();
        // 根据复制工单id获取工单结果
        List<MaintenanceResultEntity> resultList = maintenanceResultMapper.listByOrderId(orderId);
        if(resultList != null){
            resultList.forEach(t -> {
                OrderDeviceDto od = new OrderDeviceDto();
                od.setDeviceId(t.getDeviceId());
                List<IdNameDto> standardList = maintenanceResultStandardMapper.listStandardIdNameByResultId(t.getId());
                od.setStandardList(standardList);
                deviceList.add(od);
            });
        }
        outsourceOrder.setDeviceList(deviceList);
        this.save(tenantIsolation,outsourceOrder);

        return Result.ok();
    }

    @Override
    public Result<DeviceMaintenanceCountVo> countDeviceMaintenanceOrder(TenantIsolation tenantIsolation, Long deviceId) {
        if (deviceId == null) {
            return Result.error("设备ID不能为空");
        }
        DeviceMaintenanceCountVo vo = new DeviceMaintenanceCountVo();

        QueryMaintenanceOrderDto dto = new QueryMaintenanceOrderDto();
        dto.setDeviceId(deviceId);
        dto.setOutsource(OutsourceEnum.NO.getValue());
        Page<MaintenanceOrderVo> page = new Page<>();
        page.setCurrent(1);
        page.setSize(10);
        Result<MaintenanceOrderPage> in = getPage(tenantIsolation, dto, page);
        vo.setInsourceCount((int) in.getResult().getTotal());

        dto.setOutsource(OutsourceEnum.YES.getValue());
        Result<MaintenanceOrderPage> out = getPage(tenantIsolation, dto, page);
        vo.setOutsourceCount((int) out.getResult().getTotal());
        return Result.ok(vo);
    }

    @Override
    public Result<WaitProcessMaintenanceCountVo> countWaitProcessMaintenanceOrder(TenantIsolation tenantIsolation) {
        WaitProcessMaintenanceCountVo vo = new WaitProcessMaintenanceCountVo();

        QueryMaintenanceOrderDto dto = new QueryMaintenanceOrderDto();
        dto.setOutsource(OutsourceEnum.NO.getValue());
        Page<MaintenanceOrderVo> page = new Page<>();
        page.setCurrent(1);
        page.setSize(10);

        Result<Page<MaintenanceOrderVo>> in = pageWaitProcessOrder(tenantIsolation, dto, page);
        vo.setInsourceCount((int) in.getResult().getTotal());

        dto.setOutsource(OutsourceEnum.YES.getValue());
        Result<Page<MaintenanceOrderVo>> out = pageWaitProcessOrder(tenantIsolation, dto, page);
        vo.setOutsourceCount((int) out.getResult().getTotal());
        return Result.ok(vo);
    }

    @Override
    public Result<WaitProcessMaintenanceCountVo> countWaitProcessMaintenanceOrderByTenant(TenantIsolation tenantIsolation) {
        WaitProcessMaintenanceCountVo vo = new WaitProcessMaintenanceCountVo();
        List<MaintenanceOrderEntity> maintenanceOrderList = this.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                .eq(MaintenanceOrderEntity::getTenantId, tenantIsolation.getTenantId())
                .in(MaintenanceOrderEntity::getStatus, Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(),
                        OrderStatusEnum.WAIT_RECEIVE.getValue(), OrderStatusEnum.EXECUTING.getValue(),
                        OrderStatusEnum.WAIT_ACCEPT.getValue())));
        Map<Integer, List<MaintenanceOrderEntity>> map = maintenanceOrderList.stream().collect(Collectors.groupingBy(MaintenanceOrderEntity::getOutsource));
        List<MaintenanceOrderEntity> in = map.get(OutsourceEnum.NO.getValue());
        vo.setInsourceCount(in == null ? 0 : in.size());
        List<MaintenanceOrderEntity> out = map.get(OutsourceEnum.YES.getValue());
        vo.setOutsourceCount(out == null ? 0 : out.size());
        return Result.ok(vo);
    }

    @Override
    public Result summary(TenantIsolation tenantIsolation) {
        Integer idType = tenantIsolation.getIdType();
        Long tenantId = tenantIsolation.getTenantId();
        List<MaintenanceOrderEntity> maintenanceOrderList = this.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                .eq(DictConstant.ID_TYPE_CUSTOMER.equals(idType), MaintenanceOrderEntity::getTenantId, tenantId)
                .eq(DictConstant.ID_TYPE_SUPPLIER.equals(idType), MaintenanceOrderEntity::getSupplierId, tenantId)
                .notIn(MaintenanceOrderEntity::getStatus, Arrays.asList(OrderStatusEnum.ACCEPTED.getValue(),
                        OrderStatusEnum.REJECT.getValue(), OrderStatusEnum.REVOKE.getValue())));

        MaintenanceOrderSummaryVo vo = new MaintenanceOrderSummaryVo();
        vo.setProcessingCount(maintenanceOrderList.size());

        Map<Integer, List<MaintenanceOrderEntity>> map = maintenanceOrderList.stream().collect(Collectors.groupingBy(MaintenanceOrderEntity::getStatus));

        List<MaintenanceOrderEntity> waitDispatch = map.get(OrderStatusEnum.WAIT_DISPATCH.getValue());
        vo.setWaitDispatchCount(waitDispatch == null ? 0 : waitDispatch.size());
        List<MaintenanceOrderEntity> maintenance = map.get(OrderStatusEnum.EXECUTING.getValue());
        vo.setMaintenanceCount(maintenance == null ? 0 : maintenance.size());
        List<MaintenanceOrderEntity> waitAccept = map.get(OrderStatusEnum.WAIT_ACCEPT.getValue());
        vo.setWaitAcceptCount(waitAccept == null ? 0 : waitAccept.size());

        Map<Integer, List<MaintenanceOrderEntity>> sourceMap = maintenanceOrderList.stream().collect(Collectors.groupingBy(MaintenanceOrderEntity::getOutsource));
        List<MaintenanceOrderEntity> in = sourceMap.get(OutsourceEnum.NO.getValue());
        vo.setInsourceCount(in == null ? 0 : in.size());
        List<MaintenanceOrderEntity> out = sourceMap.get(OutsourceEnum.YES.getValue());
        vo.setOutsourceCount(out == null ? 0 : out.size());

        return Result.ok(vo);
    }

    @Override
    public Result<Map<String, Long>> countOrderByDate(TenantIsolation tenantIsolation, LocalDate begin, LocalDate end) {
        //查询建立关系的租户
        List<Long> customerIds = new ArrayList<>();
        if (IdTypeEnum.SUPPLIER.getValue().equals(tenantIsolation.getIdType())) {
            Result<List<CustomerRelationEntity>> result = customerRelationService.listTenantCustomer(tenantIsolation);
            if (result.getSignal()) {
                customerIds = result.getResult().stream().map(CustomerRelationEntity::getCustomerId).collect(Collectors.toList());
            }
            if (CollectionUtil.isEmpty(customerIds)) {
                return Result.ok(new HashMap<>());
            }
        }

        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.of(0, 0, 0));
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.of(23, 59, 59));
        if(beginTime.isAfter(endTime)){
            return Result.error("开始时间不能大于结束时间");
        }
        List<MaintenanceOrderEntity> maintenanceOrderList = mapper.listOrderByDate(tenantIsolation.getTenantId(),
                tenantIsolation.getIdType(), beginTime, endTime, customerIds);
        if(maintenanceOrderList == null){
            maintenanceOrderList = new ArrayList<>();
        }
        Map<String, Long> countMap = maintenanceOrderList.stream().collect(Collectors.groupingBy(t -> {
            LocalDateTime planInspectTime = t.getPlanMaintenanceTime();
            return yyyyMMdd.format(planInspectTime);
        }, Collectors.counting()));

        // if(countMap == null){
        //     countMap = new HashMap<>();
        // }
        // LocalDateTime tmp = beginTime;
        // while(tmp.isBefore(endTime)){
        //     String s = yyyyMMdd.format(tmp);
        //     if(!countMap.containsKey(s)){
        //         countMap.put(s, 0L);
        //     }
        //     tmp = tmp.plusDays(1);
        // }
        
        return Result.ok(countMap);
    }

    @Override
    public Result<Page<MaintenanceOrderVo>> pageOrderByDevice(TenantIsolation tenantIsolation, QueryMaintenanceOrderDto dto, Page<MaintenanceOrderVo> page) {
        if (dto.getDeviceId() == null || dto.getOutsource() == null) {
            return Result.error("设备ID或委外状态不能为空");
        }
        Page<MaintenanceOrderVo> voPage = mapper.pageOrderByDevice(dto, page);
        return Result.ok(voPage);
    }

    @Override
    public Result uploadItemFile(TenantIsolation tenantIsolation, MaintenanceItemDto dto) {
        MaintenanceItemEntity entity = maintenanceItemService.getById(dto.getId());
        if (entity == null) {
            return Result.error("保养明细不存在");
        }
        if (CollectionUtil.isNotEmpty(dto.getMaintenanceItemImages())) {
            dto.getMaintenanceItemImages().forEach(item -> {
                item.setBizId(dto.getId());
                item.setBizType(FileBizTypeEnum.MAINTENANCE_ITEM_PICTURE.getValue());
                item.setTenantId(tenantIsolation.getTenantId());
                FileEntity fileEntity = BeanUtil.copyProperties(item, FileEntity.class);
                fileMapper.insert(fileEntity);
            });
        }
        return Result.ok();
    }

    @Override
    public Result getMyCustomerAndSupplierCount(TenantIsolation tenantIsolation) {
        CustomerCountVo vo = new CustomerCountVo();
        Result<List<CustomerRelationEntity>> customerList = customerRelationService.listTenantCustomer(tenantIsolation);
        Result<List<CustomerRelationEntity>> surpplierList = customerRelationService.listTenantSupplier(tenantIsolation);
        if (customerList.getSignal()){
            vo.setCustomerCount(customerList.getResult().size());
        }
        if (surpplierList.getSignal()){
            vo.setSupplierCount(surpplierList.getResult().size());
        }
        return Result.ok(vo);
    }

    @Override
    public Result summaryCustomerOrder(TenantIsolation tenantIsolation, Integer type) {
        //type 1:近一周 2:近15天 3:近30天
        if (type == null){
            return Result.error("查询范围不能为空");
        }
        LocalDate beginTime = null;
        LocalDate endTime = LocalDate.now();
        if (type == 1){
            //近一周
            beginTime = LocalDate.now().minusDays(6);
        } else if (type == 2){
            //近一个月
            beginTime = LocalDate.now().minusDays(14);
        } else if (type == 3){
            //近一个季度
            beginTime = LocalDate.now().minusMonths(1);;
        } else {
            return Result.error("请传入正确的查询范围");
        }

        List<MaintenanceOrderEntity> maintenanceOrderList = this.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                .eq(MaintenanceOrderEntity::getSupplierId, tenantIsolation.getTenantId())
                .eq(MaintenanceOrderEntity::getOutsource, OutsourceEnum.YES.getValue())
                .in(MaintenanceOrderEntity::getStatus, Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(),
                        OrderStatusEnum.WAIT_RECEIVE.getValue(), OrderStatusEnum.EXECUTING.getValue(),
                        OrderStatusEnum.WAIT_ACCEPT.getValue()))
                .ge(MaintenanceOrderEntity::getPlanMaintenanceTime, LocalDateTime.of(beginTime, LocalTime.of(0, 0, 0)))
                .le(MaintenanceOrderEntity::getPlanMaintenanceTime, LocalDateTime.of(endTime, LocalTime.of(23, 59, 59)))
                .eq(MaintenanceOrderEntity::getDeleted, 0));

        if(maintenanceOrderList == null){
            maintenanceOrderList = new ArrayList<>();
        }

        MaintenanceOrderSummaryVo vo = new MaintenanceOrderSummaryVo();
        vo.setProcessingCount(maintenanceOrderList.size());

        Map<Integer, List<MaintenanceOrderEntity>> map = maintenanceOrderList.stream().collect(Collectors.groupingBy(MaintenanceOrderEntity::getStatus));

        List<MaintenanceOrderEntity> waitDispatch = map.get(OrderStatusEnum.WAIT_DISPATCH.getValue());
        vo.setWaitDispatchCount(waitDispatch == null ? 0 : waitDispatch.size());
        List<MaintenanceOrderEntity> waitReceive = map.get(OrderStatusEnum.WAIT_RECEIVE.getValue());
        vo.setWaitReceiveCount(waitReceive == null ? 0 : waitReceive.size());
        List<MaintenanceOrderEntity> maintenance = map.get(OrderStatusEnum.EXECUTING.getValue());
        vo.setMaintenanceCount(maintenance == null ? 0 : maintenance.size());
        List<MaintenanceOrderEntity> waitAccept = map.get(OrderStatusEnum.WAIT_ACCEPT.getValue());
        vo.setWaitAcceptCount(waitAccept == null ? 0 : waitAccept.size());

        return Result.ok(vo);
    }

    @Override
    public Result<Page<MaintenanceOrderVo>> pageMaintenanceOrderPMO(PMOPageOrderDto dto, Page<MaintenanceOrderVo> page) {
        QueryMaintenanceOrderDto queryDto = new QueryMaintenanceOrderDto();
        queryDto.setIdType(IdTypeEnum.CUSTOMER.getValue());
        if (dto.getLastModifyStartDate() != null) {
            queryDto.setLastModifyStartDate(LocalDateTime.of(dto.getLastModifyStartDate(), LocalTime.of(0, 0, 0)));
        }
        if (dto.getLastModifyEndDate() != null) {
            queryDto.setLastModifyEndDate(LocalDateTime.of(dto.getLastModifyEndDate(), LocalTime.of(23, 59, 59)));
        }
        if (dto.getTenantId() != null) {
            queryDto.setTenantId(dto.getTenantId());
        }
        Page<MaintenanceOrderVo> voPage = mapper.pageMaintenanceOrder(queryDto, page);
        return Result.ok(voPage);
    }

    @Override
    public Result<DeviceMaintenanceResultVo> getDeviceMaintenanceResult(TenantIsolation tenantIsolation, Long maintenanceOrderId, Long deviceId) {
        // 查出保养结果列表
        MaintenanceResultEntity maintenanceResult = maintenanceResultMapper.getByOrderDeviceId(maintenanceOrderId, deviceId);
        DeviceMaintenanceResultVo vo = new DeviceMaintenanceResultVo();
        if(maintenanceResult == null){
            vo.setItemList(new ArrayList<>());
            return Result.ok(vo);
        }
        Long maintenanceResultId = maintenanceResult.getId();
        // 查出保养结果图片
        List<FileVo> resultFileList = fileMapper.listByBizIds(
                FileBizTypeEnum.MAINTENANCE_RESULT_PICTURE.getValue(),
                new ArrayList<Long>(){{add(maintenanceResultId);}}
        );
        vo.setResultFileList(resultFileList);
        // 查出保养结果项（关联标准项）
        List<DeviceMaintenanceItemDto> maintenanceItemList = maintenanceResultItemMapper.listByResultIdWithDetail(maintenanceResultId);
        // 查出保养结果项标准附图
        if(maintenanceItemList != null && maintenanceItemList.size() > 0){
            List<Long> bizIds = maintenanceItemList.stream().map(DeviceMaintenanceItemDto::getMaintenanceStandardItemId).collect(Collectors.toList());
            if(bizIds != null && bizIds.size() > 0){
                List<FileVo> fileList = fileMapper.listByBizIds(FileBizTypeEnum.MAINTENANCE_STANDARD_ITEM_PICTURE.getValue(), bizIds);
                if(fileList != null && fileList.size() > 0){
                    // bizId -> fileList
                    Map<Long, List<FileVo>> map = fileList.stream().collect(Collectors.groupingBy(FileVo::getBizId));
                    maintenanceItemList.forEach(t -> {
                        Long bizId = t.getMaintenanceStandardItemId();
                        List<FileVo> list = map.get(bizId);
                        t.setStandardFileList(list);
                    });
                }
            }
        }
        // 查出保养结果项检查图片
        if(maintenanceItemList != null && maintenanceItemList.size() > 0){
            List<Long> bizIds = maintenanceItemList.stream().map(DeviceMaintenanceItemDto::getId).collect(Collectors.toList());
            if(bizIds != null && bizIds.size() > 0){
                List<FileVo> fileList = fileMapper.listByBizIds(FileBizTypeEnum.MAINTENANCE_RESULT_ITEM_PICTURE.getValue(), bizIds);
                if(fileList != null && fileList.size() > 0){
                    // bizId -> fileList
                    Map<Long, List<FileVo>> map = fileList.stream().collect(Collectors.groupingBy(FileVo::getBizId));
                    maintenanceItemList.forEach(t -> {
                        Long bizId = t.getId();
                        List<FileVo> list = map.get(bizId);
                        t.setMaintenanceFileList(list);
                    });
                }
            }
        }
        vo.setItemList(maintenanceItemList);
        vo.setMaintenanceBegin(maintenanceResult.getMaintenanceBegin());
        vo.setMaintenanceEnd(maintenanceResult.getMaintenanceEnd());
        return Result.ok(vo);
    }

    @Override
    @Transactional
    public Result submitDeviceResult(TenantIsolation tenantIsolation, MaintenanceResultDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        //工单是否存在
        Long maintenanceOrderId = dto.getMaintenanceOrderId();
        MaintenanceOrderEntity orderEntity = mapper.selectById(maintenanceOrderId);
        if(orderEntity == null){
            return Result.error("找不到工单，maintenanceOrderId:" + maintenanceOrderId);
        }
        if(!tenantId.equals(orderEntity.getTenantId()) && !tenantId.equals(orderEntity.getSupplierId())){
            return Result.error("找不到工单");
        }
        Long deviceId = dto.getDeviceId();
        // 保养工单结果是否存在
        MaintenanceResultEntity maintenanceResultEntity = maintenanceResultMapper.getByOrderDeviceId(maintenanceOrderId, deviceId);
        if(maintenanceResultEntity == null){
            return Result.error("找不到工单结果，maintenanceOrderId:" + maintenanceOrderId + ", deviceId:" + deviceId);
        }
        // 校验开始时间和结束时间
        LocalDateTime inspectBegin = dto.getMaintenanceBegin();
        if(inspectBegin == null){
            return Result.error("保养开始时间不能为空");
        }
        LocalDateTime inspectEnd = dto.getMaintenanceEnd();
        if(inspectEnd == null){
            return Result.error("保养结束时间不能为空");
        }
        if(inspectEnd.isBefore(inspectBegin)){
            return Result.error("保养结束时间不能小于开始时间");
        }
        // 更新工单结果
        maintenanceResultEntity.setStatus(MaintenanceResultStatusEnum.MAINTENANCE.getValue());
        maintenanceResultEntity.setMaintenanceBegin(dto.getMaintenanceBegin());
        maintenanceResultEntity.setMaintenanceEnd(dto.getMaintenanceEnd());
        maintenanceResultEntity.setIsError(dto.getIsError());
        maintenanceResultMapper.updateById(maintenanceResultEntity);

        // 更新保养结果项
        // 查询出所有结果项
        Long maintenanceResultId = maintenanceResultEntity.getId();
        List<MaintenanceResultItemEntity> itemEntityList = maintenanceResultItemMapper.listByResultId(maintenanceResultId);
        // 提交结果转map
        // maintenanceResultItemId -> itemDto
        Map<Long, MaintenanceResultItemDto> submitItemMap = new HashMap<>();
        List<MaintenanceResultItemDto> itemList = dto.getItemList();
        if(itemList != null){
            itemList.forEach(t -> {
                submitItemMap.put(t.getId(), t);
            });
        }
        // 每个结果项都要有对应的提交
        if(itemEntityList != null){
            for(MaintenanceResultItemEntity t:itemEntityList){
                Long maintenanceResultItemId = t.getId();
                MaintenanceResultItemDto itemDto = submitItemMap.get(maintenanceResultItemId);
                if(itemDto == null){
                    Long maintenanceStandardItemId = t.getMaintenanceStandardItemId();
                    MaintenanceStandardItemEntity standardItem = maintenanceStandardItemMapper.selectById(maintenanceStandardItemId);
                    throw new BizException("缺少保养项，" + standardItem.getPosition());
                }
                List<FileVo> fileList = itemDto.getFileList();
                // 删除旧照片
                Long bizId = t.getId();
                fileMapper.deleteByBizId(FileBizTypeEnum.MAINTENANCE_RESULT_ITEM_PICTURE.getValue(), bizId);
                // 新照片入库
                if(fileList != null && fileList.size() > 0){
                    fileList.forEach(f -> {
                        FileEntity e = new FileEntity();
                        BeanUtils.copyProperties(f, e);
                        e.setBizId(bizId);
                        e.setTenantId(tenantId);
                        e.setBizType(FileBizTypeEnum.MAINTENANCE_RESULT_ITEM_PICTURE.getValue());
                        fileMapper.insert(e);
                    });
                }
                t.setStatus(itemDto.getStatus());
                t.setRemark(itemDto.getRemark());
                t.setIsError(itemDto.getIsError());
                maintenanceResultItemMapper.updateById(t);
            }
        }

        return Result.ok();
    }

    @Override
    public Result submitResultFile(TenantIsolation tenantIsolation, MaintenanceResultFileDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        Long maintenanceResultId = dto.getMaintenanceResultId();
        MaintenanceResultEntity entity =  maintenanceResultMapper.selectById(maintenanceResultId);
        if(entity == null){
            return Result.error("找不到工单结果");
        }
        Long maintenanceOrderId = entity.getMaintenanceOrderId();
        MaintenanceOrderEntity orderEntity = this.getById(maintenanceOrderId);
        if(!tenantId.equals(orderEntity.getTenantId()) && !tenantId.equals(orderEntity.getSupplierId())){
            return Result.error("找不到工单");
        }

        Long bizId = maintenanceResultId;

        // 插入新文件
        List<FileVo> fileList = dto.getFileList();
        if(fileList != null && fileList.size() > 0){
            fileList.forEach(f -> {
                FileEntity e = new FileEntity();
                BeanUtils.copyProperties(f, e);
                e.setBizId(bizId);
                e.setTenantId(tenantId);
                e.setBizType(FileBizTypeEnum.MAINTENANCE_RESULT_PICTURE.getValue());
                fileMapper.insert(e);
            });
        }

        return Result.ok();
    }

    @Override
    public Result submitOrderFile(TenantIsolation tenantIsolation, MaintenanceOrderFileDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        Long maintenanceOrderId = dto.getMaintenanceOrderId();
        MaintenanceOrderEntity entity = this.getById(maintenanceOrderId);
        if(!tenantId.equals(entity.getTenantId()) && !tenantId.equals(entity.getSupplierId())){
            return Result.error("找不到工单");
        }

        Long bizId = maintenanceOrderId;

        // 插入新文件
        List<FileVo> fileList = dto.getFileList();
        if(fileList != null && fileList.size() > 0){
            fileList.forEach(f -> {
                FileEntity e = new FileEntity();
                BeanUtils.copyProperties(f, e);
                e.setBizId(bizId);
                e.setTenantId(tenantId);
                e.setBizType(FileBizTypeEnum.MAINTENANCE_ORDER_PICTURE.getValue());
                fileMapper.insert(e);
            });
        }

        return Result.ok();
    }

    @Override
    public Result submitMaintenanceOutboundOrder(TenantIsolation tenantIsolation, MaintenanceOutboundOrderDto dto) {
        Long maintenanceOrderId = dto.getMaintenanceOrderId();
        MaintenanceOrderEntity entity = this.getById(maintenanceOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        List<MaintenanceOutboundOrderDto.WarehouseSparesDto> warehouseSparesList = dto.getWarehouseSparesList();
        if(warehouseSparesList == null || warehouseSparesList.size() <= 0){
            return Result.ok();
        }
        for(MaintenanceOutboundOrderDto.WarehouseSparesDto warehouseSpares:warehouseSparesList){
            Long sparesWarehouseId = warehouseSpares.getSparesWarehouseId();
            // todo调用出库接口
        }

        return Result.ok();
    }

    /**
     * 工单超时监测
     * @return
     */
    @Override
    @Transactional
    public Result orderTimeoutMonitor() {
        //查询所有未结束、未超时的保养工单
        List<MaintenanceOrderEntity> orderList = this.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                .eq(MaintenanceOrderEntity::getTimeout, TimeoutEnum.NORMAL.getValue())
                .and(wrapper -> wrapper.eq(MaintenanceOrderEntity::getOutsourceCustomerType, SupplierTypeEnum.TENANT.getValue())
                        .or().isNull(MaintenanceOrderEntity::getOutsourceCustomerType))
                .in(MaintenanceOrderEntity::getStatus, Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(),
                        OrderStatusEnum.WAIT_RECEIVE.getValue(), OrderStatusEnum.EXECUTING.getValue(),
                        OrderStatusEnum.WAIT_ACCEPT.getValue())));
        if (CollectionUtil.isEmpty(orderList)) {
            return Result.ok();
        }

        // 分别获取供应商和客户创建的订单
        Map<Long, List<MaintenanceOrderEntity>> supplierOrderMap = orderList.stream()
                .filter(order -> IdTypeEnum.SUPPLIER.getValue().equals(order.getCreateSource()))
                .collect(Collectors.groupingBy(MaintenanceOrderEntity::getSupplierId));

        Map<Long, List<MaintenanceOrderEntity>> customerOrderMap = orderList.stream()
                .filter(order -> IdTypeEnum.CUSTOMER.getValue().equals(order.getCreateSource()))
                .collect(Collectors.groupingBy(MaintenanceOrderEntity::getTenantId));

        // 合并所有需要处理的租户ID
        Set<Long> tenantIds = new HashSet<>();
        tenantIds.addAll(supplierOrderMap.keySet());
        tenantIds.addAll(customerOrderMap.keySet());

        //获取租户保养超时配置(启用)
        List<TimeoutConfigEntity> allTimeoutConfigList = timeoutConfigService.list(Wrappers.<TimeoutConfigEntity>lambdaQuery()
                .eq(TimeoutConfigEntity::getOrderType, OrderTypeEnum.MAINTENANCE.getValue())
                .eq(TimeoutConfigEntity::getStatus, YesNoEnum.YES.getValue())
                .in(TimeoutConfigEntity::getTenantId, tenantIds));
        Map<Long, List<TimeoutConfigEntity>> tenantTimeoutMap = allTimeoutConfigList.stream()
                .collect(Collectors.groupingBy(TimeoutConfigEntity::getTenantId));

        LocalDateTime now = LocalDateTime.now();

        //遍历租户超时配置，判断工单是否超时
        tenantTimeoutMap.forEach((tenantId, tenantTimeoutList) -> {
            if (CollectionUtil.isEmpty(tenantTimeoutList)) {
                return;
            }
            // 获取该租户作为供应商的订单
            List<MaintenanceOrderEntity> supplierOrders = supplierOrderMap.getOrDefault(tenantId, new ArrayList<>());
            // 获取该租户作为客户的订单
            List<MaintenanceOrderEntity> customerOrders = customerOrderMap.getOrDefault(tenantId, new ArrayList<>());

            // 合并订单列表进行处理
            List<MaintenanceOrderEntity> tenantOrderList = new ArrayList<>();
            tenantOrderList.addAll(supplierOrders);
            tenantOrderList.addAll(customerOrders);
            if (CollectionUtil.isEmpty(tenantOrderList)) {
                return;
            }
            handle(tenantTimeoutList, tenantOrderList, now);
        });
        return Result.ok();
    }

    private void handle(List<TimeoutConfigEntity> timeoutConfigList, List<MaintenanceOrderEntity> tenantOrderList,
                        LocalDateTime now) {
        Map<Integer, List<MaintenanceOrderEntity>> sourceOrderMap = tenantOrderList.stream()
                .collect(Collectors.groupingBy(MaintenanceOrderEntity::getOutsource));
        timeoutConfigList.forEach(timeoutConfig -> {
            Integer timeoutSecond = timeoutConfig.getTimeoutSecond();
            Integer configType = timeoutConfig.getConfigType();
            Long tenantId = timeoutConfig.getTenantId();
            Integer idType = timeoutConfig.getIdType();

            //获取工单通知渠道
            List<MessageStrategyConfigVo> messageNoticeConfigList = messageNoticeStrategyMapper.queryOrderTimeoutStrategyConfig(
                    timeoutConfig.getTenantId(), OrderTypeEnum.MAINTENANCE.getValue(), configType, idType);

            List<MaintenanceOrderEntity> orderList = Lists.newArrayList();
            if (TimeoutConfigTypeEnum.INSIDE.getValue().equals(configType)) {
                //内部工单
                if (sourceOrderMap.get(OutsourceEnum.NO.getValue()) != null) {
                    orderList = sourceOrderMap.get(OutsourceEnum.NO.getValue());
                }
            } else if (TimeoutConfigTypeEnum.OUTSIDE.getValue().equals(configType)) {
                //外委工单
                if(sourceOrderMap.get(OutsourceEnum.YES.getValue()) != null) {
                    // 根据idType筛选createSource
                    orderList = sourceOrderMap.get(OutsourceEnum.YES.getValue()).stream()
                            .filter(order -> order.getCreateSource().equals(idType))
                            .collect(Collectors.toList());
                }
            }
            //超时判断
            orderList.forEach(order -> {
                LocalDateTime createTime = order.getCreateTime();
                //超时时间
                LocalDateTime timeoutTime = createTime.plusSeconds(timeoutSecond);
                if (now.isAfter(timeoutTime)) {
                    //设置超时
                    Integer count = mapper.orderTimeout(order.getId());

                    // 保存告警记录
                    saveAlramRecord(order, now, tenantId, idType);

                    //发送通知
                    if (CollectionUtil.isEmpty(messageNoticeConfigList)) {
                        return;
                    }
                    messageNoticeConfigList.forEach(messageNoticeConfig -> sendMessageNotice(order, messageNoticeConfig, now));
                }
            });
        });
    }

    private void saveAlramRecord(MaintenanceOrderEntity maintenanceOrder, LocalDateTime alarmTime,
                                 Long tenantId, Integer idType) {
        AlarmRecordEntity alarmRecord = new AlarmRecordEntity();
        alarmRecord.setOrderId(maintenanceOrder.getId());
        alarmRecord.setAlarmType(AlarmTypeEnum.ORDER_TIMEOUT.getValue());
        alarmRecord.setAlarmTime(alarmTime);
        alarmRecord.setOrderNo(maintenanceOrder.getOrderNumber());
        alarmRecord.setOrderCustomerId(maintenanceOrder.getTenantId());
        alarmRecord.setOrderSource(OrderTypeEnum.MAINTENANCE.getValue());
        alarmRecord.setOrderStatus(maintenanceOrder.getStatus());
        alarmRecord.setTenantId(tenantId);
        alarmRecord.setIdType(idType);
        alarmRecord.setCreatorId(1L);
        alarmRecord.setCreator("定时任务");
        Result saveAlarmResult = alarmRecordService.saveAlarmRecord(alarmRecord);
        if (!saveAlarmResult.getSignal()) {
            log.error("保存保养工单超时告警记录失败：{} {}", maintenanceOrder.getId(), saveAlarmResult.getMessage());
        }
    }

    private void sendMessageNotice(MaintenanceOrderEntity maintenanceOrder, MessageStrategyConfigVo messageNoticeConfig, LocalDateTime now) {
        Integer idType = messageNoticeConfig.getIdType();
        String customerName = "";
        if (IdTypeEnum.SUPPLIER.getValue().equals(idType) && IdTypeEnum.SUPPLIER.getValue().equals(maintenanceOrder.getCreateSource())) {
            customerName = maintenanceOrder.getCustomerName();
        }

        OrderMessageDto orderMessageDto = OrderMessageDto.builder()
                .orderTypeEnum(OrderTypeEnum.MAINTENANCE)
                .orderStatusEnum(OrderStatusEnum.typeOfValue(maintenanceOrder.getStatus()))
                .messageTypeEnum(MessageTypeEnum.ORDER_TIMEOUT)
                .timeoutEnum(TimeoutEnum.TIMEOUT)
                .orderNo(maintenanceOrder.getOrderNumber())
                .outsource(maintenanceOrder.getOutsource())
                .tenantId(messageNoticeConfig.getTenantId())
                .idType(messageNoticeConfig.getIdType())
                .customerName(customerName)
                .now(now)
                .build();

        asyncSendNoticeService.sendOrderTimeoutNotice(messageNoticeConfig, orderMessageDto);

    }

    @Override
    @Transactional
    public Result orderOverdueMonitor() {
        List<MaintenanceOrderEntity> orderList = this.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                .eq(MaintenanceOrderEntity::getOverdue, OverdueEnum.NO.getValue())
                .in(MaintenanceOrderEntity::getStatus, Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(), OrderStatusEnum.EXECUTING.getValue())));
        if (CollectionUtil.isEmpty(orderList)) {
            return Result.ok();
        }
        LocalDate now = LocalDate.now();
        List<MaintenanceOrderEntity> overdueList = new ArrayList<>();
        orderList.forEach(order -> {
            if (order.getPlanMaintenanceTime() != null) {
                LocalDate planMaintenanceDate = order.getPlanMaintenanceTime().toLocalDate();
                if (planMaintenanceDate != null) {
                    Long daysBetween = ChronoUnit.DAYS.between(now, planMaintenanceDate);
                    if (daysBetween < 0) {
                        order.setOverdue(OverdueEnum.YES.getValue());
                        overdueList.add(order);
                    }
                }
            }
        });
        if (CollectionUtil.isNotEmpty(overdueList)) {
            this.updateBatchById(overdueList);
        }
        return Result.ok();
    }

}
