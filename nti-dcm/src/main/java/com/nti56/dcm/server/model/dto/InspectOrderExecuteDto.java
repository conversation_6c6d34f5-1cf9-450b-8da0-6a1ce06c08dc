package com.nti56.dcm.server.model.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.nti56.dcm.server.entity.WorkloadEntity;

import lombok.Data;

@Data
public class InspectOrderExecuteDto {
    
    /**
     * 工单id
     */
    private Long inspectOrderId;

    /**
     *委外执行工单时，支持的供应商id
     */
    private Long supportSupplierId;
    /**
     * 检查开始时间
     */
    private LocalDateTime inspectBegin;
    
    /**
     * 检查结束时间
     */
    private LocalDateTime inspectEnd;

    /**
     * 人员工作量列表
     */
    private List<WorkloadEntity> workloadList;
    
}
