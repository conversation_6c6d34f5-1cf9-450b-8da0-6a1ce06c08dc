package com.nti56.dcm.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.dcm.server.entity.MaintenanceStandardEntity;
import com.nti56.dcm.server.model.dto.CopyMaintenanceStandardDto;
import com.nti56.dcm.server.model.dto.MaintenanceStandardDto;
import com.nti56.dcm.server.model.vo.ImportFailInfo;
import com.nti56.dcm.server.model.vo.MaintenanceStandardVo;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 保养标准 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-28 15:37:22
 * @since JDK 1.8
 */
public interface MaintenanceStandardService extends IService<MaintenanceStandardEntity> {

    Result<Page<MaintenanceStandardVo>> getPage(TenantIsolation tenantIsolation, Page<MaintenanceStandardVo> page, MaintenanceStandardDto maintenanceStandardDto);

    Result createMaintenanceStandard(TenantIsolation tenantIsolation, MaintenanceStandardDto maintenanceStandardDto);

    Result updateMaintenanceStandard(TenantIsolation tenantIsolation, MaintenanceStandardDto maintenanceStandardDto);

    Result deleteById(TenantIsolation tenantIsolation, Long id);

    Result<MaintenanceStandardEntity> getById(TenantIsolation tenantIsolation, Long id);

    Result copy(TenantIsolation tenantIsolation, CopyMaintenanceStandardDto dto);

    Result<List<ImportFailInfo>> importStandard(TenantIsolation tenantIsolation, MaintenanceStandardDto dto, MultipartFile file) throws IOException;

    Result<List<ImportFailInfo>> importStandardCover(TenantIsolation tenantIsolation, Long standardId, MultipartFile file) throws IOException;

    void exportStandard(Long tenantId, Long standardId, HttpServletResponse response) throws IOException;

    void downloadTemplate(HttpServletResponse response, Integer type) throws IOException;

    void initTenantStandard(Long tenantId);

}
