package com.nti56.dcm.server.service;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Sets;
import com.nti56.common.constant.Constant;
import com.nti56.common.enums.ErrorEnum;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.user.sdk.request.*;
import com.nti56.user.sdk.response.ApiMenuResponse;
import com.nti56.user.sdk.response.ApiRoleMemberResponse;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.UriComponentsBuilder;

import com.nti56.dcm.server.mapper.AuthMapper;
import com.nti56.dcm.server.mapper.RoleAuthMapper;
import com.nti56.dcm.server.mapper.RoleMapper;
import com.nti56.dcm.server.mapper.UserRoleMapper;
import com.nti56.dcm.server.model.dto.SysRoleDto;
import com.nti56.dcm.server.model.dto.UserLoginInfoDto;
import com.nti56.nlink.common.util.Result;
import com.nti56.sdk.basic.response.ApiResult;
import com.nti56.user.sdk.response.ApiRoleResponse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class UserRoleAuthService {

    @Autowired
    private RoleAuthMapper roleAuthMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private AuthMapper authMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restHttpTemplate;

    @Value("${ucenter.ucenterBaseUrl}")
    private String baseUrl;
    
    private String getUserLoginInfoUri = "/moduleApi/platform/permission/auth/getUserLoginInfo";

    private String listSysRoleUri = "/moduleApi/platform/permission/sysRole/list";
    private String addRoleUri = "/moduleApi/platform/permission/sysRole/sdk/save";
    private String addMember = "/moduleApi/platform/permission/sysRole/addMember";
    private String roleMemberList = "/moduleApi/platform/permission/sysRole/memberList";

    private String updateRoleMenuUri = "/moduleApi/platform/permission/sysRole/updateRoleMenu";

    private String menuList = "/moduleApi/platform/permission/sysMenu/list";

    private static final Set<String> ALLOWED_MENUS = new HashSet<>(Arrays.asList(
            "设备监控", "设备维修", "设备保养", "设备巡检",
            "资料库", "备件管理", "基础数据", "备件配置"
    ));

    public Result<List<SysRoleDto>> listSysRole(Long tenantId){
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add("clientId", tenantId.toString());
            headers.add("appcode", Constant.APPCODE_HEADER);
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
            if (StrUtil.isNotBlank(request.getHeader(Constant.EQUIP_ID))){
                headers.add(Constant.EQUIP_ID,request.getHeader(Constant.EQUIP_ID));
            }
            if (StrUtil.isNotBlank(request.getHeader(Constant.EQUIP_TYPE))){
                headers.add(Constant.EQUIP_TYPE,request.getHeader(Constant.EQUIP_TYPE));
            }
            // 构造请求体参数
            HashMap<String, Object> params = new HashMap<>();
            params.put("clientId", tenantId);
            HttpEntity<HashMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);
            String pathUrl = baseUrl + listSysRoleUri;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            log.info("获取角色列表请求参数：{}", JSON.toJSONString(params));
            String result = restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, String.class).getBody();
            log.info("获取角色列表返回结果：{}", result);
            R<List<SysRoleDto>> rr = JSON.parseObject(result, new TypeReference<R<List<SysRoleDto>>>() {
            });
            if (rr != null && ObjectUtil.equals(rr.getCode(), 200)) {
                List<SysRoleDto> list = rr.getData();
                return Result.ok(list);
            }else{
                return Result.error("请求底座角色列表失败:" + rr.getMessage());
            }
        } catch (Exception e) {
            log.error("获取角色列表异常：{}", e.getMessage());
            return Result.error("请求底座角色列表异常");
        }

    }

    private Result<UserLoginInfoDto> getUserLoginInfo(Long tenantId, Long userId){
        
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add("clientId", tenantId.toString());
            headers.add("appcode", Constant.APPCODE_HEADER);
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
            if (StrUtil.isNotBlank(request.getHeader(Constant.EQUIP_ID))){
                headers.add(Constant.EQUIP_ID,request.getHeader(Constant.EQUIP_ID));
            }
            if (StrUtil.isNotBlank(request.getHeader(Constant.EQUIP_TYPE))){
                headers.add(Constant.EQUIP_TYPE,request.getHeader(Constant.EQUIP_TYPE));
            }
            // 构造请求体参数
            HashMap<String, Object> params = new HashMap<>();
            params.put("userCode", userId.toString());
            params.put("clientId", tenantId);
            HttpEntity<HashMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);
            String pathUrl = baseUrl + getUserLoginInfoUri;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            log.debug("获取用户角色请求参数：{}", JSON.toJSONString(params));
            String result = restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, String.class).getBody();
            log.debug("获取用户角色返回结果：{}", result);
            R<UserLoginInfoDto> rr = JSON.parseObject(result, new TypeReference<R<UserLoginInfoDto>>() {
            });
            if (rr != null && ObjectUtil.equals(rr.getCode(), 200)) {
                UserLoginInfoDto info = rr.getData();
                return Result.ok(info);
            }else{
                return Result.error(ErrorEnum.NO_AUTH.getCode(),"请求底座用户角色失败");
            }
        } catch (Exception e) {
            log.error("获取用户角色异常：{}", e.getMessage());
            return Result.error(ErrorEnum.NO_AUTH.getCode(),"请求底座用户角色异常");
        }

    }
    /**
     * 获取用户的角色
     * @param userId 用户id
     */
    public Result<Boolean> haveAuthReportRepair(Long tenantId, Long userId, Integer idType){
        Result<UserLoginInfoDto> userLoginInfoResult = getUserLoginInfo(tenantId, userId);
        if(!userLoginInfoResult.getSignal()){
            return Result.error(userLoginInfoResult.getMessage());
        }
        UserLoginInfoDto info = userLoginInfoResult.getResult();

        List<SysRoleDto> roleList = info.getRoles();

        if (IdTypeEnum.SUPPLIER.getValue().equals(idType)) {
            roleList = roleList.stream().filter(role -> StringUtils.isNotEmpty(role.getTag()) && role.getTag().contains(IdTypeEnum.SUPPLIER.getNameDesc())).collect(Collectors.toList());
        } else if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
            roleList = roleList.stream().filter(role -> StringUtils.isNotEmpty(role.getTag()) && role.getTag().equals(IdTypeEnum.CUSTOMER.getNameDesc())).collect(Collectors.toList());
        }
        Map<String, Boolean> resultMap = new HashMap<>();
        for(SysRoleDto role : roleList){
            resultMap.put(role.getRoleName(), true);
        }
        Boolean haveAuth = false;
        Boolean boolean1 = resultMap.get("设备维保班长");
        if(boolean1 != null){
            haveAuth = boolean1;
        }
        Boolean boolean2 = resultMap.get("管理员");
        if(boolean2 != null){
            haveAuth = boolean2;
        }

        return Result.ok(haveAuth);
    }
    

    /**
     * 判断当前登录用户是否是管理员
     * @param tenantIsolation
     * @return
     */
    public Boolean getCurrentUserIsSuperRole(TenantIsolation tenantIsolation){

        // 是否是超级角色
        Boolean currentUserIsAdministrator = this.getCurrentUserIsAdministrator(tenantIsolation);
        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = this.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        return currentUserIsAdministrator || isReportHandleRole;
    }
    /**
     * 判断当前登录用户是否是管理员
     * @param tenant
     * @return
     */
    public Boolean getCurrentUserIsAdministrator(TenantIsolation tenant){
        // 获取当前登录租户当前身份的管理员用户列表
        Long currentUserID = JwtUserInfoUtils.getUserId();
        Result<UserLoginInfoDto> userLoginInfoResult = getUserLoginInfo(tenant.getTenantId(), currentUserID);
        if(!userLoginInfoResult.getSignal()){
            log.error("获取userLoginInfo失败：{}", userLoginInfoResult.getMessage());
            return false;
        }
        UserLoginInfoDto info = userLoginInfoResult.getResult();

        List<SysRoleDto> roleList = info.getRoles();

        Integer idType = tenant.getIdType();
        if (IdTypeEnum.SUPPLIER.getValue().equals(idType)) {
            roleList = roleList.stream().filter(role -> StringUtils.isNotEmpty(role.getTag()) && role.getTag().contains(IdTypeEnum.SUPPLIER.getNameDesc())).collect(Collectors.toList());
        } else if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
            roleList = roleList.stream().filter(role -> StringUtils.isNotEmpty(role.getTag()) && role.getTag().equals(IdTypeEnum.CUSTOMER.getNameDesc())).collect(Collectors.toList());
        }
        Map<String, Boolean> resultMap = new HashMap<>();
        for(SysRoleDto role : roleList){
            resultMap.put(role.getRoleName(), true);
        }
        Boolean haveAuth = false;
        Boolean boolean1 = resultMap.get("管理员");
        if(boolean1 != null){
            haveAuth = boolean1;
        }
        return haveAuth;
    }

    /**
     * 判断当前登录用户是否是管理员
     * @param tenant
     * @return
     */
    public Boolean getCurrentUserIsAdministrator(Long tenantId, Integer idType){
       // 获取当前登录租户当前身份的管理员用户列表
       Long currentUserID = JwtUserInfoUtils.getUserId();
       Result<UserLoginInfoDto> userLoginInfoResult = getUserLoginInfo(tenantId, currentUserID);
       if(!userLoginInfoResult.getSignal()){
           log.error("获取userLoginInfo失败：{}", userLoginInfoResult.getMessage());
           return false;
       }
       UserLoginInfoDto info = userLoginInfoResult.getResult();
       
       List<SysRoleDto> roleList = info.getRoles();

       if (IdTypeEnum.SUPPLIER.getValue().equals(idType)) {
           roleList = roleList.stream().filter(role -> StringUtils.isNotEmpty(role.getTag()) && role.getTag().contains(IdTypeEnum.SUPPLIER.getNameDesc())).collect(Collectors.toList());
       } else if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
           roleList = roleList.stream().filter(role -> StringUtils.isNotEmpty(role.getTag()) && role.getTag().equals(IdTypeEnum.CUSTOMER.getNameDesc())).collect(Collectors.toList());
       }
       Map<String, Boolean> resultMap = new HashMap<>();
       for(SysRoleDto role : roleList){
           resultMap.put(role.getRoleName(), true);
       }
       Boolean haveAuth = false;
       Boolean boolean1 = resultMap.get("管理员");
       if(boolean1 != null){
           haveAuth = boolean1;
       }
       return haveAuth;

    }

    /**
     * 用户是否管理员
     * @param tenantId
     * @param userId
     * @return
     */
    public Result<Boolean> checkUserIsAdmin(Long tenantId, Long userId) {
        // 获取当前登录租户当前身份的管理员用户列表
       Long currentUserID = JwtUserInfoUtils.getUserId();
       Result<UserLoginInfoDto> userLoginInfoResult = getUserLoginInfo(tenantId, currentUserID);
       if(!userLoginInfoResult.getSignal()){
           log.error("获取userLoginInfo失败：{}", userLoginInfoResult.getMessage());
           return Result.error(userLoginInfoResult.getMessage());
       }
       UserLoginInfoDto info = userLoginInfoResult.getResult();
       
       List<SysRoleDto> roleList = info.getRoles();
       Map<String, Boolean> resultMap = new HashMap<>();
       for(SysRoleDto role : roleList){
           resultMap.put(role.getRoleName(), true);
       }
       Boolean haveAuth = false;
       Boolean boolean1 = resultMap.get("管理员");
       if(boolean1 != null && boolean1){
           haveAuth = true;
       }
       return Result.ok(haveAuth);
    }

    public void initCustomerRole(Long tenantId) {
        ApiRoleRequest req1 = new ApiRoleRequest();
        req1.setAppcode(Constant.APPCODE_HEADER);
        req1.setClientId(tenantId.toString());
        req1.setRoleType(Constant.ROLE_BUSSINESS_TYPE); //业务类型
        ApiResult<List<ApiRoleResponse>> listResp =listRole(req1);
        if(listResp == null){
            return;
        }
        //只初始化业主，创建业主身份的角色
        List<String> defaultRoleList = Arrays.asList(Constant.DEFAULT_ROLE_NAME_1, Constant.DEFAULT_ROLE_NAME_2/*, Constant.DEFAULT_ROLE_NAME_3, Constant.DEFAULT_ROLE_NAME_4*/);
        List<ApiRoleResponse> list = listResp.getData();
        List<String> existRoleNameList = list.stream().map(i -> i.getRoleName()).collect(Collectors.toList());
        List<String> needAddRoleNameList = defaultRoleList.stream().filter(x -> !existRoleNameList.contains(x)).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(needAddRoleNameList)){
            for (String roleName : needAddRoleNameList) {
                ApiAddOrEditRoleRequest request = new ApiAddOrEditRoleRequest();
                request.setRoleName(roleName);
                request.setTag(IdTypeEnum.CUSTOMER.getNameDesc());
                request.setAppcode(Constant.APPCODE_HEADER);
                request.setRoleType(Constant.ROLE_BUSSINESS_TYPE);
                request.setClientId(tenantId.toString());
                ApiResult<ApiRoleResponse> resp =addRole(tenantId,request);
                Boolean succeeded = resp.getSucceeded();
                if(!succeeded){
                    log.error("创建角色失败，失败信息:{}", resp.getMessage());
                    return;
                }
                Long roleId = resp.getData().getId();
                Long userId = JwtUserInfoUtils.getUserId();
                Result<Boolean> booleanResult = roleContainsUser(tenantId, roleId,userId);
                if (booleanResult.getSignal() && !booleanResult.getResult()) {
                    addUserToRole(tenantId, roleId, userId);
                }
                grantDefaultRoleMenu(tenantId, roleId,roleName);
            }
        }

    }

    private Result<Boolean> roleContainsUser(Long tenantId, Long roleId, Long userId) {
        boolean isExit = false;
        ApiResult<List<ApiRoleMemberResponse>> result = listRoleMember(tenantId, roleId);
        log.info("listRoleMember result:{}", JSON.toJSONString(result));
        if (result.getSucceeded()) {
            if (result.getData() != null) {
                isExit = result.getData().stream().filter(x -> x.getUserId().equals(userId.toString())).findFirst().isPresent();
            }
            log.info("roleContainsUser result:{}", isExit);
            return Result.ok(isExit);
        }
        log.error("roleContainsUser error:{}", result.getMessage());
        return Result.error(result.getMessage());
    }


    private Result<Boolean> addUserToRole(Long tenantId, Long roleId, Long userId) {
        ApiRoleMemberAddRequest request = new ApiRoleMemberAddRequest();
        request.setRoleId(roleId);
        Set<Long> userIds = Sets.newHashSet();
        userIds.add(userId);
        request.setUserIds(userIds);
        ApiResult<Boolean> booleanApiResult = addRoleMember(tenantId, request);
        if (booleanApiResult.getSucceeded()) {
            return Result.ok(booleanApiResult.getData());
        }
        log.error("addUserToRole error:{}", booleanApiResult.getMessage());
        return Result.error(booleanApiResult.getMessage());

    }

    private ApiResult<Boolean> addRoleMember(Long tenantId, ApiRoleMemberAddRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + addMember;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<Boolean>> responseType = new ParameterizedTypeReference<ApiResult<Boolean>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


        return ApiResult.failed("添加用户与角色的关联失败");
    }


    private ApiResult<List<ApiRoleMemberResponse>> listRoleMember(Long tenantId, Long roleId) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(headers);
            String pathUrl = baseUrl + roleMemberList + "?roleId=" + roleId;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<List<ApiRoleMemberResponse>>> responseType = new ParameterizedTypeReference<ApiResult<List<ApiRoleMemberResponse>>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.GET, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("查询角色下的用户列表失败");
    }

    public ApiResult<List<ApiRoleMemberResponse>> listRoleMemberByRoleName(Long tenantId, String roleName) {
        Result<List<SysRoleDto>> listResult = this.listSysRole(tenantId);
        if (!listResult.getSignal()) {
            return ApiResult.failed(listResult.getMessage());
        }
        List<SysRoleDto> list = listResult.getResult();
        SysRoleDto sysRoleDto = list.stream().filter(x -> x.getRoleName().equals(roleName)).findFirst().orElse(null);
        if (sysRoleDto == null) {
            return ApiResult.failed("角色不存在");
        }
        return listRoleMember(tenantId, sysRoleDto.getId());
    }





    private ApiResult<ApiRoleResponse> addRole(Long tenantId,ApiAddOrEditRoleRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + addRoleUri;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<ApiRoleResponse>> responseType = new ParameterizedTypeReference<ApiResult<ApiRoleResponse>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("添加角色失败");
    }



    private ApiResult<List<ApiRoleResponse>> listRole(ApiRoleRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, request.getClientId());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + listSysRoleUri;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<List<ApiRoleResponse>>> responseType = new ParameterizedTypeReference<ApiResult<List<ApiRoleResponse>>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("查询角色失败");
    }



    private List<Long> getAllCustomerMenu(Long tenantId, String roleName) {

        Set<Long> menuListIds = new HashSet<>();
        ApiMenuRequest request = new ApiMenuRequest();
        request.setAppcode(Constant.APPCODE_HEADER);
        ApiResult<List<ApiMenuResponse>> list = listMenu(tenantId,request);
        if (list.getSucceeded()) {
            // 先遍历一次找出父菜单ID
            Set<Long> cusAllowedParentIds = new HashSet<>();
            Set<Long> supAllowedParentIds = new HashSet<>();
            list.getData().stream()
                    .filter(menu -> Objects.nonNull(menu.getTag()) && !menu.getTag().contains("开发者"))
                    .forEach(x -> {
                        if (ALLOWED_MENUS.contains(x.getMenuName())) {
                            if ("业主".equals(x.getTag())) {
                                cusAllowedParentIds.add(x.getId());
                            }
                            if (x.getTag().contains("供应商")) {
                                supAllowedParentIds.add(x.getId());
                            }
                        }
                    });

            list.getData().forEach(x -> {
                // 管理员角色不加上标签为开发者的菜单
                if (Constant.DEFAULT_ROLE_NAME_1.equals(roleName) && "业主".equals(x.getTag())) {
                    menuListIds.add(x.getId());
                } else if (Constant.DEFAULT_ROLE_NAME_2.equals(roleName)) {
                    // 维保班组长无系统设置菜单权限
                    if (x.getTag()==null || x.getTag().contains("开发者") || x.getMenuName().equals("系统设置")) {
                        return;
                    } else if ("业主".equals(x.getTag()) &&
                            (ALLOWED_MENUS.contains(x.getMenuName()) || cusAllowedParentIds.contains(x.getPId()))) {
                        menuListIds.add(x.getId());
                    }
                }else{
                    menuListIds.add(x.getId());
                }
            });
        }
        return menuListIds.stream().collect(Collectors.toList());
    }
    private ApiResult<List<ApiMenuResponse>> listMenu(Long tenantId,ApiMenuRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + menuList;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<List<ApiMenuResponse>>> responseType = new ParameterizedTypeReference<ApiResult<List<ApiMenuResponse>>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


        return ApiResult.failed("查询菜单列表失败");

    }


    private Result<Boolean> grantDefaultRoleMenu(Long tenantId, Long roleId, String roleName) {
        List<Long> allMenuIds = getAllCustomerMenu(tenantId,roleName);
        ApiUpdateRoleMenuRequest request = new ApiUpdateRoleMenuRequest();
        request.setRoleId(roleId);
        request.setAppcode(Constant.APPCODE_HEADER);
        request.setClientId(tenantId.toString());
        request.setMenus(getRoleMenu(tenantId, roleId, allMenuIds));
        request.setMenuCategory(Constant.MENU_CATEGORY);
        ApiResult<Boolean> booleanApiResult = updateRoleMenu(tenantId,request);
        if (booleanApiResult.getSucceeded()) {
            log.info("为默认角色授权成功，角色id:{} ,菜单id列表:{}", roleId, JSON.toJSONString(allMenuIds));
            return Result.ok(booleanApiResult.getData());
        }
        log.error("grantDefaultRoleMenu error:{}", booleanApiResult.getMessage());
        return Result.error(booleanApiResult.getMessage());
    }


    private List<ApiUpdateRoleMenuRequest.SysRoleMenuDTO> getRoleMenu(Long tenantId, Long roleId, List<Long> menuIds) {
        List<ApiUpdateRoleMenuRequest.SysRoleMenuDTO> result = Lists.newArrayList();
        menuIds.forEach(x -> {
            ApiUpdateRoleMenuRequest.SysRoleMenuDTO dto = new ApiUpdateRoleMenuRequest.SysRoleMenuDTO();
            dto.setMenuId(x);
            dto.setRoleId(roleId);
            dto.setAppcode(Constant.APPCODE_HEADER);
            dto.setClientId(tenantId.toString());
            result.add(dto);
        });
        return result;
    }
    private ApiResult<Boolean> updateRoleMenu(Long tenantId, ApiUpdateRoleMenuRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + updateRoleMenuUri;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<String>> responseType = new ParameterizedTypeReference<ApiResult<String>>() {
            };
            ApiResult<String> result = restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
            if(result.getSucceeded()&&"success".equalsIgnoreCase(result.getData())){
                return ApiResult.succeed(true);
            }
            log.error("updateRoleMenu failure errMsg:{}", result.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("更新角色的菜单权限失败");
    }

}
