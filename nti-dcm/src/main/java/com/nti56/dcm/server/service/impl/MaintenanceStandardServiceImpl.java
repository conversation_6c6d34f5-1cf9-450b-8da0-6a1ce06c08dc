package com.nti56.dcm.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nti56.dcm.server.domain.MaintenanceStandard;
import com.nti56.dcm.server.domain.enums.FileBizTypeEnum;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.domain.enums.StandardTypeEnum;
import com.nti56.dcm.server.entity.*;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.mapper.FileMapper;
import com.nti56.dcm.server.mapper.MaintenanceStandardItemMapper;
import com.nti56.dcm.server.mapper.MaintenanceStandardMapper;
import com.nti56.dcm.server.model.dto.CopyMaintenanceStandardDto;
import com.nti56.dcm.server.model.dto.MaintenanceStandardDto;
import com.nti56.dcm.server.model.dto.MaintenanceStandardItemImportDto;
import com.nti56.dcm.server.model.vo.*;
import com.nti56.dcm.server.service.*;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 保养标准 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-28 15:37:22
 * @since JDK 1.8
 */
@Slf4j
@Service
public class MaintenanceStandardServiceImpl extends ServiceImpl<MaintenanceStandardMapper, MaintenanceStandardEntity>
        implements MaintenanceStandardService {

    @Autowired
    private SerialNumberService serialNumberService;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Autowired
    private MaintenanceStandardItemService maintenanceStandardItemService;

    @Autowired
    private FileMapper fileMapper;

    @Autowired
    private FileService fileService;

    @Autowired
    private MaintenanceStandardMapper mapper;

    @Autowired
    private MaintenanceStandardItemMapper standardItemMapper;

    @Autowired
    private IDeviceBomService deviceBomService;

    @Autowired
    private IMaintenanceStandardModuleService maintenanceStandardModuleService;

    @Override
    public Result<Page<MaintenanceStandardVo>> getPage(TenantIsolation tenantIsolation, Page<MaintenanceStandardVo> page,
                                                       MaintenanceStandardDto dto) {
        dto.setIdType(tenantIsolation.getIdType());
        dto.setTenantId(tenantIsolation.getTenantId());
        Page<MaintenanceStandardVo> pageResult = mapper.pageMaintenanceStandard(page, dto);

        List<MaintenanceStandardVo> maintenanceStandardVoList = Lists.newArrayList();
        List<MaintenanceStandardVo> maintenanceStandardList = pageResult.getRecords();
        if (CollectionUtil.isNotEmpty(maintenanceStandardList)) {
            List<MaintenanceStandardItemEntity> maintenanceStandardItemList = maintenanceStandardItemService.list(Wrappers.<MaintenanceStandardItemEntity>lambdaQuery()
                    .in(MaintenanceStandardItemEntity::getMaintenanceStandardId,
                            maintenanceStandardList.stream().map(MaintenanceStandardEntity::getId).collect(Collectors.toList()))
                    .eq(MaintenanceStandardItemEntity::getTenantId, tenantIsolation.getTenantId())
                    .eq(MaintenanceStandardItemEntity::getDeleted, 0));
            Map<Long, List<MaintenanceStandardItemEntity>> itemMap = maintenanceStandardItemList.stream()
                    .collect(Collectors.groupingBy(MaintenanceStandardItemEntity::getMaintenanceStandardId));
            maintenanceStandardList.forEach(entity -> {
                MaintenanceStandardVo vo = BeanUtilsIntensifier.copyBean(entity, MaintenanceStandardVo.class);
                vo.setItemCount(itemMap.getOrDefault(entity.getId(), new ArrayList<>()).size());
                maintenanceStandardVoList.add(vo);
            });
        }

        pageResult.setRecords(maintenanceStandardVoList);
        return Result.ok(pageResult);
    }

    @Override
    public Result createMaintenanceStandard(TenantIsolation tenantIsolation, MaintenanceStandardDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        dto.setIdType(tenantIsolation.getIdType());
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<MaintenanceStandard> checkResult = MaintenanceStandard.checkCreate(dto, commonFetcher);
        if (checkResult.getSignal()) {
            MaintenanceStandardEntity maintenanceStandard = checkResult.getResult().getEntity();
            long id = IdGenerator.generateId();
            maintenanceStandard.setId(id);
            String orderNumber = serialNumberService.getNext(tenantId, LocalDate.now(), MaintenanceStandard.SERIALNUMBER);
            maintenanceStandard.setStandardNumber(orderNumber);
            maintenanceStandard.setIdType(tenantIsolation.getIdType());
            this.save(maintenanceStandard);
            return Result.ok();
        }
        return checkResult;
    }

    @Override
    public Result updateMaintenanceStandard(TenantIsolation tenantIsolation, MaintenanceStandardDto dto) {
        dto.setIdType(tenantIsolation.getIdType());
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<MaintenanceStandard> checkResult = MaintenanceStandard.checkUpdate(dto, commonFetcher);
        if (checkResult.getSignal()) {
            MaintenanceStandardEntity maintenanceStandard = checkResult.getResult().getEntity();
            this.updateById(maintenanceStandard);
            return Result.ok();
        }
        return checkResult;
    }

    @Override
    public Result<Void> deleteById(TenantIsolation tenantIsolation, Long id) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result checkResult = MaintenanceStandard.checkDelete(id, commonFetcher);
        if (checkResult.getSignal()) {
            this.removeById(id);
            //删除保养明细
            maintenanceStandardItemService.remove(Wrappers.<MaintenanceStandardItemEntity>lambdaQuery()
                    .eq(MaintenanceStandardItemEntity::getMaintenanceStandardId, id));
            return Result.ok();
        }
        return checkResult;
    }

    @Override
    public Result<MaintenanceStandardEntity> getById(TenantIsolation tenantIsolation, Long id) {
        MaintenanceStandardEntity maintenanceStandardEntity = this.getById(id);
        return Result.ok(maintenanceStandardEntity);
    }

    @Override
    @Transactional
    public Result copy(TenantIsolation tenantIsolation, CopyMaintenanceStandardDto dto) {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        MaintenanceStandardEntity copyStandard = this.getById(dto.getCopyStandardId());
        if (copyStandard == null) {
            return Result.error("保养标准不存在");
        }

        if (!uniqueName(tenantId, idType, dto.getStandardName())) {
            return Result.error("保养标准名称已存在");
        }

        MaintenanceStandardEntity newStandard = new MaintenanceStandardEntity();
        long newStandardId = IdGenerator.generateId();
        newStandard.setId(newStandardId);
        String orderNumber = serialNumberService.getNext(tenantId, LocalDate.now(), MaintenanceStandard.SERIALNUMBER);
        newStandard.setStandardNumber(orderNumber);
        newStandard.setStandardName(dto.getStandardName());
        newStandard.setIdType(idType);
        newStandard.setTenantId(tenantId);
        newStandard.setType(copyStandard.getType());
        newStandard.setDeviceTypeId(copyStandard.getDeviceTypeId());
        this.save(newStandard);

        List<MaintenanceStandardItemEntity> standardItemList = maintenanceStandardItemService.list(Wrappers.<MaintenanceStandardItemEntity>lambdaQuery()
                .eq(MaintenanceStandardItemEntity::getMaintenanceStandardId, dto.getCopyStandardId()));

        if (CollectionUtil.isNotEmpty(standardItemList)) {
            standardItemList.forEach(standard -> {
                MaintenanceStandardItemEntity newItem = BeanUtilsIntensifier.copyBean(standard, MaintenanceStandardItemEntity.class);
                long newItemId = IdGenerator.generateId();
                newItem.setId(newItemId);
                newItem.setMaintenanceStandardId(newStandardId);
                maintenanceStandardItemService.save(newItem);

                // 复制图片
                List<FileEntity> files = fileMapper.listByBizId(
                        FileBizTypeEnum.MAINTENANCE_STANDARD_ITEM_PICTURE.getValue(),
                        standard.getId());
                if (CollectionUtil.isNotEmpty(files)) {
                    files.forEach(t -> {
                        FileEntity e = new FileEntity();
                        BeanUtils.copyProperties(t, e);
                        e.setId(IdGenerator.generateId());
                        e.setBizId(newItemId);
                        e.setTenantId(tenantId);
                        e.setBizType(FileBizTypeEnum.MAINTENANCE_STANDARD_ITEM_PICTURE.getValue());
                        fileMapper.insert(e);
                    });
                }
            });
        }

        return Result.ok();
    }

    @Override
    @Transactional
    public Result<List<ImportFailInfo>> importStandard(TenantIsolation tenantIsolation, MaintenanceStandardDto dto, MultipartFile file) throws IOException {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();

        if (!uniqueName(tenantId, idType, dto.getStandardName())) {
            return Result.error("保养标准名称已存在");
        }

        MaintenanceStandardEntity maintenanceStandard = new MaintenanceStandardEntity();
        long id = IdGenerator.generateId();
        maintenanceStandard.setId(id);
        maintenanceStandard.setStandardName(dto.getStandardName());
        maintenanceStandard.setType(dto.getType());
        maintenanceStandard.setDeviceTypeId(dto.getDeviceTypeId());
        String orderNumber = serialNumberService.getNext(tenantId, LocalDate.now(), MaintenanceStandard.SERIALNUMBER);
        maintenanceStandard.setStandardNumber(orderNumber);
        maintenanceStandard.setIdType(idType);
        this.save(maintenanceStandard);

        List<ImportFailInfo> resultList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), MaintenanceStandardItemImportDto.class, new ReadListener<MaintenanceStandardItemImportDto>() {

            @Override
            public void invoke(MaintenanceStandardItemImportDto record, AnalysisContext context) {
                MaintenanceStandardItemEntity entity = BeanUtilsIntensifier.copyBean(record, MaintenanceStandardItemEntity.class);
                String fullBomName = entity.getFullBomName();
                if(record.getFullBomName() == null || "".equals(record.getFullBomName())){
                    resultList.add(new ImportFailInfo(fullBomName, "模块名称不能为空", "模块名称不能为空"));
                    return;
                }
                if(record.getPosition() == null || "".equals(record.getPosition())){
                    resultList.add(new ImportFailInfo(fullBomName, "项目名称不能为空", "项目名称不能为空"));
                    return;
                }

                entity.setMaintenanceStandardId(id);
                TenantIsolation tenant = new TenantIsolation();
                tenant.setTenantId(tenantId);
                tenant.setIdType(idType);
                if(StandardTypeEnum.SPECIAL.getValue().equals(dto.getType())){
                    DeviceBomEntity deviceBomEntity = deviceBomService.getBomByFullName(tenant, dto.getDeviceTypeId(), fullBomName);
                    if(deviceBomEntity == null){
                        resultList.add(new ImportFailInfo(fullBomName, "未找到匹配的部件", "未找到匹配的部件"));
                        return;
                    }
                } else if(StandardTypeEnum.COMMON.getValue().equals(dto.getType())) {
                    Long moduleId = maintenanceStandardModuleService.getModuleIdByModuleName(tenantId, id, fullBomName);
                    entity.setMaintenanceStandardModuleId(moduleId);
                }

                try {
                    maintenanceStandardItemService.save(entity);
                } catch (Exception e) {
                    resultList.add(new ImportFailInfo(fullBomName, "插入标准项异常", e.getMessage()));
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("import finish");
            }

            @Override
            public  void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                Set<String> expectedHeaders = new HashSet<>();
                expectedHeaders.add("模块名称");
                expectedHeaders.add("项目名称");
                expectedHeaders.add("备注");
                Set<String> headers = headMap.values().stream().map(ReadCellData::getStringValue).collect(Collectors.toSet());
                if(!expectedHeaders.equals(headers)){
                    throw new BizException("导入失败，请使用正确的模板导入");
                }
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) {
                if (exception instanceof ExcelDataConvertException) {
                    // 数据转换异常，可以选择记录日志或忽略
                    log.error("Error at row " + context.readRowHolder().getRowIndex() + ": " + exception.getMessage());
                    resultList.add(new ImportFailInfo("第" + context.readRowHolder().getRowIndex() + "行异常", "数据转换异常", exception.getMessage()));
                } else if (exception instanceof BizException) {
                    throw new RuntimeException(exception.getMessage());
                } else {
                    resultList.add(new ImportFailInfo("导入异常", "其他异常", exception.getMessage()));
                }
            }

        }).sheet().doRead();
        return Result.ok(resultList);
    }

    @Override
    @Transactional
    public Result<List<ImportFailInfo>> importStandardCover(TenantIsolation tenantIsolation, Long standardId, MultipartFile file) throws IOException {
        MaintenanceStandardEntity maintenanceStandard = this.getById(standardId);
        if (maintenanceStandard == null) {
            throw new BizException("导入失败，标准不存在");
        }

        List<MaintenanceStandardItemEntity> itemList = maintenanceStandardItemService.list(Wrappers.<MaintenanceStandardItemEntity>lambdaQuery()
                .eq(MaintenanceStandardItemEntity::getMaintenanceStandardId, maintenanceStandard.getId()));
        Map<String, Long> itemMap = new HashMap<>();
        itemList.forEach(item -> {
            itemMap.put((StringUtils.isEmpty(item.getFullBomName()) ? "" : item.getFullBomName()) + "-" + item.getPosition(), item.getId());
        });

        List<ImportFailInfo> resultList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), MaintenanceStandardItemImportDto.class, new ReadListener<MaintenanceStandardItemImportDto>() {

            @Override
            public void invoke(MaintenanceStandardItemImportDto record, AnalysisContext context) {
                MaintenanceStandardItemEntity entity = BeanUtilsIntensifier.copyBean(record, MaintenanceStandardItemEntity.class);
                String fullBomName = entity.getFullBomName();

                String key = (StringUtils.isEmpty(fullBomName) ? "" : fullBomName) + "-" + entity.getPosition();
                Long oldItemId = itemMap.get(key);
                if (oldItemId != null) {
                    entity.setId(oldItemId);
                }

                if(record.getFullBomName() == null || "".equals(record.getFullBomName())){
                    resultList.add(new ImportFailInfo(fullBomName, "模块名称不能为空", "模块名称不能为空"));
                    return;
                }
                if(record.getPosition() == null || "".equals(record.getPosition())){
                    resultList.add(new ImportFailInfo(fullBomName, "项目名称不能为空", "项目名称不能为空"));
                    return;
                }

                entity.setMaintenanceStandardId(standardId);

                TenantIsolation tenant = new TenantIsolation();
                tenant.setTenantId(tenantIsolation.getTenantId());
                tenant.setIdType(tenantIsolation.getIdType());
                if(StandardTypeEnum.SPECIAL.getValue().equals(maintenanceStandard.getType())){
                    DeviceBomEntity deviceBomEntity = deviceBomService.getBomByFullName(tenant, maintenanceStandard.getDeviceTypeId(), fullBomName);
                    if(deviceBomEntity == null){
                        resultList.add(new ImportFailInfo(fullBomName, "未找到匹配的部件", "未找到匹配的部件"));
                        return;
                    }
                } else if(StandardTypeEnum.COMMON.getValue().equals(maintenanceStandard.getType())) {
                    Long moduleId = maintenanceStandardModuleService.getModuleIdByModuleName(tenantIsolation.getTenantId(), standardId, fullBomName);
                    entity.setMaintenanceStandardModuleId(moduleId);
                }

                try {
                    maintenanceStandardItemService.saveOrUpdate(entity);
                } catch (Exception e) {
                    resultList.add(new ImportFailInfo(fullBomName, "插入标准项异常", e.getMessage()));
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("import finish");
            }

            @Override
            public  void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                Set<String> expectedHeaders = new HashSet<>();
                expectedHeaders.add("模块名称");
                expectedHeaders.add("项目名称");
                expectedHeaders.add("备注");
                Set<String> headers = headMap.values().stream().map(ReadCellData::getStringValue).collect(Collectors.toSet());
                if(!expectedHeaders.equals(headers)){
                    throw new BizException("导入失败，请使用正确的模板导入");
                }
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) {
                if (exception instanceof ExcelDataConvertException) {
                    // 数据转换异常，可以选择记录日志或忽略
                    log.error("Error at row " + context.readRowHolder().getRowIndex() + ": " + exception.getMessage());
                    resultList.add(new ImportFailInfo("第" + context.readRowHolder().getRowIndex() + "行异常", "数据转换异常", exception.getMessage()));
                } else if (exception instanceof BizException) {
                    throw new RuntimeException(exception.getMessage());
                } else {
                    resultList.add(new ImportFailInfo("导入异常", "其他异常", exception.getMessage()));
                }
            }

        }).sheet().doRead();
        return Result.ok(resultList);
    }

    @Override
    public void exportStandard(Long tenantId, Long standardId, HttpServletResponse response) throws IOException {
        MaintenanceStandardEntity maintenanceStandard = this.getById(standardId);
        if (maintenanceStandard == null) {
            throw new BizException("导出失败，标准不存在");
        }

        List<MaintenanceStandardItemEntity> itemList = maintenanceStandardItemService.list(Wrappers.<MaintenanceStandardItemEntity>lambdaQuery()
                .eq(MaintenanceStandardItemEntity::getMaintenanceStandardId, maintenanceStandard.getId()));

        List<MaintenanceStandardItemImportDto> exportStandardList = BeanUtilsIntensifier.copyBeanList(itemList, MaintenanceStandardItemImportDto.class);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = maintenanceStandard.getStandardName() + ".xlsx";
        String encodeFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + encodeFileName
                + ";filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

        EasyExcel.write(response.getOutputStream(), MaintenanceStandardItemImportDto.class)
                .registerWriteHandler(new SheetWriteHandler() {
                    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                        Sheet sheet = writeSheetHolder.getSheet();
                        Row row1 = sheet.createRow(0);
                        row1.createCell(0).setCellValue("test");
                    }
                })
                .sheet(maintenanceStandard.getStandardName())
                .doWrite(exportStandardList);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response, Integer type) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "";
        if (StandardTypeEnum.COMMON.getValue().equals(type)) {
            fileName = "保养标准通用型模版.xlsx";
        } else if (StandardTypeEnum.SPECIAL.getValue().equals(type)) {
            fileName = "保养标准设备型模版.xlsx";
        }
        String encodeFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + encodeFileName);

        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("templates/" + fileName);
        StreamUtils.copy(inputStream, response.getOutputStream());
    }

    public boolean uniqueName(Long tenantId, Integer idType, String name){
        Long count = new LambdaQueryChainWrapper<>(mapper)
                .eq(MaintenanceStandardEntity::getIdType, idType)
                .eq(MaintenanceStandardEntity::getTenantId, tenantId)
                .eq(MaintenanceStandardEntity::getStandardName, name)
                .count();
        if (count > 0) {
            return false;
        }
        return true;
    }

    @Override
    public void initTenantStandard(Long tenantId) {
        List<MaintenanceStandardEntity> standardList = this.list(Wrappers.<MaintenanceStandardEntity>lambdaQuery().eq(MaintenanceStandardEntity::getTenantId, tenantId));
        if (CollectionUtil.isEmpty(standardList)) {
            MaintenanceStandardEntity entity = new MaintenanceStandardEntity();
            Long id = IdGenerator.generateId();
            entity.setId(id);
            entity.setStandardName("堆垛机保养标准（案例）");
            String orderNumber = serialNumberService.getNext(tenantId, LocalDate.now(), MaintenanceStandard.SERIALNUMBER);
            entity.setStandardNumber(orderNumber);
            entity.setType(StandardTypeEnum.COMMON.getValue());
            entity.setIdType(IdTypeEnum.CUSTOMER.getValue());
            entity.setDeviceTypeId(0L);
            entity.setCreator("admin");
            entity.setUpdator("admin");
            if (mapper.insert(entity) == 1) {
                standardItemMapper.initTenantStandardItem(tenantId, id);
            }
        }
    }
}
