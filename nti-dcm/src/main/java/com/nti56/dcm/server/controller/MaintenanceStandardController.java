package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.MaintenanceStandardEntity;
import com.nti56.dcm.server.model.dto.CopyMaintenanceStandardDto;
import com.nti56.dcm.server.model.dto.MaintenanceStandardDto;
import com.nti56.dcm.server.model.vo.MaintenanceStandardVo;
import com.nti56.dcm.server.service.MaintenanceStandardService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-28 15:37:22
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/maintenanceStandard")
@Tag(name = "保养标准模块")
public class MaintenanceStandardController {

    @Autowired
    private MaintenanceStandardService maintenanceStandardService;

    @GetMapping("page")
    @Operation(summary = "获取保养标准分页")
    public R<Page<MaintenanceStandardVo>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                               PageParam pageParam, MaintenanceStandardDto dto){
        Page<MaintenanceStandardVo> page = pageParam.toPage(MaintenanceStandardVo.class);
        Result<Page<MaintenanceStandardVo>> result = maintenanceStandardService.getPage(tenantIsolation, page, dto);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建保养标准")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @Validated @RequestBody MaintenanceStandardDto dto){
        return R.result(maintenanceStandardService.createMaintenanceStandard(tenantIsolation, dto));
    }

    @PutMapping("")
    @Operation(summary = "更新保养标准")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @Validated @RequestBody MaintenanceStandardDto dto){
        return R.result(maintenanceStandardService.updateMaintenanceStandard(tenantIsolation, dto));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除保养标准")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(maintenanceStandardService.deleteById(tenantIsolation, id));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取保养标准")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(maintenanceStandardService.getById(tenantIsolation, id));
    }

    @PostMapping("copy")
    @Operation(summary = "复制保养标准")
    public R copy(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  @Validated @RequestBody CopyMaintenanceStandardDto dto) {
        return R.result(maintenanceStandardService.copy(tenantIsolation, dto));
    }

    @PostMapping("import")
    @Operation(summary = "导入")
    public R importStandard(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                            MaintenanceStandardDto dto,
                            @RequestParam("file") MultipartFile file) throws IOException {
        return R.result(maintenanceStandardService.importStandard(tenantIsolation, dto, file));
    }

    @PostMapping("importStandardCover")
    @Operation(summary = "导入")
    public R importStandardCover(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                            Long standardId,
                            @RequestParam("file") MultipartFile file) throws IOException {
        return R.result(maintenanceStandardService.importStandardCover(tenantIsolation, standardId, file));
    }

    @GetMapping("export")
    @Operation(summary = "导出")
    public void exportStandard(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                            @RequestParam(name = "standardId") Long standardId,
                            HttpServletResponse response) throws IOException {
        maintenanceStandardService.exportStandard(tenantIsolation.getTenantId(), standardId, response);
    }

    @GetMapping("/download-template")
    @Operation(summary = "下载模板")
    public void downloadTemplate(HttpServletResponse response, Integer type) throws IOException {
        maintenanceStandardService.downloadTemplate(response, type);
    }
    
}
