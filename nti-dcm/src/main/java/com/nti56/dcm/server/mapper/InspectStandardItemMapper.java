package com.nti56.dcm.server.mapper;

import com.nti56.dcm.server.entity.InspectStandardItemEntity;
import com.nti56.dcm.server.model.vo.CountVo;
import com.nti56.dcm.server.model.vo.InspectStandardItemVo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nti56.common.mybatis.MyBaseMapper;

/**
 * <p>
 * 点巡检标准项目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23 15:13:54
 */
public interface InspectStandardItemMapper extends MyBaseMapper<InspectStandardItemEntity> {

    @Select("SELECT * FROM inspect_standard_item WHERE inspect_standard_id = #{standardId} AND deleted = 0")
    List<InspectStandardItemEntity> listByStandardId(@Param("standardId") Long standardId);

    @Select("SELECT * FROM inspect_standard_item WHERE inspect_standard_id = #{standardId} AND deleted = 0")
    List<InspectStandardItemVo> listVoByStandardId(@Param("standardId") Long standardId);

    @MapKey("key")
    Map<String, CountVo> countByStandardIds(@Param("standardIds") List<Long> standardIds);

    void initTenantStandardItem(@Param("tenantId") Long tenantId, @Param("standardId") Long standardId);

    @Delete("DELETE FROM inspect_standard_item WHERE inspect_standard_module_id = #{moduleId}")
    void deleteByModuleId(@Param("moduleId") Long moduleId);

}
