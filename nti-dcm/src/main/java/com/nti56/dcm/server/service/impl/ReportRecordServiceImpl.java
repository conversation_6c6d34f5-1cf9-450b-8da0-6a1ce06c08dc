package com.nti56.dcm.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nti56.common.constant.Constant;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.*;
import com.nti56.dcm.server.mapper.DeviceMapper;
import com.nti56.dcm.server.mapper.InspectOrderMapper;
import com.nti56.dcm.server.mapper.ReportRecordMapper;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.*;
import com.nti56.dcm.server.service.*;
import com.nti56.flowable.common.dto.R;
import com.nti56.flowable.common.service.biz.IRemoteService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.sdk.basic.request.PageQuery;
import com.nti56.sdk.basic.response.ApiResult;
import com.nti56.sdk.basic.utils.NtiApiUtils;
import com.nti56.user.sdk.request.ApiDingUserListRequest;
import com.nti56.user.sdk.request.UserOnlineTimePageRequest;
import com.nti56.user.sdk.response.ApiDingUserListResponse;
import com.nti56.user.sdk.response.UserOnlineTimePageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 报表记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-04-07 16:06:08
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ReportRecordServiceImpl extends ServiceImpl<ReportRecordMapper, ReportRecordEntity> implements IReportRecordService {

    @Autowired
    private MaintenanceOrderService maintenanceOrderService;
    
    @Autowired
    private IDeviceRepairService repairOrderService;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private AlarmRecordService alarmRecordService;

    @Autowired
    private MessageNoticeConfigService messageNoticeConfigService;

    @Autowired
    private IRemoteService remoteService;

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private DeviceEmpowerService deviceEmpowerService;

    @Autowired
    private IReportRecordUserService reportRecordUserService;
    
    @Autowired
    private InspectOrderMapper inspectOrderMapper;

    @Autowired
    private ReportRecordMapper reportRecordMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Value("${user-center.base-url}")
    private String baseUrl;

    @Value("${user-center.user-online-time}")
    private String userOnlineTime;

    @Value("${use-report.receivers}")
    private String receivers;

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restHttpTemplate;

    @Override
    public Result<Page<ReportRecordVo>> getPage(TenantIsolation tenantIsolation, ReportRecordDto dto, Page<ReportRecordVo> page) {
        dto.setUserId(JwtUserInfoUtils.getUserId());
        Page<ReportRecordVo> list = reportRecordMapper.pageReportRecord(tenantIsolation.getTenantId(), dto, page);
        return Result.ok(list);
    }

    @Override
    public Result<ReportRecordDetailVo> getById(Long id) {
        ReportRecordDetailVo detailVo = new ReportRecordDetailVo();

        ReportRecordEntity entity = reportRecordMapper.selectById(id);
        if (Objects.isNull(entity)) {
            return Result.error("报表不存在");
        }

        ReportRecordUserEntity reportRecordUserEntity = reportRecordUserService.getOne(Wrappers.<ReportRecordUserEntity>lambdaQuery()
                .eq(ReportRecordUserEntity::getReportRecordId, id)
                .eq(ReportRecordUserEntity::getUserId, JwtUserInfoUtils.getUserId()));
        if (Objects.isNull(reportRecordUserEntity)) {
            return Result.error("没有权限查看该报表");
        }

        String reportRange = reportRecordUserEntity.getReportRange();

        if (StringUtils.isEmpty(reportRange)) {
            return Result.ok(detailVo);
        }

        if (reportRange.contains("1")) {
            detailVo.setVisible1(true);
        }
        if (reportRange.contains("2")) {
            detailVo.setVisible2(true);
        }
        if (reportRange.contains("3")) {
            detailVo.setVisible3(true);
        }
        if (reportRange.contains("4")) {
            detailVo.setVisible4(true);
        }

        BeanUtils.copyProperties(entity, detailVo);

        return Result.ok(detailVo);
    }

    @Override
    public Result<List<CustomerRelationEntity>> getCustomerList(Long tenantId, List<Long> customerIds) {
        if (CollectionUtil.isEmpty(customerIds)) {
            return Result.ok(Lists.newArrayList());
        }

        List<CustomerRelationEntity> list = customerRelationService.list(Wrappers.<CustomerRelationEntity>lambdaQuery()
                .select(CustomerRelationEntity::getId, CustomerRelationEntity::getCustomerId, CustomerRelationEntity::getCustomerName)
                .eq(CustomerRelationEntity::getSupplierId, tenantId)
                .eq(CustomerRelationEntity::getIdType, IdTypeEnum.SUPPLIER.getValue())
                .eq(CustomerRelationEntity::getStatus, RelationStatusEnum.APPROVE.getValue())
                .in(CustomerRelationEntity::getCustomerId, customerIds));
        return Result.ok(list);
    }

    @Override
    public ReportRecordEntity reportStatistics(Integer reportType, String belongDate, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        log.info("统计时间范围: {} 至 {}", startDateTime, endDateTime);
        ReportRecordEntity entity = new ReportRecordEntity();
        entity.setReportType(reportType);
        entity.setBelongDate(belongDate);
        entity.setStartTime(startDateTime);
        entity.setEndTime(endDateTime);
        entity.setTenantId(DictConstant.DEFAULT_TENANT_ID);
        entity.setCreator("admin");
        entity.setUpdator("admin");

        // 客户新增及上云概况
        NewCustomerVo newCustomerVo = customerRelationService.getNewCustomerCountByDate(startDateTime, endDateTime);
        BeanUtils.copyProperties(newCustomerVo, entity);

        // 设备异常概况、维保/质保到期概况
        NewAlarmVo newAlarmVo = alarmRecordService.getNewAlarmCountByDate(startDateTime, endDateTime);
        BeanUtils.copyProperties(newAlarmVo, entity);

        // 累计临期、过期客户
        getCumData(entity);

        // 客户服务概况
        OrderStatisticsVo orderStatisticsVo = getOrderStatistics(startDateTime, endDateTime);
        BeanUtils.copyProperties(orderStatisticsVo, entity);

        // 记录统计结果
        log.info("统计结果: ", JSONObject.toJSONString(entity));

        this.save(entity);

        // 发送消息通知
        sendNotice(entity);
        return entity;
    }

    /**
     * 查询时间范围内的工单统计数据
     * 
     * @param startDateTime 开始日期
     * @param endDateTime 结束日期
     * @return 工单统计数据
     */
    public OrderStatisticsVo getOrderStatistics(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        OrderStatisticsVo result = new OrderStatisticsVo();
        
        // 查询维修工单数据
        Map<String, Object> repairOrderStats = getRepairOrderStatistics(startDateTime, endDateTime);
        result.setRepairIngOrderCount(((Long) repairOrderStats.get("repairInProgressCount")).intValue());
        result.setRepairCompleteOrderCount(((Integer) repairOrderStats.get("repairCompletedCount")));
        result.setRepairServiceCustomerCount(((Integer) repairOrderStats.get("repairCustomerCount")));
        
        // 查询保养工单数据
        Map<String, Object> maintenanceOrderStats = getMaintenanceOrderStatistics(startDateTime, endDateTime);
        result.setMaintenanceIngOrderCount(((Long) maintenanceOrderStats.get("maintenanceInProgressCount")).intValue());
        result.setMaintenanceCompleteOrderCount(((Integer) maintenanceOrderStats.get("maintenanceCompletedCount")));
        result.setMaintenanceServiceCustomerCount(((Integer) maintenanceOrderStats.get("maintenanceCustomerCount")));
        
        // 查询巡检工单数据
        Map<String, Object> inspectionOrderStats = getInspectionOrderStatistics(startDateTime, endDateTime);
        result.setInspectIngOrderCount(((Long) inspectionOrderStats.get("inspectionInProgressCount")).intValue());
        result.setInspectCompleteOrderCount(((Integer) inspectionOrderStats.get("inspectionCompletedCount")));
        result.setInspectServiceCustomerCount(((Integer) inspectionOrderStats.get("inspectionCustomerCount")));
        
        // 计算总计数据
        // 获取所有工单的客户ID并去重
        Set<Long> allCustomerIds = new HashSet<>();
        
        // 添加维修工单客户ID
        Set<Long> repairCustomerIds = (Set<Long>) repairOrderStats.get("customerIds");
        if (repairCustomerIds != null) {
            result.setRepairServiceCustomer(repairCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            allCustomerIds.addAll(repairCustomerIds);
        }
        
        // 添加保养工单客户ID
        Set<Long> maintenanceCustomerIds = (Set<Long>) maintenanceOrderStats.get("customerIds");
        if (maintenanceCustomerIds != null) {
            result.setMaintenanceServiceCustomer(maintenanceCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            allCustomerIds.addAll(maintenanceCustomerIds);
        }
        
        // 添加巡检工单客户ID
        Set<Long> inspectionCustomerIds = (Set<Long>) inspectionOrderStats.get("customerIds");
        if (inspectionCustomerIds != null) {
            result.setInspectServiceCustomer(inspectionCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            allCustomerIds.addAll(inspectionCustomerIds);
        }
        
        // 设置去重后的总客户数量
        result.setServiceCustomerCount(allCustomerIds.size());
        result.setServiceCustomer(allCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        
        // 计算总完成数量
        int totalCompletedCount = result.getRepairCompleteOrderCount() +
                                 result.getMaintenanceCompleteOrderCount() +
                                 result.getInspectCompleteOrderCount();
        
        result.setFinishOrderCount(totalCompletedCount);
        
        return result;
    }

    /**
     * 查询维修工单统计数据
     */
    private Map<String, Object> getRepairOrderStatistics(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Map<String, Object> result = new HashMap<>();
        
        // 构建查询条件
        LambdaQueryWrapper<DeviceRepairEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceRepairEntity::getOutsourceSupplierId, DictConstant.DEFAULT_TENANT_ID);
        queryWrapper.eq(DeviceRepairEntity::getOutsourceStatus, OutsourceEnum.YES.getValue());
        queryWrapper.ne(DeviceRepairEntity::getStatus, OrderStatusEnum.WAIT_RECEIVE.getValue());

        // 查询所有符合条件的维修工单
        List<DeviceRepairEntity> allOrders = repairOrderService.list(queryWrapper);
        
        // 统计维修中数量
        long inProgressCount = allOrders.stream()
                .filter(order -> order.getStatus() < OrderStatusEnum.ACCEPTED.getValue())
                .count();
        
        // 统计完成数量
        List<DeviceRepairEntity> completeOrders = allOrders.stream()
                .filter(order -> OrderStatusEnum.ACCEPTED.getValue().equals(order.getStatus())
                        && order.getRepairEndTime() != null
                        && !order.getRepairEndTime().isBefore(startDateTime)
                        && !order.getRepairEndTime().isAfter(endDateTime))
                .collect(Collectors.toList());
        
        // 统计服务客户数量（去重）
        Set<Long> customerIds = completeOrders.stream()
                .map(DeviceRepairEntity::getTenantId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        result.put("repairInProgressCount", inProgressCount);
        result.put("repairCompletedCount", completeOrders.size());
        result.put("repairCustomerCount", customerIds.size());
        result.put("customerIds", customerIds); // 保存客户ID集合，用于后续去重计算
        
        return result;
    }
    
    /**
     * 查询保养工单统计数据
     */
    private Map<String, Object> getMaintenanceOrderStatistics(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Map<String, Object> result = new HashMap<>();
        
        // 构建查询条件
        LambdaQueryWrapper<MaintenanceOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaintenanceOrderEntity::getSupplierId, DictConstant.DEFAULT_TENANT_ID);
        queryWrapper.eq(MaintenanceOrderEntity::getOutsource, OutsourceEnum.YES.getValue());
        queryWrapper.ne(MaintenanceOrderEntity::getStatus, OrderStatusEnum.WAIT_RECEIVE.getValue());
        
        // 查询所有符合条件的保养工单
        List<MaintenanceOrderEntity> allOrders = maintenanceOrderService.list(queryWrapper);
        
        // 统计维修中数量
        long inProgressCount = allOrders.stream()
                .filter(order -> order.getStatus() < OrderStatusEnum.ACCEPTED.getValue())
                .count();
        
        // 统计完成数量
        List<MaintenanceOrderEntity> completeOrders  = allOrders.stream()
                .filter(order -> OrderStatusEnum.ACCEPTED.getValue().equals(order.getStatus())
                        && order.getMaintenanceEnd() != null
                        && !order.getMaintenanceEnd().isBefore(startDateTime)
                        && !order.getMaintenanceEnd().isAfter(endDateTime))
                .collect(Collectors.toList());
        
        // 统计服务客户数量（去重）
        Set<Long> customerIds = completeOrders.stream()
                .map(MaintenanceOrderEntity::getTenantId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        result.put("maintenanceInProgressCount", inProgressCount);
        result.put("maintenanceCompletedCount", completeOrders.size());
        result.put("maintenanceCustomerCount", customerIds.size());
        result.put("customerIds", customerIds); // 保存客户ID集合，用于后续去重计算
        
        return result;
    }
    
    /**
     * 查询巡检工单统计数据
     */
    private Map<String, Object> getInspectionOrderStatistics(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Map<String, Object> result = new HashMap<>();
        
        // 构建查询条件
        LambdaQueryWrapper<InspectOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectOrderEntity::getSupplierId, DictConstant.DEFAULT_TENANT_ID);
        queryWrapper.eq(InspectOrderEntity::getOutsource, OutsourceEnum.YES.getValue());
        queryWrapper.ne(InspectOrderEntity::getStatus, OrderStatusEnum.WAIT_RECEIVE.getValue());
        
        // 查询所有符合条件的巡检工单
        List<InspectOrderEntity> allOrders = inspectOrderMapper.selectList(queryWrapper);
        
        // 统计维修中数量
        long inProgressCount = allOrders.stream()
                .filter(order -> order.getStatus() < OrderStatusEnum.ACCEPTED.getValue())
                .count();
        
        // 统计完成数量
        List<InspectOrderEntity> completeOrders = allOrders.stream()
                .filter(order -> OrderStatusEnum.ACCEPTED.getValue().equals(order.getStatus())
                        && order.getInspectEnd() != null
                        && !order.getInspectEnd().isBefore(startDateTime)
                        && !order.getInspectEnd().isAfter(endDateTime))
                .collect(Collectors.toList());
        
        // 统计服务客户数量（去重）
        Set<Long> customerIds = completeOrders.stream()
                .map(InspectOrderEntity::getCustomerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        result.put("inspectionInProgressCount", inProgressCount);
        result.put("inspectionCompletedCount", completeOrders.size());
        result.put("inspectionCustomerCount", customerIds.size());
        result.put("customerIds", customerIds); // 保存客户ID集合，用于后续去重计算
        
        return result;
    }

    private void sendNotice(ReportRecordEntity reportRecordEntity) {
        Integer configType = reportRecordEntity.getReportType();
        // 查询每条配置的通知人员
        List<MessageNoticeConfigEditDto> actualNoticeConfigList = getActualNoticeConfigList(configType);

        if (CollectionUtil.isEmpty(actualNoticeConfigList)) {
            return;
        }
        MessageNoticeConfigEditDto config = actualNoticeConfigList.get(0);

        // 构建 报表范围 和 通知人员 的映射关系
        Map<Set<String>, Set<Long>> typeGroupPersonMap = getTypeGroupPersonMap(actualNoticeConfigList);

        typeGroupPersonMap.forEach((types, persons) -> {
            // 保存人员报表范围
            saveReportRecordUser(reportRecordEntity.getId(), types, persons);

            // 获取钉钉用户ID
            Result<List<String>> dingUserIdResult = userCenterService.getDingUserIdByIds(config.getTenantId(), new ArrayList<>(persons));
            if (!dingUserIdResult.getSignal()) {
                log.error("获取钉钉UserId失败：" + dingUserIdResult.getMessage());
                return;
            }
            // 发送通知
            sendMessageService.sendReportNotify(config, types, dingUserIdResult.getResult(), reportRecordEntity);
        });

    }

    private void saveReportRecordUser(Long reportRecordId, Set<String> types, Set<Long> userIds) {
        List<ReportRecordUserEntity> reportUserList = new ArrayList<>();
        String reportRange = String.join(",", types);
        for (Long userId : userIds) {
            ReportRecordUserEntity reportUser = ReportRecordUserEntity.builder()
                    .reportRecordId(reportRecordId)
                    .userId(userId)
                    .reportRange(reportRange)
                    .tenantId(DictConstant.DEFAULT_TENANT_ID)
                    .creator("admin")
                    .updator("admin")
                    .build();
            reportUserList.add(reportUser);
        }
        reportRecordUserService.saveBatch(reportUserList);
    }

    private List<MessageNoticeConfigEditDto> getActualNoticeConfigList(Integer configType) {
        List<MessageNoticeConfigEditDto> actualNoticeConfigList = new ArrayList<>();
        // 查询报表推送配置
        List<MessageNoticeConfigEntity> noticeConfigList = messageNoticeConfigService.list(Wrappers.<MessageNoticeConfigEntity>lambdaQuery()
                .eq(MessageNoticeConfigEntity::getTenantId, DictConstant.DEFAULT_TENANT_ID)
                .eq(MessageNoticeConfigEntity::getIdType, IdTypeEnum.SUPPLIER.getValue())
                .eq(MessageNoticeConfigEntity::getOrderType, 4)
                .eq(MessageNoticeConfigEntity::getStatus, YesNoEnum.YES.getValue())
                .eq(MessageNoticeConfigEntity::getConfigType, configType));

        if (CollectionUtil.isEmpty(noticeConfigList)) {
            return actualNoticeConfigList;
        }

        // 查询每条配置的通知人员
        actualNoticeConfigList = noticeConfigList.stream()
                .map(config -> {
                    MessageNoticeConfigEditDto dto = new MessageNoticeConfigEditDto();
                    BeanUtils.copyProperties(config, dto);
                    dto.setUserIdList(getReceiveUserIdList(config.getPersonScope(), config.getTenantId()));
                    return dto;
                }).filter(d -> CollectionUtil.isNotEmpty(d.getUserIdList()))
                .collect(Collectors.toList());
        return actualNoticeConfigList;
    }

    /**
     * 构建 报表范围 和 通知人员 的映射关系
     *
     * @param configVos 配置信息
     * @return 报表范围 和 通知人员 的映射关系
     */
    public Map<Set<String>, Set<Long>> getTypeGroupPersonMap(List<MessageNoticeConfigEditDto> configVos) {
        // 首先构建每个人对应的type集合
        Map<Long, Set<String>> personTypeMap = new HashMap<>();

        for (MessageNoticeConfigEditDto config : configVos) {
            String[] types = config.getReportRange().split(",");

            for (Long person : config.getUserIdList()) {
                personTypeMap.computeIfAbsent(person, k -> new HashSet<>());
                personTypeMap.get(person).addAll(Arrays.asList(types));
            }
        }

        // 反转映射：将相同type集合的人分到一组
        Map<Set<String>, Set<Long>> typeGroupPersonMap = new HashMap<>();

        for (Map.Entry<Long, Set<String>> entry : personTypeMap.entrySet()) {
            Long person = entry.getKey();
            Set<String> types = entry.getValue();
            typeGroupPersonMap.computeIfAbsent(types, k -> new HashSet<>()).add(person);
        }

        return typeGroupPersonMap;
    }

    private List<Long> getReceiveUserIdList(String personScope, Long tenantId) {
        List<Long> receiveUserIdList = Lists.newLinkedList();
        //获取发送人员ID
        if (StringUtils.isEmpty(personScope)) {
            return receiveUserIdList;
        }

        List<PersonScopeDto> personScopeList = JSONArray.parseArray(personScope, PersonScopeDto.class);

        if (CollectionUtil.isEmpty(personScopeList)) {
            return receiveUserIdList;
        }

        List<Long> userIds = personScopeList.stream()
                .filter(p -> !p.isRole())
                .map(p -> Long.parseLong(p.getId()))
                .collect(Collectors.toList());

        receiveUserIdList.addAll(userIds);

        List<String> roleIds = personScopeList.stream()
                .filter(p -> p.isRole())
                .map(p -> p.getId())
                .collect(Collectors.toList());
        log.info("获取角色下用户Id,param:{}", roleIds);
        if (CollectionUtil.isNotEmpty(roleIds)) {
            R<List<String>> userIdList = remoteService.queryUserIdListByRoleIdList(roleIds, String.valueOf(tenantId));
            if (userIdList.isOk() && CollectionUtil.isNotEmpty(userIdList.getData())) {
                List<Long> roleUserIds = userIdList.getData().stream()
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                receiveUserIdList.addAll(roleUserIds);
                log.info("获取角色下用户Id,response:{}", roleUserIds);
            }
        }
        return receiveUserIdList;
    }

    private void getCumData(ReportRecordEntity entity) {
        // 累计临期、过期客户
        List<CustomerRelationEntity> currentCustomerList = customerRelationService.list(Wrappers.<CustomerRelationEntity>lambdaQuery()
                .eq(CustomerRelationEntity::getSupplierId, DictConstant.DEFAULT_TENANT_ID)
                .eq(CustomerRelationEntity::getIdType, IdTypeEnum.SUPPLIER.getValue())
                .eq(CustomerRelationEntity::getStatus, RelationStatusEnum.APPROVE.getValue()));
        if (CollUtil.isNotEmpty(currentCustomerList)) {
            // 查询授权给当前租户的设备信息
            List<Long> empowerDeviceIdList = deviceEmpowerService.list(Wrappers.<DeviceEmpowerEntity>lambdaQuery()
                            .in(DeviceEmpowerEntity::getCustomerId, currentCustomerList.stream().map(i -> i.getCustomerId()).collect(Collectors.toList()))
                            .eq(DeviceEmpowerEntity::getSupplierId, DictConstant.DEFAULT_TENANT_ID))
                    .stream().map(DeviceEmpowerEntity::getDeviceId).collect(Collectors.toList());

            List<DeviceConnectCountDto> result = new ArrayList<>();
            result = cumCustomerCount(empowerDeviceIdList, AggregatorDeviceQueryDto.builder().maintenanceExpireState(1).build());
            entity.setCumMaintenanceAdventCusCount(result.size());
            // 获取maintenanceCum的deviceCount总和
            entity.setCumMaintenanceAdventDeviceCount(result.stream().mapToInt(DeviceConnectCountDto::getDeviceCount).sum());

            result = cumCustomerCount(empowerDeviceIdList, AggregatorDeviceQueryDto.builder().maintenanceExpireState(2).build());
            entity.setCumMaintenanceExpireCusCount(result.size());
            // 获取maintenanceCum的deviceCount总和
            entity.setCumMaintenanceExpireDeviceCount(result.stream().mapToInt(DeviceConnectCountDto::getDeviceCount).sum());

            result = cumCustomerCount(empowerDeviceIdList, AggregatorDeviceQueryDto.builder().warrantyExpireState(1).build());
            entity.setCumWarrantyAdventCusCount(result.size());
            // 获取warrantyCum的deviceCount总和
            entity.setCumWarrantyAdventDeviceCount(result.stream().mapToInt(DeviceConnectCountDto::getDeviceCount).sum());

            result = cumCustomerCount(empowerDeviceIdList, AggregatorDeviceQueryDto.builder().warrantyExpireState(2).build());
            entity.setCumWarrantyExpireCusCount(result.size());
            // 获取warrantyCum的deviceCount总和
            entity.setCumWarrantyExpireDeviceCount(result.stream().mapToInt(DeviceConnectCountDto::getDeviceCount).sum());
        }
    }

    private List<DeviceConnectCountDto> cumCustomerCount(List<Long> empowerDeviceIdList, AggregatorDeviceQueryDto queryDto) {
        List<DeviceConnectCountDto> allCountDtoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(empowerDeviceIdList)) {
            // 从设备表中查询授权设备租户下的设备数量
            List<DeviceConnectCountDto> deviceConnectCountDtos = deviceMapper.queryCustomerDeviceCount(queryDto, empowerDeviceIdList, null);
            allCountDtoList.addAll(deviceConnectCountDtos);
        }
        // 如果当前是集成商身份的今天国际   则台账数据要加上来自于pmo的数据
        // 从设备表中查询 pmo设备 按租户分组的设备数量
        List<DeviceConnectCountDto> deviceConnectCountDtos = deviceMapper.queryCustomerDeviceCount(queryDto, null, DictConstant.IS_PMO);
        allCountDtoList.addAll(deviceConnectCountDtos);

        return allCountDtoList;
    }

    @Override
    public void useDcmReport(Integer reportType, String belongDate, LocalDate startDate, LocalDate endDate) {
        List<UserOnlineTimePageResponse> list = getUserOnlineTimeList(startDate, endDate);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.typeOfValue(reportType);

        if (StringUtils.isBlank(receivers)) {
            log.error("接收人配置为空");
            return;
        }
        List<Long> receiveUserIdList = new ArrayList<>();
        for (String s : receivers.split(",")) {
            receiveUserIdList.add(Long.parseLong(s));
        }
        Result<List<String>> dingUserIdResult = userCenterService.getDingUserIdByIds(DictConstant.DEFAULT_TENANT_ID, receiveUserIdList);
        if (!dingUserIdResult.getSignal()) {
            log.error("获取钉钉UserId失败：" + dingUserIdResult.getMessage());
            return;
        }
        List<String> dingUserIds = dingUserIdResult.getResult();
        sendMessageService.sendUseDcmReportNotify(list, reportTypeEnum, startDate, endDate, belongDate, dingUserIds);
    }

    public List<UserOnlineTimePageResponse> getUserOnlineTimeList(LocalDate startDate, LocalDate endDate) {
        UserOnlineTimePageRequest request = new UserOnlineTimePageRequest();
        request.setDateRange(Arrays.asList(startDate.toString(), endDate.toString()));

        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setRows(10000);
        request.setPageQuery(pageQuery);

        log.info("获取用户在线时长,param:{}", JSONObject.toJSONString(request));
        ApiResult<com.nti56.sdk.basic.response.Page<UserOnlineTimePageResponse>> response = NtiApiUtils.appUserLogApi().userOnlineTimeList(request);
        log.info("获取用户在线时长,response:{}", JSONObject.toJSONString(response));
        if (response.getData() == null || response.getData().getRecords().isEmpty()) {
            log.error("获取用户在线时长失败！");
            return new ArrayList<>();
        }
        List<UserOnlineTimePageResponse> records = response.getData().getRecords();
        return records;
    }

    public List<UserOnlineTimeDto> getAddList(LocalDate startDate, LocalDate endDate) {
        List<UserOnlineTimeDto> list = new ArrayList<>();
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add("clientId", String.valueOf(DictConstant.DEFAULT_TENANT_ID));
            headers.add("appcode", Constant.APPCODE_HEADER);
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());

            // 构造请求体参数
            HashMap<String, Object> params = new HashMap<>();
            params.put("dateRange", Arrays.asList(startDate, endDate));
            HashMap<String, Object> pageQuery = new HashMap<>();
            pageQuery.put("page",1);
            pageQuery.put("rows",10000);
            params.put("pageQuery", pageQuery);
            HttpEntity<HashMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);
            String pathUrl = baseUrl + userOnlineTime;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            log.info("获取用户在线时长请求参数：{}", JSON.toJSONString(params));
            String result = restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, String.class).getBody();
            log.info("获取用户在线时长返回结果：{}", result);
            com.nti56.common.util.R<Page<UserOnlineTimeDto>> userDtoR = JSON.parseObject(result, new TypeReference<com.nti56.common.util.R<Page<UserOnlineTimeDto>>>() {});
            if (userDtoR != null && ObjectUtil.equals(userDtoR.getCode(), 200)) {
                list = userDtoR.getData().getRecords();
            }
        } catch (Exception e) {
            log.error("获取用户在线时长异常：{}", e.getMessage());
        }

        return list;
    }

}
