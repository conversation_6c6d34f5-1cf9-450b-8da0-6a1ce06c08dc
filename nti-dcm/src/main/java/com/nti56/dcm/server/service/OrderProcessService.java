package com.nti56.dcm.server.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.OrderProcessConfigEntity;
import com.nti56.dcm.server.entity.OrderProcessRecordEntity;
import com.nti56.dcm.server.model.dto.ResponsibleDto;
import com.nti56.dcm.server.model.dto.StartFlowDto;
import com.nti56.flowable.common.constants.NodeUserTypeEnum;
import com.nti56.flowable.common.constants.ProcessInstanceConstant;
import com.nti56.flowable.common.dto.*;
import com.nti56.flowable.common.dto.flow.Condition;
import com.nti56.flowable.common.dto.flow.NodeUser;
import com.nti56.flowable.common.dto.flow.SelectValue;
import com.nti56.flowable.common.service.core.IFlowService;
import com.nti56.flowable.common.service.core.ITaskService;
import com.nti56.flowable.common.utils.JsonUtil;
import com.nti56.flowable.common.utils.TenantUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.nti56.flowable.common.constants.ProcessInstanceConstant.VariableKey.AUTO_DISPATCH_FLAG;

/**
 * <p>
 * 工单流程 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@Service
@Slf4j
public class OrderProcessService {

    @Autowired
    private OrderProcessConfigService configService;

    @Autowired
    private OrderProcessRecordService recordService;

    @Autowired
    IFlowService flowService;

    @Autowired
    IWorkGroupUserService workGroupUserService;

    @Autowired
    ITaskService taskService;

    @Resource
    private RuntimeService runtimeService;

    /**
     * 启动流程（客户的外委工单）
     *
     * @param customerId     客户租户ID
     * @param orderType      工单类型
     * @param orderId        工单ID
     * @param vendorTenantId 供应商租户ID
     * @return processInstanceId
     */
    public Result<StartFlowDto> startFlow(Long customerId, Integer orderType, Long orderId, ResponsibleDto startUser, Long vendorTenantId, Boolean isDataRecord,Integer aggregatorOutsource) {
        OrderProcessConfigEntity useConfig;
        Long useTenantId = customerId;
        Integer vendorAcceptanceMode = null;
        TenantIsolation tenantIsolation = new TenantIsolation();
        if (!isDataRecord) {
            Result<OrderProcessConfigEntity> configResult = configService.getById(customerId,ObjectUtil.equals(aggregatorOutsource, YesNoEnum.YES.getValue())?IdTypeEnum.SUPPLIER.getValue(): IdTypeEnum.CUSTOMER.getValue(), orderType, true, false);
            Result<OrderProcessConfigEntity> vendorConfigResult = configService.getById(vendorTenantId, IdTypeEnum.SUPPLIER.getValue(), orderType, false, false);
            if (!configResult.getSignal()) {
                return Result.error("工单流程配置不存在");
            }
            if (!vendorConfigResult.getSignal()) {
                return Result.error("工单流程配置不存在");
            }
            OrderProcessConfigEntity config = configResult.getResult();
            OrderProcessConfigEntity vendorConfig = vendorConfigResult.getResult();
            useConfig = configService.buildOutsourcingOrderProcess(config, vendorConfig);
            vendorAcceptanceMode = vendorConfig.getAcceptanceMode();
            useTenantId = vendorTenantId;
            tenantIsolation.setTenantId(vendorTenantId);
            tenantIsolation.setIdType(IdTypeEnum.SUPPLIER.getValue());
        } else {
            Result<OrderProcessConfigEntity> configResult = configService.getById(customerId, IdTypeEnum.CUSTOMER.getValue(), orderType, true, true);
            if (!configResult.getSignal()) {
                return Result.error("工单流程配置不存在");
            }
            tenantIsolation.setTenantId(customerId);
            tenantIsolation.setIdType(IdTypeEnum.SUPPLIER.getValue());
            useConfig = configResult.getResult();
        }
        List<NodeUser> nodeUserList = new ArrayList<>();
        if (useConfig.getIsAutoDispatch()){
            nodeUserList = workGroupUserService.queryNodeUserByOrderType(orderType, tenantIsolation);
        }
        return doStartFlow(useTenantId, orderType, orderId, startUser, useConfig, vendorAcceptanceMode,nodeUserList);
    }



    /**
     * 启动流程（集成商委外给供应商的外委工单）
     *
     * @param customerId     客户租户ID
     * @param orderType      工单类型
     * @param orderId        工单ID
     * @param vendorTenantId 供应商租户ID
     * @return processInstanceId
     */
    public Result<StartFlowDto> startFlow(Long customerId, Integer orderType, Long orderId, ResponsibleDto startUser, Long vendorTenantId, Boolean isDataRecord,Boolean aggregatorOutsource) {
        OrderProcessConfigEntity useConfig;
        Long useTenantId = customerId;
        Integer vendorAcceptanceMode = null;
        TenantIsolation tenantIsolation = new TenantIsolation();
        if (!isDataRecord) {
            Result<OrderProcessConfigEntity> configResult = configService.getById(customerId, IdTypeEnum.SUPPLIER.getValue(), orderType, true, false);
            Result<OrderProcessConfigEntity> vendorConfigResult = configService.getById(vendorTenantId, IdTypeEnum.SUPPLIER.getValue(), orderType, false, false);
            if (!configResult.getSignal()) {
                return Result.error("工单流程配置不存在");
            }
            if (!vendorConfigResult.getSignal()) {
                return Result.error("工单流程配置不存在");
            }
            OrderProcessConfigEntity config = configResult.getResult();
            OrderProcessConfigEntity vendorConfig = vendorConfigResult.getResult();
            useConfig = configService.buildOutsourcingOrderProcess(config, vendorConfig);
            vendorAcceptanceMode = vendorConfig.getAcceptanceMode();
            useTenantId = vendorTenantId;
            tenantIsolation.setTenantId(vendorTenantId);
            tenantIsolation.setIdType(IdTypeEnum.SUPPLIER.getValue());
        } else {
            Result<OrderProcessConfigEntity> configResult = configService.getById(customerId, IdTypeEnum.CUSTOMER.getValue(), orderType, true, true);
            if (!configResult.getSignal()) {
                return Result.error("工单流程配置不存在");
            }
            tenantIsolation.setTenantId(customerId);
            tenantIsolation.setIdType(IdTypeEnum.SUPPLIER.getValue());
            useConfig = configResult.getResult();
        }
        List<NodeUser> nodeUserList = new ArrayList<>();
        if (useConfig.getIsAutoDispatch()){
            nodeUserList = workGroupUserService.queryNodeUserByOrderType(orderType, tenantIsolation);
        }
        return doStartFlow(useTenantId, orderType, orderId, startUser, useConfig, vendorAcceptanceMode,nodeUserList);
    }

    /**
     * 启动流程（所有内部工单和供应商外委自定义客户工单）
     *
     * @param tenantId      租户ID
     * @param idType        所属身份,1-供应商，2-客户
     * @param orderType     工单类型
     * @param isOutsourcing 是否外委
     * @param isDataRecord  是否数据型
     * @param orderId       工单ID
     * @return processInstanceId
     */
    public Result<StartFlowDto> startFlow(Long tenantId, Integer idType, Integer orderType, Boolean isOutsourcing, Boolean isDataRecord, Long orderId, ResponsibleDto startUser) {
        Result<OrderProcessConfigEntity> configResult = configService.getById(tenantId, idType, orderType, isOutsourcing, isDataRecord);
        if (!configResult.getSignal()) {
            return Result.error("工单流程配置不存在");
        }
        List<NodeUser> nodeUserList = new ArrayList<>();
        if (configResult.getResult().getIsAutoDispatch()){
            TenantIsolation tenantIsolation = new TenantIsolation();
            tenantIsolation.setTenantId(tenantId);
            tenantIsolation.setIdType(idType);
            nodeUserList = workGroupUserService.queryNodeUserByOrderType(orderType, tenantIsolation);
        }
        return doStartFlow(tenantId, orderType, orderId, startUser, configResult.getResult(), null, nodeUserList);
    }

    private Result<StartFlowDto> doStartFlow(Long tenantId, Integer orderType, Long orderId, ResponsibleDto startUser, OrderProcessConfigEntity config, Integer vendorAcceptanceMode,List<NodeUser> nodeUserList) {
        Map<String, Object> paramMap = new HashMap<>();
        NodeUser rootUser = NodeUser.builder().id(String.valueOf(startUser.getId())).name(startUser.getName()).type(NodeUserTypeEnum.USER.getKey()).build();
        paramMap.put(ProcessInstanceConstant.VariableKey.STARTER_USER, CollUtil.newArrayList(rootUser));
        paramMap.put("flyflow_723678314280", orderId);

        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        if (config.getIsAutoDispatch()){
            map.put("key", "是");
            map.put("value", "1");
        }else {
            map.put("key", "否");
            map.put("value", "0");
        }
        paramMap.put("flyflow_178395737591", CollUtil.newArrayList(map));
        if (config.getIsAutoDispatch()){
            paramMap.put("flyflow_246374089240", nodeUserList);
        }
        paramMap.put("orderId", orderId);
        paramMap.put("orderType", orderType);
        paramMap.put("allowForward", config.getAllowForward());
        paramMap.put("acceptanceMode", config.getAcceptanceMode());
        if (Objects.nonNull(vendorAcceptanceMode)) {
            paramMap.put("vendorAcceptanceMode", vendorAcceptanceMode);
            paramMap.put("vendorAutoAccept", Objects.equals(vendorAcceptanceMode, AcceptanceModeEnum.NOT_ACCEPTANCE.getValue()));
        }
        ProcessInstanceParamDto build = ProcessInstanceParamDto.builder()
                .tenantId(String.valueOf(tenantId))
                .flowId(config.getFlowId())
                .startUserId(tenantId + ":" + startUser.getId())
                .paramMap(paramMap)
                .bizKey(String.valueOf(orderId))
                .build();
        R result = flowService.start(build);
        if (!result.isOk()) {
            return Result.error(result.getMsg());
        }
        StartFlowDto startFlowDto = new StartFlowDto();
        startFlowDto.setProcessInstanceId(result.getData().toString());
        startFlowDto.setIsAutoDispatch(config.getIsAutoDispatch());
        return checkResult(R.success(startFlowDto), tenantId, orderId, result.getData().toString(), OrderProcessOperationEnum.BEGIN.getValue(), null, null, orderType, startUser.getId(), startUser.getName());
    }
    /**
     * 接收工单
     *
     * @param tenantId          租户ID
     * @param orderId           工单ID
     * @param processInstanceId 流程实例ID
     * @param targetUser        派工给目标对象
     * @param orderType         工单类型
     * @param isSuperRole       是否是超级角色
     * @return
     */
    public Result<Void> receiveOrder(Long tenantId, Long orderId, String processInstanceId, ResponsibleDto targetUser, Integer orderType,Boolean isSuperRole) {
        if (Objects.isNull(targetUser) || Objects.isNull(targetUser.getId())) {
            return Result.error("未找到接收人信息");
        }
        Map<String, Object> paramMap = new HashMap<>();
        NodeUser target = NodeUser.builder().id(tenantId + ":" + targetUser.getId()).name(targetUser.getName()).type(NodeUserTypeEnum.USER.getKey()).build();
        paramMap.put("flyflow_246192072627", CollUtil.newArrayList(target));
        paramMap.put("userId", CollUtil.newArrayList(target));
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        TaskParamDto build = TaskParamDto.builder()
                .approveResult(true)
                .processInstanceId(processInstanceId)
                .userId(tenantId + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .paramMap(paramMap)
                .isAdmin(isSuperRole)
                .approveDesc("接收工单")
                .build();

        R result = taskService.complete(build);
        return checkResult(result, tenantId, orderId, processInstanceId, OrderProcessOperationEnum.RECEIVE.getValue(), targetUser, null, orderType);
    }

    /**
     * 派工
     *
     * @param tenantId          租户ID
     * @param orderId           工单ID
     * @param processInstanceId 流程实例ID
     * @param targetUser        派工给目标对象
     * @param orderType         工单类型
     * @param isSuperRole       是否是超级角色
     * @return
     */
    public Result<Void> dispatch(Long tenantId, Long orderId, String processInstanceId, ResponsibleDto targetUser, Integer orderType,Boolean isSuperRole) {
        if (Objects.isNull(targetUser) || Objects.isNull(targetUser.getId())) {
            return Result.error("请选择派工对象");
        }
        Map<String, Object> paramMap = new HashMap<>();
        NodeUser target = NodeUser.builder().id(tenantId + ":" + targetUser.getId()).name(targetUser.getName()).type(NodeUserTypeEnum.USER.getKey()).build();
        paramMap.put("flyflow_246192072627", CollUtil.newArrayList(target));
        paramMap.put("userId", CollUtil.newArrayList(target));
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        TaskParamDto build = TaskParamDto.builder()
                .approveResult(true)
                .processInstanceId(processInstanceId)
                .userId(tenantId + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .paramMap(paramMap)
                .isAdmin(isSuperRole)
                .approveDesc("派工")
                .build();

        R result = taskService.complete(build);
        return checkResult(result, tenantId, orderId, processInstanceId, OrderProcessOperationEnum.DISPATCH.getValue(), targetUser, null, orderType);
    }

    private <T> Result<T> checkResult(R<T> result, Long tenantId, Long orderId, String processInstanceId, String operation, ResponsibleDto targetUser, String remark, Integer orderType, Long executorId, String executorName) {
        if (!result.isOk()) {
            return Result.error(result.getMsg());
        }
        OrderProcessRecordEntity build = OrderProcessRecordEntity.builder()
                .processInstanceId(processInstanceId)
                .orderId(orderId)
                .orderType(orderType)
                .operation(operation)
                .executorId(executorId)
                .executorName(executorName)
                .remark(remark)
                .build();
        if (Objects.nonNull(targetUser)) {
            build.setTargetId(targetUser.getId());
            build.setTargetName(targetUser.getName());
        }
        recordService.save((Objects.equals(ProcessInstanceConstant.VariableKey.DEFAULT_TENANT_ID, TenantUtil.get()) || Objects.equals("null", TenantUtil.get())) ? tenantId : Long.valueOf(TenantUtil.get()), build);
        return Result.ok(result.getData());
    }

    private <T> Result<T> checkResult(R<T> result, Long tenantId, Long orderId, String processInstanceId, String operation, ResponsibleDto targetUser, String remark, Integer orderType) {
        return checkResult(result, tenantId, orderId, processInstanceId, operation, targetUser, remark, orderType, JwtUserInfoUtils.getUserId(), JwtUserInfoUtils.getUserName());
    }

    /**
     * 转交
     *
     * @param tenantId
     * @param orderId
     * @param processInstanceId 流程实例ID
     * @param targetUser        转交对象
     * @param remark            转交备注
     * @param orderType         工单类型
     * @return
     */
    public Result<Void> forward(Long tenantId, Long orderId, String processInstanceId, ResponsibleDto targetUser, String remark, Integer orderType) {
        if (Objects.isNull(targetUser) || Objects.isNull(targetUser.getId())) {
            return Result.error("请选择转交对象");
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        paramMap.put("remark", remark);
        TaskParamDto build = TaskParamDto.builder()
                .approveResult(true)
                .paramMap(paramMap)
                .processInstanceId(processInstanceId)
                .userId(tenantId + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .targetUserId(tenantId + ":" + targetUser.getId())
                .approveDesc(remark)
                .build();
        R r = taskService.forwardTask(build);
        return checkResult(r, tenantId, orderId, processInstanceId, OrderProcessOperationEnum.FORWARD.getValue(), targetUser, remark, orderType);
    }

    /**
     * 外委非数据型驳回（验收驳回）
     *
     * @param tenantId              外委非数据型都传供应商租户ID
     * @param orderId
     * @param processInstanceId     流程实例ID
     * @param orderType             工单类型
     * @param outsourcingAndNotData 是否是外委非数据型工单
     * @param idType                是供应商驳回还是客户驳回
     * @return
     */
    public Result<Void> rejectedTask(Long tenantId, Long orderId, String processInstanceId, String reason, Integer orderType, Boolean outsourcingAndNotData, IdTypeEnum idType) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        paramMap.put("reason", reason);
        TaskParamDto build = TaskParamDto.builder()
                .paramMap(paramMap)
                .processInstanceId(processInstanceId)
                .userId(TenantUtil.get() + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .approveDesc(reason)
                .build();
        Result<Long> result = queryLastOperatorByProcessInstance(processInstanceId);
        if (result.getSignal()) {
            build.setTargetUserId(tenantId + ":" + result.getResult());
        }
        R r = taskService.fallbackTaskByTenantId(build, tenantId.toString());
        String operation;
        if (outsourcingAndNotData) {
            if (Objects.equals(idType, IdTypeEnum.CUSTOMER)) {
                operation = OrderProcessOperationEnum.CUSTOMER_WITHDRAW.getValue();
            } else {
                operation = OrderProcessOperationEnum.VENDOR_WITHDRAW.getValue();
            }
        } else {
            operation = OrderProcessOperationEnum.WITHDRAW.getValue();
        }
        return checkResult(r, tenantId, orderId, processInstanceId, operation, null, reason, orderType);
    }

    /**
     * 驳回（验收驳回）
     *
     * @param tenantId
     * @param orderId
     * @param processInstanceId 流程实例ID
     * @param orderType         工单类型
     * @return
     */
    public Result<Void> rejectedTask(Long tenantId, Long orderId, String processInstanceId, String reason, Integer orderType) {
        return rejectedTask(tenantId, orderId, processInstanceId, reason, orderType, false, IdTypeEnum.CUSTOMER);
    }

    /**
     * 撤回(适用于撤回派工、撤回执行)
     *
     * @param tenantId
     * @param orderId
     * @param processInstanceId 流程实例ID
     * @param orderType         工单类型
     * @return
     */
    public Result<Void> withdrawTask(Long tenantId, Long orderId, String processInstanceId, String reason, Integer orderType, String operationName) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        paramMap.put("reason", reason);
        TaskParamDto build = TaskParamDto.builder()
                .paramMap(paramMap)
                .processInstanceId(processInstanceId)
//                .targetUserId(TenantUtil.get() + ":" + JwtUserInfoUtils.getUserId())
                .userId(TenantUtil.get() + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .approveDesc(reason)
                .build();
        Result<Long> result = queryLastOperatorByProcessInstance(processInstanceId);
        if (result.getSignal()) {
            build.setTargetUserId(tenantId + ":" + result.getResult());
        }
        R r = taskService.fallbackTask(build);
        return checkResult(r, tenantId, orderId, processInstanceId, operationName, null, reason, orderType);
    }

    /**
     * 撤销（适用于撤销整个工单任务）
     *
     * @param tenantId
     * @param orderId
     * @param processInstanceId 流程实例ID
     * @param orderType         工单类型
     * @return
     */
    public Result<Void> undo(Long tenantId, Long orderId, String processInstanceId, Integer orderType) {
        TaskParamDto build = TaskParamDto.builder()
                .approveResult(true)
                .processInstanceId(processInstanceId)
                .userId(String.valueOf(JwtUserInfoUtils.getUserId()))
                .userName(JwtUserInfoUtils.getUserName())
                .approveDesc("撤销")
                .build();
        R r = taskService.terminate(build);
        return checkResult(r, tenantId, orderId, processInstanceId, OrderProcessOperationEnum.TERMINATE.getValue(), null, null, orderType);
    }

    /**
     * 执行工单
     *
     * @param tenantId
     * @param orderId           工单ID
     * @param processInstanceId 工作流实例ID
     * @param orderType         工单类型
     * @param isSuperRole       是否是超级角色
     * @return
     */
    public Result<Void> doTask(Long tenantId, Long orderId, String processInstanceId, Integer orderType,Boolean isSuperRole) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        paramMap.put("approveResult", true);
        TaskParamDto build = TaskParamDto.builder()
                .approveResult(true)
                .processInstanceId(processInstanceId)
                .userId(TenantUtil.get() + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .paramMap(paramMap)
                .isAdmin(isSuperRole)
                .approveDesc("执行工单")
                .build();
        //先记录
        OrderProcessRecordEntity record = OrderProcessRecordEntity.builder()
                .processInstanceId(processInstanceId)
                .orderId(orderId)
                .orderType(orderType)
                .operation(OrderProcessOperationEnum.EXECUTE.getValue())
                .executorId(JwtUserInfoUtils.getUserId())
                .executorName(JwtUserInfoUtils.getUserName())
                .remark("执行工单")
                .build();
        recordService.save(Long.valueOf(TenantUtil.get()), record);
        R r = taskService.complete(build);
        if (!r.isOk()) {
            recordService.deleteById(record);
            return Result.error(r.getMsg());
        }
        return Result.ok();
    }

    /**
     * 外委非数据型工单供应商验收通过（只能验收通过）
     *
     * @param vendorTenantId    供应商租户ID
     * @param orderId           工单ID
     * @param processInstanceId 工作流实例ID
     * @param approveDesc       备注
     * @param orderType         工单类型
     * @param isSuperRole       是否是超级角色
     * @return
     */
    public Result<Void> vendorAcceptance(Long vendorTenantId, Long orderId, String processInstanceId, String approveDesc, Integer orderType,Boolean isSuperRole) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        paramMap.put("remark", approveDesc);
        paramMap.put("approveResult", true);
        TaskParamDto build = TaskParamDto.builder()
                .approveResult(true)
                .processInstanceId(processInstanceId)
                .userId(TenantUtil.get() + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .isAdmin(isSuperRole)
                .paramMap(paramMap)
                .approveDesc(approveDesc)
                .build();
        //先记录
        OrderProcessRecordEntity record = OrderProcessRecordEntity.builder()
                .processInstanceId(processInstanceId)
                .orderId(orderId)
                .orderType(orderType)
                .operation(OrderProcessOperationEnum.VENDOR_ACCEPTANCE.getValue())
                .executorId(JwtUserInfoUtils.getUserId())
                .executorName(JwtUserInfoUtils.getUserName())
                .remark(approveDesc)
                .build();
        recordService.save(vendorTenantId, record);
        R r = taskService.complete(build);
        if (!r.isOk()) {
            recordService.deleteById(record);
            return Result.error(r.getMsg());
        }
        return Result.ok();
    }

    /**
     * 完成工单 (适用于工单的最后环节，验收通过或不通过)
     *
     * @param tenantId          租户ID(如果是外委非数据型工单，传供应商租户ID，否则传自身租户ID)
     * @param orderId           工单ID
     * @param processInstanceId 工作流实例ID
     * @param approveResult     是否通过
     * @param approveDesc       备注或不通过原因
     * @param orderType         工单类型
     * @param isSuperRole       是否是超级角色
     * @return
     */
    public Result<Void> complete(Long tenantId, Long orderId, String processInstanceId, Boolean approveResult, String approveDesc, Integer orderType,Boolean isSuperRole) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("flyflow_723678314280", orderId);
        paramMap.put("orderId", orderId);
        paramMap.put("remark", approveDesc);
        paramMap.put("approveResult", approveResult);
        TaskParamDto build = TaskParamDto.builder()
                .approveResult(approveResult)
                .processInstanceId(processInstanceId)
                .userId(TenantUtil.get() + ":" + JwtUserInfoUtils.getUserId())
                .userName(JwtUserInfoUtils.getUserName())
                .isAdmin(isSuperRole)
                .paramMap(paramMap)
                .approveDesc(approveDesc)
                .build();
        R r = taskService.completeByTenantId(build, tenantId.toString());
        return checkResult(r, tenantId, orderId, processInstanceId, approveResult ? OrderProcessOperationEnum.ACCEPTANCE.getValue() : OrderProcessOperationEnum.ACCEPTANCE_FAILED.getValue(), null, approveDesc, orderType);
    }

//    /**
//     * 查询待办工单
//     * @param userId 用户ID
//     * @param pageNum 页码
//     * @param pageSize 分页长度
//     * @return
//     */
//    public Result<PageResultDto<TaskDto>> queryTodoTaskPage(Long userId, Integer pageNum, Integer pageSize){
//        TaskQueryParamDto build = new TaskQueryParamDto();
//        build.setAssign(String.valueOf(userId));
//        build.setPageNum(pageNum);
//        build.setPageSize(pageSize);
//        R<PageResultDto<TaskDto>> todoTask = flowService.queryTodoTask(build);
//        return Result.ok(todoTask.getData());
//    }

//    /**
//     * 查询已办工单
//     * @param userId 用户ID
//     * @param pageNum 页码
//     * @param pageSize 分页长度
//     * @return
//     */
//    public Result<PageResultDto<TaskDto>> queryCompletedTaskPage(Long userId, Integer pageNum, Integer pageSize){
//        TaskQueryParamDto build = new TaskQueryParamDto();
//        build.setAssign(String.valueOf(userId));
//        build.setPageNum(pageNum);
//        build.setPageSize(pageSize);
//        R<PageResultDto<TaskDto>> todoTask = flowService.queryCompletedTask(build);
//        return Result.ok(todoTask.getData());
//   }

    /**
     * 查询待办工单
     *
     * @param userId 用户ID (如果为空)
     * @return
     */
    public Result<List<TaskDto>> queryTodoTaskList(Long userId) {
        R<List<TaskDto>> todoTask = flowService.queryTodoTaskList(userId);
        return Result.ok(todoTask.getData());
    }

//    /**
//     * 查询已办工单
//     * @param userId 用户ID
//     * @return
//     */
//    public Result<List<TaskDto>> queryCompletedTaskList(Long userId){
//        R<List<TaskDto>> todoTask = flowService.queryCompletedTaskList(userId);
//        return Result.ok(todoTask.getData());
//    }
    public Result<Long> queryLastOperatorByProcessInstance(String processInstanceId) {
        Result<List<OrderProcessRecordEntity>> listByProcessInstanceId = recordService.getListByProcessInstanceId(processInstanceId);
        OrderProcessRecordEntity entity = listByProcessInstanceId.getResult().stream().filter(record -> Objects.equals(OrderProcessOperationEnum.EXECUTE.getValue(), record.getOperation())).findFirst().orElse(null);

        if (Objects.nonNull(entity)) {
            return Result.ok(entity.getExecutorId());
        }
        return Result.error();
    }


    /**
     * 最后执行人查询
     *
     * @return Map KEY:VALUE -> processInstanceId:userId
     */
    public Result<Map<String, String>> queryLastOperatorByProcessInstances(List<String> processInstanceIds) {
        Map<String, String> result = new HashMap<>();
        if (CollUtil.isEmpty(processInstanceIds)) {
            return Result.ok(result);
        }
        Map<String, List<OrderProcessRecordEntity>> map = recordService.getListByProcessInstanceIds(processInstanceIds);

        map.forEach((instanceId, records) -> {
            if (CollUtil.isEmpty(records)) {
                return;
            }

            OrderProcessRecordEntity lastEntity = records.get(0);
            OrderProcessOperationEnum operationEnum = OrderProcessOperationEnum.typeOfValue(lastEntity.getOperation());
            if (Objects.isNull(operationEnum)) {
                return;
            }
            OrderProcessOperationEnum targetOperationEnum = null;
            switch (operationEnum) {
                case TERMINATE:
                case AUTO_ACCEPT:
                case ACCEPTANCE:
                case ACCEPTANCE_FAILED:
                case VENDOR_AUTO_ACCEPT:
                case VENDOR_ACCEPTANCE:
                    return;
                case WITHDRAW:
                case WITHDRAW_EXECUTE:
                case FORWARD:
                case DISPATCH:
                case RECEIVE:
                case VENDOR_WITHDRAW:
                    Boolean isAutoDispatch = false;
                    Object variable = null;
                    try {
                        variable = runtimeService.getVariable(instanceId, AUTO_DISPATCH_FLAG);
                    }catch (Exception e){
                        log.error("查询流程变量失败，processInstanceId:{}",instanceId);
                    }
                    if (variable != null) {
                        List<SelectValue> selectValues = JsonUtil.parseArray(JsonUtil.toJSONString(variable), SelectValue.class);
                        if (CollUtil.isNotEmpty(selectValues)){
                            isAutoDispatch = ObjectUtil.equals(selectValues.get(0).getValue(),"1");
                        }
                    }
                    if (isAutoDispatch){
                        targetOperationEnum = OrderProcessOperationEnum.RECEIVE;
                    }else {
                        targetOperationEnum = OrderProcessOperationEnum.DISPATCH;
                    }
                    break;
                case EXECUTE:
                    targetOperationEnum = OrderProcessOperationEnum.EXECUTE;
                    break;
                case CUSTOMER_WITHDRAW:
                    if (Objects.equals(OrderProcessOperationEnum.typeOfValue(records.get(1).getOperation()),OrderProcessOperationEnum.VENDOR_AUTO_ACCEPT)) {
                        targetOperationEnum = OrderProcessOperationEnum.DISPATCH;
                    }else {
                        targetOperationEnum = OrderProcessOperationEnum.EXECUTE;
                    }
                    break;
                //供应商验收通过权限关闭
//                case VENDOR_ACCEPTANCE:
//                    targetOperationEnum = OrderProcessOperationEnum.VENDOR_ACCEPTANCE;
//                    break;
                case WITHDRAW_DISPATCH:
                case WITHDRAW_RECEIVE:
                case BEGIN:
                    targetOperationEnum = OrderProcessOperationEnum.BEGIN;
            }
            OrderProcessOperationEnum finalTargetOperationEnum = targetOperationEnum;
            OrderProcessRecordEntity entity = records.stream().filter(record -> Objects.equals(finalTargetOperationEnum.getValue(), record.getOperation())).findFirst().orElse(null);
            if (Objects.nonNull(entity)) {
                result.put(instanceId, entity.getTenantId() + ":" + entity.getExecutorId());
            }
        });

        return Result.ok(result);
    }

    /**
     * 最后执行节点查询
     *
     * @return Map KEY:VALUE -> processInstanceId:acceptState
     * acceptState 是否处于待验收状态
     */
    public Result<Map<String, Boolean>> queryAcceptStateByProcessInstances(List<String> processInstanceIds,Integer idType) {
        Map<String, Boolean> result = new HashMap<>();
        if (CollUtil.isEmpty(processInstanceIds)) {
            return Result.ok(result);
        }
        Map<String, List<OrderProcessRecordEntity>> map = recordService.getListByProcessInstanceIds(processInstanceIds);

        map.forEach((instanceId, records) -> {
            if (CollUtil.isEmpty(records)) {
                return;
            }
            int index = 0;
            OrderProcessRecordEntity lastEntity = records.get(0);
          // 最后一个节点是 执行和验收驳回 此时的工单状态应该为供应商验收状态
            List<String> supplierStateOperationList = Arrays.asList(OrderProcessOperationEnum.EXECUTE.getValue(),OrderProcessOperationEnum.CUSTOMER_WITHDRAW.getValue());
            // 最后一个节点是 验收通过和自动验收通过 此时的工单状态应该为客户验收状态
            List<String> customerStateOperationList = Arrays.asList(OrderProcessOperationEnum.VENDOR_ACCEPTANCE.getValue(),OrderProcessOperationEnum.VENDOR_AUTO_ACCEPT.getValue());
            if (IdTypeEnum.SUPPLIER.getValue().equals(idType)){
                result.put(instanceId, supplierStateOperationList.contains(lastEntity.getOperation()));
            }else {
                result.put(instanceId, customerStateOperationList.contains(lastEntity.getOperation()));
            }
        });

        return Result.ok(result);
    }

    /**
     * 是否有转交权限查询
     *
     * @return Map KEY:VALUE -> processInstanceId:true OR false
     */
    public Result<Map<String, Boolean>> queryForwardPermissionByProcessInstances(List<String> processInstanceIds) {
        R<Map<String, Boolean>> mapR = taskService.queryForwardPermissionByProcessInstances(processInstanceIds);
        return Result.ok(mapR.getData());
    }


    /**
     * 查询外委非数据型工单流程配置
     *
     * @return 供应商验收节点是否是自动验收
     */
    public Result<Boolean> queryVendorAutoAcceptByProcessInstanceId(String processInstanceId) {
        R<Boolean> bR = taskService.queryVendorAutoAcceptByProcessInstanceId(processInstanceId);
        return Result.ok(bR.getData());
    }

    /**
     * 查询当前任务执行人列表
     * @return 返回执行人列表 tenantId:userId
     */
    public Result<List<String>> queryExecutorByProcessInstanceId(String processInstanceId){
        R<List<String>> listR = taskService.queryExecutorByProcessInstanceId(processInstanceId);
        return Result.ok(listR.getData());
    }


}
