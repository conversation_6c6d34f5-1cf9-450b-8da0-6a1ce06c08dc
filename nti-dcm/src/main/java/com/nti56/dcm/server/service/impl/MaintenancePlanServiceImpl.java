package com.nti56.dcm.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.MaintenanceOrder;
import com.nti56.dcm.server.domain.MaintenancePlan;
import com.nti56.dcm.server.domain.OrderPlanner;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.*;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.mapper.*;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.*;
import com.nti56.dcm.server.service.*;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 保养计划表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 14:27:47
 * @since JDK 1.8
 */
@Service
@Slf4j
public class MaintenancePlanServiceImpl extends ServiceImpl<MaintenancePlanMapper, MaintenancePlanEntity>
        implements MaintenancePlanService {

    @Autowired
    private SerialNumberService serialNumberService;

    @Autowired
    private MaintenancePlanMapper mapper;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Autowired
    private MaintenanceOrderService maintenanceOrderService;

    @Autowired
    private MaintenanceItemService maintenanceItemService;

    @Autowired
    private MaintenanceOrderProgressService maintenanceOrderProgressService;

    @Autowired
    private MaintenanceStandardItemService maintenanceStandardItemService;

    @Autowired
    private DeviceEmpowerService deviceEmpowerService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private MaintenancePlanDeviceMapper maintenancePlanDeviceMapper;

    @Autowired
    private MaintenancePlanDeviceStandardMapper maintenancePlanDeviceStandardMapper;

    @Autowired
    private MaintenanceResultMapper maintenanceResultMapper;

    @Autowired
    private MaintenanceResultItemMapper maintenanceResultItemMapper;

    @Autowired
    private MaintenanceResultStandardMapper maintenanceResultStandardMapper;

    @Autowired
    private MaintenanceStandardItemMapper maintenanceStandardItemMapper;

    @Autowired
    private MaintenanceOrderMapper maintenanceOrderMapper;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private OrderProcessService orderProcessService;

    @Autowired
    private IMessageService messageService;

    @Autowired
    private AsyncSendNoticeService asyncSendNoticeService;

    private static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    @Transactional
    public void createOrderAndProgress(LocalDateTime maintenanceTime, Long tenantId, Integer idType,
                                       MaintenancePlanEntity maintenancePlan, LocalDateTime now,
                                       Integer createSource) {
        //创建工单
        long orderId = IdGenerator.generateId();
        MaintenanceOrderEntity maintenanceOrder = new MaintenanceOrderEntity();
        maintenanceOrder.setMaintenancePlanId(maintenancePlan.getId());
        maintenanceOrder.setId(orderId);
        String orderNumber = serialNumberService.getNext(tenantId, LocalDate.now(), MaintenanceOrder.serialNumber);
        maintenanceOrder.setOrderNumber(orderNumber);
        maintenanceOrder.setMaintenanceMode(maintenancePlan.getMaintenanceMode());
        maintenanceOrder.setMaintenanceStandardId(maintenancePlan.getMaintenanceStandardId());
        maintenanceOrder.setDeviceId(maintenancePlan.getDeviceId());
        maintenanceOrder.setHalt(maintenancePlan.getHalt());
        maintenanceOrder.setPlanMaintenanceTime(maintenanceTime);
        maintenanceOrder.setStatus(OrderStatusEnum.WAIT_DISPATCH.getValue());
        maintenanceOrder.setOverdue(OverdueEnum.NO.getValue());
        maintenanceOrder.setCustomerName(maintenancePlan.getCustomerName());
        maintenanceOrder.setLevel(maintenancePlan.getLevel());
        maintenanceOrder.setTenantId(tenantId);
        maintenanceOrder.setCreator(maintenancePlan.getCreator());
        maintenanceOrder.setCreatorId(maintenancePlan.getCreatorId());
        Integer outsource = maintenancePlan.getOutsource();
        maintenanceOrder.setOutsource(outsource);

        boolean isDataRecord = false;

        // 外委
        if(OutsourceEnum.YES.getValue().equals(outsource)){
            maintenanceOrder.setSupplierId(maintenancePlan.getSupplierId());
            maintenanceOrder.setSupplierName(maintenancePlan.getSupplierName());

            //客户为供应商创建计划工单 / 供应商为客户创建工单
            Result<Integer> result = customerRelationService.getTenantType(maintenancePlan.getCustomerId(), maintenancePlan.getSupplierId(), idType);
            if (result.getSignal()) {
                Integer type = result.getResult();
                if (type != null && SupplierTypeEnum.SELF_DEFINING.getValue().equals(type)) {
                    isDataRecord = true;
                }

                if (IdTypeEnum.SUPPLIER.getValue().equals(createSource)){
                    maintenanceOrder.setOutsourceCustomerType(type);
                    maintenanceOrder.setOutsourceSupplierType(SupplierTypeEnum.TENANT.getValue());
                } else if (IdTypeEnum.CUSTOMER.getValue().equals(createSource)) {
                    maintenanceOrder.setOutsourceSupplierType(type);
                    maintenanceOrder.setOutsourceCustomerType(SupplierTypeEnum.TENANT.getValue());
                }
            }
        }

        maintenanceOrder.setCreateSource(createSource);
        //供应商/集成商为客户创建计划工单
        if (IdTypeEnum.SUPPLIER.getValue().equals(createSource)) {
            maintenanceOrder.setTenantId(maintenancePlan.getTenantId());
            maintenanceOrder.setCustomerName(maintenancePlan.getCustomerName());
        }
        maintenanceOrderService.save(maintenanceOrder);

        // 启动工单流程
        Result<StartFlowDto> startFlowResult = null;
        try {
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                if (isDataRecord) {
                    startFlowResult = orderProcessService.startFlow(
                            tenantId,
                            idType,
                            OrderTypeEnum.MAINTENANCE.getValue(),
                            OutsourceEnum.YES.getValue().equals(outsource),
                            isDataRecord,
                            orderId,
                            ResponsibleDto.builder().id(maintenancePlan.getCreatorId()).name(maintenancePlan.getCreator()).build()
                    );
                } else {
                    startFlowResult = orderProcessService.startFlow(
                            tenantId,
                            OrderTypeEnum.MAINTENANCE.getValue(),
                            orderId,
                            ResponsibleDto.builder().id(maintenancePlan.getCreatorId()).name(maintenancePlan.getCreator()).build(),
                            maintenanceOrder.getSupplierId(),
                            isDataRecord,
                            YesNoEnum.NO.getValue()
                    );
                }
            } else{
                startFlowResult = orderProcessService.startFlow(
                        tenantId,
                        idType,
                        OrderTypeEnum.MAINTENANCE.getValue(),
                        OutsourceEnum.YES.getValue().equals(outsource),
                        isDataRecord,
                        orderId,
                        ResponsibleDto.builder().id(maintenancePlan.getCreatorId()).name(maintenancePlan.getCreator()).build()
                );
            }
        } catch (Exception e) {
            log.error("startFlow fail {}, {}, {}, {}, {}, {}",
                    tenantId, idType, OrderTypeEnum.MAINTENANCE.getValue(),
                    OutsourceEnum.YES.getValue().equals(outsource),
                    isDataRecord, orderId
            );
            throw new BizException(e.getMessage());
        }

        if(!startFlowResult.getSignal()){
            throw new BizException(startFlowResult.getMessage());
        }

        String processInstanceId = startFlowResult.getResult().getProcessInstanceId();
        maintenanceOrder.setProcessInstanceId(processInstanceId);
        if (startFlowResult.getResult().getIsAutoDispatch()){
            maintenanceOrder.setStatus(OrderStatusEnum.WAIT_RECEIVE.getValue());
        }
        maintenanceOrderMapper.updateById(maintenanceOrder);
//        maintenanceOrderMapper.updateProcessInstanceIdById(orderId, processInstanceId);

        // 复制标准，创建工单结果
        // 先查出计划涉及的设备和标准
        Long maintenancePlanId = maintenancePlan.getId();
        List<MaintenancePlanDeviceEntity> planDeviceList = maintenancePlanDeviceMapper.listByPlanId(maintenancePlanId);
        if(planDeviceList != null){
            // 每个设备建一个工单结果
            planDeviceList.forEach(planDevice -> {
                MaintenanceResultEntity maintenanceResultEntity = new MaintenanceResultEntity();
                maintenanceResultEntity.setTenantId(tenantId);
                maintenanceResultEntity.setMaintenanceOrderId(orderId);
                maintenanceResultEntity.setDeviceId(planDevice.getDeviceId());
                maintenanceResultEntity.setStatus(MaintenanceResultStatusEnum.NOT_MAINTENANCE.getValue());
                maintenanceResultMapper.insert(maintenanceResultEntity);
                Long resultId = maintenanceResultEntity.getId();
                // 每个设备对应多个标准
                List<MaintenancePlanDeviceStandardEntity> planDeviceStandards = maintenancePlanDeviceStandardMapper.listByPlanDeviceId(planDevice.getId());
                if(planDeviceStandards == null){
                    return;
                }
                planDeviceStandards.forEach(planDeviceStandard -> {
                    Long maintenanceStandardId = planDeviceStandard.getMaintenanceStandardId();
                    MaintenanceResultStandardEntity resultStandardEntity = new MaintenanceResultStandardEntity();
                    resultStandardEntity.setTenantId(tenantId);
                    resultStandardEntity.setMaintenanceResultId(resultId);
                    resultStandardEntity.setMaintenanceStandardId(maintenanceStandardId);
                    maintenanceResultStandardMapper.insert(resultStandardEntity);

                    // 根据标准创建工单结果项
                    List<MaintenanceStandardItemEntity> standardItemList = maintenanceStandardItemMapper.listByStandardId(maintenanceStandardId);
                    if(standardItemList == null){
                        return;
                    }
                    standardItemList.forEach(standardItem -> {
                        MaintenanceResultItemEntity resultItemEntity = new MaintenanceResultItemEntity();
                        resultItemEntity.setTenantId(tenantId);
                        resultItemEntity.setMaintenanceResultId(resultId);
                        resultItemEntity.setMaintenanceStandardItemId(standardItem.getId());
                        maintenanceResultItemMapper.insert(resultItemEntity);
                    });
                });
            });
        }

        // 发送通知
        asyncSendNoticeService.sendOrderNoticeSingle(processInstanceId, maintenanceOrder.getOrderNumber(),
                maintenanceOrder.getOutsource(), tenantId, idType, OrderTypeEnum.MAINTENANCE,startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE: OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(maintenanceOrder.getTimeout()), maintenanceOrder.getCustomerName());

    }

    private Set<Integer> parsePeriodInspectDay(String periodInspectDayStr){
        Set<Integer> periodInspectDaySet = new HashSet<>();
        if(periodInspectDayStr != null && !"".equals(periodInspectDayStr)){
            String[] split = periodInspectDayStr.split(",");
            for(String s:split){
                periodInspectDaySet.add(Integer.valueOf(s));
            }
        }
        return periodInspectDaySet;
    }

    @Override
    @Transactional
    public Result save(TenantIsolation tenantIsolation, MaintenancePlanDto dto) {
        Result<String> tenantNameResult = userCenterService.getTenantNameById(tenantIsolation.getTenantId());
        if(!tenantNameResult.getSignal()){
            return Result.error(tenantNameResult.getMessage());
        }
        String tenantName = tenantNameResult.getResult();
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        Integer createSource = dto.getCreateSource();

        MaintenancePlanEntity maintenancePlan = BeanUtilsIntensifier.copyBean(dto, MaintenancePlanEntity.class);

        //委外工单 先判断设备是否授权
        if (OutsourceEnum.YES.getValue().equals(maintenancePlan.getOutsource())) {
            if (IdTypeEnum.CUSTOMER.getValue().equals(dto.getCreateSource())) {
                if (ObjectUtil.isNull(maintenancePlan.getSupplierId()) || StringUtils.isEmpty(maintenancePlan.getSupplierName())) {
                    return Result.error("创建委外保养计划时，请传入供应商id和名称!");
                }
//                List<DeviceEmpowerEntity> deviceEmpowerEntityList = deviceEmpowerService.list(Wrappers.<DeviceEmpowerEntity>lambdaQuery()
//                        .eq(DeviceEmpowerEntity::getCustomerId, tenantId)
//                        .eq(DeviceEmpowerEntity::getSupplierId, maintenancePlan.getSupplierId())
//                        .eq(DeviceEmpowerEntity::getDeviceId, maintenancePlan.getDeviceId())
//                        .eq(DeviceEmpowerEntity::getDeleted, 0));
//                if (CollUtil.isEmpty(deviceEmpowerEntityList)) {
//                    return Result.error("设备未授权给此供应商:" + dto.getSupplierName() + ",请先进行授权！");
//                }
            }
            if (IdTypeEnum.SUPPLIER.getValue().equals(dto.getCreateSource())) {
                if (ObjectUtil.isNull(maintenancePlan.getCustomerId()) || StringUtils.isEmpty(maintenancePlan.getCustomerName())) {
                    return Result.error("为客户创建保养计划时，请传入客户id和名称!");
                }
            }
        }

        //客户为供应商/集成商创建计划
        if (IdTypeEnum.CUSTOMER.getValue().equals(createSource)) {
            maintenancePlan.setCustomerId(tenantId);
            maintenancePlan.setCustomerName(tenantName);

            maintenancePlan.setOutsourceCustomerType(SupplierTypeEnum.TENANT.getValue());
        }
        //供应商/集成商为客户创建计划
        if (IdTypeEnum.SUPPLIER.getValue().equals(createSource)) {
            maintenancePlan.setOutsource(OutsourceEnum.YES.getValue());
            maintenancePlan.setSupplierId(tenantId);
            maintenancePlan.setSupplierName(tenantName);
            maintenancePlan.setCustomerId(dto.getCustomerId());
            maintenancePlan.setCustomerName(dto.getCustomerName());
            maintenancePlan.setTenantId(dto.getCustomerId());

            maintenancePlan.setOutsourceSupplierType(SupplierTypeEnum.TENANT.getValue());
        }

        // 计划名不能重复
        List<MaintenancePlanEntity> list = this.list(Wrappers.<MaintenancePlanEntity>lambdaQuery()
                .eq(MaintenancePlanEntity::getTenantId, maintenancePlan.getCustomerId())
                .eq(MaintenancePlanEntity::getPlanName, maintenancePlan.getPlanName()));
        if (CollectionUtil.isNotEmpty(list)) {
            return Result.error("保养计划名称已存在：" + maintenancePlan.getPlanName());
        }

        long id = IdGenerator.generateId();
        LocalDateTime now = LocalDateTime.now();
        maintenancePlan.setId(id);
        String orderNumber = serialNumberService.getNext(tenantId, now.toLocalDate(), MaintenancePlan.serialNumber);
        maintenancePlan.setPlanNumber(orderNumber);
        maintenancePlan.setIsStop(DictConstant.NOT_STOP);
        maintenancePlan.setIdType(idType);

        // 工单生成策略
        Result<OrderPlanner> plannerResult = OrderPlanner.checkInfo(
                maintenancePlan.getMaintenanceMode(),
                maintenancePlan.getSingleMaintenanceTime(),
                maintenancePlan.getPeriodMaintenanceBegin(),
                maintenancePlan.getPeriodMaintenanceEnd(),
                maintenancePlan.getPeriodMaintenanceType(),
                maintenancePlan.getPeriodMaintenanceTime(),
                maintenancePlan.getPeriodMaintenanceDay(),
                maintenancePlan.getMultiMaintenanceTime(),
                maintenancePlan.getOrderCreateStrategy(),
                maintenancePlan.getCreateBeforeNumber(),
                maintenancePlan.getCreateBeforeUnit());

        if(!plannerResult.getSignal()){
            return Result.error(plannerResult.getMessage());
        }
        OrderPlanner orderPlanner = plannerResult.getResult();

        if (mapper.insert(maintenancePlan) != 1) {
            return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
        }

        // 插入计划关联的设备及标准
        List<MaintenancePlanDeviceDto> planDeviceDtos = dto.getDeviceList();
        if(planDeviceDtos != null){
            planDeviceDtos.forEach(t -> {
                // 插入关联设备
                MaintenancePlanDeviceEntity entity = new MaintenancePlanDeviceEntity();
                BeanUtils.copyProperties(t, entity);
                entity.setMaintenancePlanId(maintenancePlan.getId());
                entity.setId(null);
                maintenancePlanDeviceMapper.insert(entity);
                // 插入设备关联的标准
                List<IdNameDto> standardList = t.getStandardList();
                if(standardList != null){
                    standardList.forEach(standard -> {
                        MaintenancePlanDeviceStandardEntity e = new MaintenancePlanDeviceStandardEntity();
                        e.setMaintenancePlanDeviceId(entity.getId());
                        e.setMaintenanceStandardId(standard.getId());
                        e.setId(null);
                        maintenancePlanDeviceStandardMapper.insert(e);
                    });
                }
            });
        }

        if(OrderCreateStrategyEnum.BEFORE.getValue().equals(dto.getOrderCreateStrategy())){
            // 提前生成

            // 第一次生成时间不能小于当前时间
            LocalDateTime firstCreateTime = orderPlanner.getFirstCreateTime();
            if(firstCreateTime == null){
                throw new RuntimeException("无法获取第一次工单生成时间");
            }
            if(firstCreateTime.isBefore(now)){
                throw new RuntimeException("工单生成时间不能小于当前时间");
            }

            // 更新下次工单生成时间
            Integer count = mapper.updateNextOrderCreateTime(maintenancePlan.getId(), firstCreateTime);

            // 剩下的交给定时器

            return Result.ok();
        }

        // 立即生成

        final AtomicInteger orderCreateCount = new AtomicInteger(0);
        orderPlanner.iterateEach(t -> {
            createOrderAndProgress(
                    t,
                    tenantId,
                    idType,
                    maintenancePlan,
                    now,
                    createSource
            );
            orderCreateCount.incrementAndGet();
        });
        if(orderCreateCount.get() <= 0){
            throw new BizException("计划创建的工单数量不能为0，请选择有效的时间和周期");
        }
        return Result.ok();
    }

    @Override
    public Result<Page<MaintenancePlanVo>> getPage(QueryMaintenancePlanDto dto, Page<MaintenancePlanVo> page) {
        Page<MaintenancePlanVo> voPage = mapper.pageMaintenancePlan(page, dto);
        List<MaintenancePlanVo> maintenancePlanVoList = voPage.getRecords();
        List<Long> planIds = maintenancePlanVoList.stream().map(MaintenancePlanVo::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(planIds)) {
            List<MaintenanceOrderEntity> orderList = maintenanceOrderService.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                    .in(MaintenanceOrderEntity::getMaintenancePlanId, planIds));
            maintenancePlanVoList.forEach(plan -> {
                List<MaintenanceOrderEntity> list = orderList.stream().filter(o -> plan.getId().equals(o.getMaintenancePlanId())).collect(Collectors.toList());
                plan.setAllOrderCount(list.size());
                plan.setFinishOrderCount(list.stream().filter(o ->
                        OrderStatusEnum.ACCEPTED.getValue().equals(o.getStatus())
                                || OrderStatusEnum.REJECT.getValue().equals(o.getStatus())).collect(Collectors.toList()).size());
                plan.setProcessingOrderCount(list.stream().filter(o ->
                        OrderStatusEnum.WAIT_DISPATCH.getValue().equals(o.getStatus())
                                || OrderStatusEnum.WAIT_RECEIVE.getValue().equals(o.getStatus())
                                || OrderStatusEnum.EXECUTING.getValue().equals(o.getStatus())
                                || OrderStatusEnum.WAIT_ACCEPT.getValue().equals(o.getStatus())).collect(Collectors.toList()).size());
            });
            
            // 设备数统计
            Map<String, CountVo> countMap = maintenancePlanDeviceMapper.countByMaintenancePlanIds(planIds);
            maintenancePlanVoList.forEach(t -> {
                CountVo countVo = countMap.get(t.getId().toString());
                if(countVo != null){
                    t.setDeviceCount(countVo.getValue());
                }else{
                    t.setDeviceCount(0);
                }
            });

            // 操作按钮
            Long currentUserId = JwtUserInfoUtils.getUserId();
            maintenancePlanVoList.forEach(vo -> {
                vo.setOperations(listPlanOperations(vo, currentUserId));
            });
        }
        return Result.ok(voPage);
    }

    private List<String> listPlanOperations(MaintenancePlanVo vo, Long currentUserId){
        List<String> operations = new ArrayList<>();
        // 停止按钮
        if(vo.getCreatorId().equals(currentUserId)){
            if(PlanStatusEnum.PROCESSING.getValue().equals(vo.getStatus())){
                operations.add(OperationEnum.STOP.getName());
            }
        }
        return operations;
    }

    @Override
    @Transactional
    public Result stopPlan(TenantIsolation tenantIsolation, Long entityId) {
        MaintenancePlanEntity entity = this.getById(entityId);
        if (entity == null) {
            return Result.error("保养计划不存在");
        }

        this.update(Wrappers.<MaintenancePlanEntity>lambdaUpdate()
                .set(MaintenancePlanEntity::getIsStop, DictConstant.HAS_STOP)
                .set(MaintenancePlanEntity::getNextOrderCreateTime, null)
                .set(MaintenancePlanEntity::getStatus, PlanStatusEnum.FINISH.getValue())
                .eq(MaintenancePlanEntity::getId, entityId));
        //删除未完成的保养工单、工单明细
        List<MaintenanceOrderEntity> unFinishOrderList = maintenanceOrderService.list(Wrappers.<MaintenanceOrderEntity>lambdaQuery()
                .eq(MaintenanceOrderEntity::getMaintenancePlanId, entityId)
                .and(wrapper -> wrapper.eq(MaintenanceOrderEntity::getStatus, OrderStatusEnum.WAIT_DISPATCH.getValue())
                        .or().eq(MaintenanceOrderEntity::getStatus, OrderStatusEnum.WAIT_RECEIVE.getValue())
                        .or().eq(MaintenanceOrderEntity::getStatus, OrderStatusEnum.EXECUTING.getValue())
                        .or().eq(MaintenanceOrderEntity::getStatus, OrderStatusEnum.WAIT_ACCEPT.getValue())));
        List<Long> orderIds = unFinishOrderList.stream().map(MaintenanceOrderEntity::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(orderIds)) {
            maintenanceOrderService.removeByIds(orderIds);
//            maintenanceItemService.remove(Wrappers.<MaintenanceItemEntity>lambdaQuery()
//                    .in(MaintenanceItemEntity::getMaintenanceOrderId, orderIds));
//            maintenanceOrderProgressService.remove(Wrappers.<MaintenanceOrderProgressEntity>lambdaQuery()
//                    .in(MaintenanceOrderProgressEntity::getMaintenanceOrderId, orderIds));
        }
        return Result.ok();
    }

    @Override
    public Result<MaintenancePlanDto> getPlanDetailById(TenantIsolation tenantIsolation, Long id) {
        // 获取计划
        MaintenancePlanEntity entity = this.getById(id);
        if(entity == null){
            return Result.error("保养计划不存在");
        }

        MaintenancePlanDto dto = new MaintenancePlanDto();
        BeanUtils.copyProperties(entity, dto);

        // 获取计划关联的设备
        List<MaintenancePlanDeviceVo> planDeviceList = maintenancePlanDeviceMapper.listByPlanIdWithDeviceInfo(id);
        if(planDeviceList == null){
            planDeviceList = new ArrayList<>();
        }

        // 获取设备关联的标准
        List<Long> planDeviceIds = planDeviceList.stream().map(MaintenancePlanDeviceVo::getId).collect(Collectors.toList());
        List<MaintenancePlanDeviceStandardVo> planDeviceStandards = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(planDeviceIds)) {
            planDeviceStandards = maintenancePlanDeviceStandardMapper.listByPlanDeviceIds(planDeviceIds);
        }

        // maintenancePlanDeviceId -> standardList
        Map<Long, List<MaintenancePlanDeviceStandardVo>> map = planDeviceStandards.stream().collect(Collectors.groupingBy(MaintenancePlanDeviceStandardVo::getMaintenancePlanDeviceId));

        List<MaintenancePlanDeviceDto> planDevices = new ArrayList<>();
        planDeviceList.forEach(t -> {
            MaintenancePlanDeviceDto maintenancePlanDeviceDto = new MaintenancePlanDeviceDto();
            BeanUtils.copyProperties(t, maintenancePlanDeviceDto);
            Long maintenancePlanDeviceId = t.getId();
            List<MaintenancePlanDeviceStandardVo> standardList = map.get(maintenancePlanDeviceId);
            if(standardList == null){
                standardList = new ArrayList<>();
            }
            List<IdNameDto> dtoList = standardList.stream().map(v -> {
                IdNameDto d = new IdNameDto();
                d.setId(v.getMaintenanceStandardId());
                d.setName(v.getStandardName());
                return d;
            }).collect(Collectors.toList());
            maintenancePlanDeviceDto.setStandardList(dtoList);
            planDevices.add(maintenancePlanDeviceDto);
        });

        dto.setDeviceList(planDevices);

        return Result.ok(dto);
    }

}
