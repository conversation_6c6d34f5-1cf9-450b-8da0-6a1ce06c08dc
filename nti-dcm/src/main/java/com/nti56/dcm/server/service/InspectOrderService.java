package com.nti56.dcm.server.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.mapper.*;
import com.nti56.dcm.server.model.vo.*;
import com.nti56.flowable.common.dto.R;
import com.nti56.flowable.common.dto.flow.SelectValue;
import com.nti56.flowable.common.service.biz.IRemoteService;
import com.nti56.flowable.common.utils.JsonUtil;
import liquibase.pro.packaged.S;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.nti56.common.constant.Constant;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.*;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.msg.center.service.ISendNotifyService;
import com.nti56.msg.center.service.ITemplateService;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.Multiset;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.dcm.server.domain.InspectOrder;
import com.nti56.dcm.server.domain.UserRoleAuth;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.model.dto.InspectOutboundOrderDto.WarehouseSparesDto;
import com.nti56.flowable.common.dto.TaskDto;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import static com.nti56.flowable.common.constants.ProcessInstanceConstant.VariableKey.AUTO_DISPATCH_FLAG;

/**
 * <p>
 * 点巡检工单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Service
@Slf4j
public class InspectOrderService {

    private static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Autowired
    private InspectOrderMapper inspectOrderMapper;

    @Autowired
    private UserRoleAuthService userRoleAuthService;

    @Autowired
    private InspectOrderProgressMapper inspectOrderProgressMapper;

    @Autowired
    private InspectPlanDeviceMapper inspectPlanDeviceMapper;

    @Autowired
    private InspectResultMapper inspectResultMapper;

    @Autowired
    private InspectStandardItemMapper inspectStandardItemMapper;

    @Autowired
    private InspectResultItemMapper inspectResultItemMapper;

    @Autowired
    private InspectPlanDeviceStandardMapper inspectPlanDeviceStandardMapper;

    @Autowired
    private FileMapper fileMapper;

    @Autowired
    private InspectResultStandardMapper inspectResultStandardMapper;

    @Autowired
    private TenantInfoService tenantInfoService;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private SerialNumberService serialNumberService;
    
    @Autowired
    private WorkloadMapper workloadMapper;

    @Autowired
    private TimeoutConfigService timeoutConfigService;

    @Autowired
    private IRemoteService remoteService;

    @Autowired
    private IMessageService messageService;
    
    @Autowired
    private OrderProcessService orderProcessService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private AlarmRecordService alarmRecordService;

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private AsyncSendNoticeService asyncSendNoticeService;

    @Autowired
    private MessageNoticeStrategyMapper messageNoticeStrategyMapper;

    @Autowired
    private AlarmRecordMapper alarmRecordMapper;

    @Resource
    private RuntimeService runtimeService;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public Result<Void> acceptOrder(Long tenantId, Integer idType, InspectOrderAcceptDto dto) {
        
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是待验收状态
        if(!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(entity.getStatus())){
            return Result.error("验收工单失败，工单不是待验收状态");
        }

        // 检查当前用户是否有验收权限
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 修改工单状态
        Integer acceptResult = dto.getAcceptResult();
        if(YesNoEnum.YES.getValue().equals(acceptResult)){
            Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.ACCEPTED.getValue());
        }else{
            Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.REJECT.getValue());
        }

        
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantId, idType);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantId, currentUserId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;

        // 验收巡检工单流程
        Integer outsource = entity.getOutsource();
        Integer outsourceCustomerType = entity.getOutsourceCustomerType();
        Integer outsourceSupplierType = entity.getOutsourceSupplierType();
        if(OutsourceEnum.YES.getValue().equals(outsource)){
            // 外委工单
            if(SupplierTypeEnum.SELF_DEFINING.getValue().equals(outsourceSupplierType)
                || SupplierTypeEnum.SELF_DEFINING.getValue().equals(outsourceCustomerType)
            ){
                // 数据型，验收
                Result<Void> completeResult = orderProcessService.complete(
                    tenantId, 
                    inspectOrderId, 
                    entity.getProcessInstanceId(), 
                    YesNoEnum.YES.getValue().equals(acceptResult), 
                    dto.getAcceptDesc(),
                    OrderTypeEnum.INSPECT.getValue(),
                    isSuperRole
                );
                if(!completeResult.getSignal()){
                    throw new BizException(completeResult.getMessage());
                }
            }else{
                // 租户型
                if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
                    // 供应商验收
                    inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.WAIT_ACCEPT.getValue());
                    Result<Void> vendorAcceptanceResult = orderProcessService.vendorAcceptance(
                        entity.getTenantId(), 
                        inspectOrderId, 
                        entity.getProcessInstanceId(), 
                        dto.getAcceptDesc(), 
                        OrderTypeEnum.INSPECT.getValue(),
                        isSuperRole
                    );
                    if(!vendorAcceptanceResult.getSignal()){
                        throw new BizException(vendorAcceptanceResult.getMessage());
                    }
                    // 发送站内信
                    asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                            entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, OrderStatusEnum.WAIT_ACCEPT, TimeoutEnum.typeOfValue(entity.getTimeout()), null);
                }else{
                    // 客户验收
                    Result<Void> completeResult = orderProcessService.complete(
                        entity.getSupplierId(), 
                        inspectOrderId, 
                        entity.getProcessInstanceId(), 
                        YesNoEnum.YES.getValue().equals(acceptResult), 
                        dto.getAcceptDesc(),
                        OrderTypeEnum.INSPECT.getValue(),
                        isSuperRole
                    );
                    if(!completeResult.getSignal()){
                        throw new BizException(completeResult.getMessage());
                    }
                }
            }
        }else{
            // 内委工单
            Result<Void> completeResult = orderProcessService.complete(
                tenantId, 
                inspectOrderId, 
                entity.getProcessInstanceId(), 
                YesNoEnum.YES.getValue().equals(acceptResult), 
                dto.getAcceptDesc(),
                OrderTypeEnum.INSPECT.getValue(),
                isSuperRole
            );
            if(!completeResult.getSignal()){
                throw new BizException(completeResult.getMessage());
            }
        }

        // 处理工单告警记录
        alarmRecordService.finishAlarm(inspectOrderId, OrderTypeEnum.INSPECT.getValue());

        return Result.ok();
    }

    @Transactional
    public Result<Void> autoAcceptOrder(Long inspectOrderId) {
        // 检查工单是否存在
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是待验收状态
        if(!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(entity.getStatus())){
            return Result.error("验收工单失败，工单不是待验收状态");
        }

        // 检查当前用户是否有验收权限
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.ACCEPTED.getValue());

        // 处理工单告警记录
        alarmRecordService.finishAlarm(inspectOrderId, OrderTypeEnum.INSPECT.getValue());

        return Result.ok();
    }

    @Transactional
    public Result<Void> submitDeviceResult(Long tenantId, Integer idType, InspectResultDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity orderEntity = inspectOrderMapper.selectById(inspectOrderId);
        if(orderEntity == null){
            return Result.error("找不到工单，inspectOrderId:" + inspectOrderId);
        }
        if(!tenantId.equals(orderEntity.getTenantId()) 
            && !tenantId.equals(orderEntity.getSupplierId()) 
            && !tenantId.equals(orderEntity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        Long deviceId = dto.getDeviceId();
        // 检查工单结果是否存在
        InspectResultEntity inspectResultEntity = inspectResultMapper.getByOrderDeviceId(inspectOrderId, deviceId);
        if(inspectResultEntity == null){
            return Result.error("找不到工单结果，inspectOrderId:" + inspectOrderId + ", deviceId:" + deviceId);
        }
        // 校验开始时间和结束时间
        LocalDateTime inspectBegin = dto.getInspectBegin();
        if(inspectBegin == null){
            return Result.error("检查开始时间不能为空");
        }
        LocalDateTime inspectEnd = dto.getInspectEnd();
        if(inspectEnd == null){
            return Result.error("检查结束时间不能为空");
        }
        if(inspectEnd.isBefore(inspectBegin)){
            return Result.error("检查结束时间不能小于开始时间");
        }
        // 更新工单结果
        inspectResultEntity.setStatus(InspectResultStatusEnum.INSPECTED.getValue());
        inspectResultEntity.setInspectBegin(dto.getInspectBegin());
        inspectResultEntity.setInspectEnd(dto.getInspectEnd());
        inspectResultEntity.setIsError(dto.getIsError());
        inspectResultMapper.updateById(inspectResultEntity);
        
        // 更新检查结果项
        // 查询出所有结果项
        Long inspectResultId = inspectResultEntity.getId();
        List<InspectResultItemVo> itemVoList = inspectResultItemMapper.listVoByResultId(inspectResultId);
        // 提交结果转map
        // inspectResultItemId -> itemDto
        Map<Long, InspectResultItemDto> submitItemMap = new HashMap<>();
        List<InspectResultItemDto> itemList = dto.getItemList();
        if(itemList != null){
            itemList.forEach(t -> {
                submitItemMap.put(t.getId(), t);
            });
        }
        // 每个结果项都要有对应的提交
        if(itemVoList != null){
            for(InspectResultItemVo t:itemVoList){
                Long inspectResultItemId = t.getId();
                InspectResultItemDto itemDto = submitItemMap.get(inspectResultItemId);
                if(itemDto == null){
                    Long inspectStandardItemId = t.getInspectStandardItemId();
                    InspectStandardItemEntity standardItem = inspectStandardItemMapper.selectById(inspectStandardItemId);
                    throw new BizException("缺少检查项，" + standardItem.getItemName());
                }
                // 检查必须拍照项
                List<FileVo> fileList = itemDto.getFileList();
                if(YesNoEnum.YES.getValue().equals(t.getRequiredPicture())){
                    if(fileList == null || fileList.size() <= 0){
                        Long inspectStandardItemId = t.getInspectStandardItemId();
                        InspectStandardItemEntity standardItem = inspectStandardItemMapper.selectById(inspectStandardItemId);
                        throw new BizException("项目缺少拍照，" + standardItem.getItemName());
                    }
                }
                // 删除旧照片
                Long bizId = t.getId();
                fileMapper.deleteByBizId(FileBizTypeEnum.INSPECT_RESULT_ITEM_PICTURE.getValue(), bizId);
                // 新照片入库
                if(fileList != null && fileList.size() > 0){
                    fileList.forEach(f -> {
                        FileEntity e = new FileEntity();
                        BeanUtils.copyProperties(f, e);
                        e.setBizId(bizId);
                        e.setTenantId(tenantId);
                        e.setBizType(FileBizTypeEnum.INSPECT_RESULT_ITEM_PICTURE.getValue());
                        fileMapper.insert(e);
                    });
                }
                t.setResultValue(itemDto.getResultValue());
                t.setIsError(itemDto.getIsError());
                t.setRemark(itemDto.getRemark());
                inspectResultItemMapper.updateById(t);
            }
        }

        return Result.ok();
    }

    @Transactional
    public Result<Void> executeOrder(Long tenantId, Integer idType, InspectOrderExecuteDto dto) {
        Result<Void> tempResult = tempExecuteOrder(tenantId, idType, dto);
        if(!tempResult.getSignal()){
            return Result.error(tempResult.getMessage());
        }

        Long inspectOrderId = dto.getInspectOrderId();

        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();
        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.WAIT_ACCEPT.getValue());
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantId, idType);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantId, currentUserId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        // 执行巡检工单流程
        Result<Void> doTaskResult = orderProcessService.doTask(
            tenantId, 
            inspectOrderId, 
            entity.getProcessInstanceId(), 
            OrderTypeEnum.INSPECT.getValue(),
                isSuperRole
        );
        if(!doTaskResult.getSignal()){
            throw new BizException(doTaskResult.getMessage());
        }

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, OrderStatusEnum.WAIT_ACCEPT,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsource()) ? entity.getCustomerName() : null);

        return Result.ok();
    }

    @Transactional
    public Result<Void> withdrawOrder(Long tenantId, Integer idType, InspectOrderWithdrawDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是待验收状态
        if(!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(entity.getStatus())
         || !OrderStatusEnum.WAIT_ACCEPT.getValue().equals(entity.getStatus())
        ){
            return Result.error("驳回失败，工单不是待验收状态");
        }

        
        // 检查当前用户是否有验收权限
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.EXECUTING.getValue());

        
        Integer createSource = entity.getCreateSource();
        Integer outsource = entity.getOutsource();
        Integer outsourceCustomerType = entity.getOutsourceCustomerType();
        Integer outsourceSupplierType = entity.getOutsourceSupplierType();

        // 驳回巡检工单流程
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.EXECUTING;
        if(OutsourceEnum.YES.getValue().equals(outsource)){
            // 外委工单
            if(SupplierTypeEnum.SELF_DEFINING.getValue().equals(outsourceSupplierType)
                || SupplierTypeEnum.SELF_DEFINING.getValue().equals(outsourceCustomerType)
            ){
                // 数据型，驳回
                Result<Void> undoResult = orderProcessService.rejectedTask(
                    tenantId, 
                    inspectOrderId, 
                    entity.getProcessInstanceId(), 
                    dto.getReason(),
                    OrderTypeEnum.INSPECT.getValue()
                );
                if(!undoResult.getSignal()){
                    throw new BizException(undoResult.getMessage());
                }
            }else{
                // 租户型
                Result<Void> undoResult = orderProcessService.rejectedTask(
                    entity.getSupplierId(), 
                    inspectOrderId, 
                    entity.getProcessInstanceId(), 
                    dto.getReason(),
                    OrderTypeEnum.INSPECT.getValue(),
                    true,
                    IdTypeEnum.typeOfValue(idType)
                );
                if(!undoResult.getSignal()){
                    throw new BizException(undoResult.getMessage());
                }
                Result<Boolean> r = orderProcessService.queryVendorAutoAcceptByProcessInstanceId(entity.getProcessInstanceId());
                if(!r.getSignal()){
                    throw new BizException(r.getMessage());
                }
                Boolean isAuto = r.getResult();
                if(!isAuto && IdTypeEnum.CUSTOMER.getValue().equals(idType)){
                    inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.WAIT_ACCEPT.getValue());
                    orderStatusEnum = OrderStatusEnum.WAIT_ACCEPT;
                }
            }
        }else{
            // 内委工单
            Result<Void> undoResult = orderProcessService.rejectedTask(
                tenantId, 
                inspectOrderId, 
                entity.getProcessInstanceId(), 
                dto.getReason(),
                OrderTypeEnum.INSPECT.getValue()
            );
            if(!undoResult.getSignal()){
                throw new BizException(undoResult.getMessage());
            }
        }

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, orderStatusEnum,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsource()) ? entity.getCustomerName() : null);

        return Result.ok();
    }

    @Transactional
    public Result<Void> tempExecuteOrder(Long tenantId, Integer idType, InspectOrderExecuteDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是执行中状态
        if(!OrderStatusEnum.EXECUTING.getValue().equals(entity.getStatus())){
            return Result.error("执行工单失败，工单不是执行中状态");
        }

        // 检查当前用户是否有接单权限
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 删除人员工作量
        Integer deleteCount = workloadMapper.deleteByOrderId(OrderTypeEnum.INSPECT.getValue(), inspectOrderId);

        // 插入人员工作量
        List<WorkloadEntity> workloadList = dto.getWorkloadList();
        if(workloadList != null){
            for(WorkloadEntity t:workloadList){
                t.setId(null);
                t.setOrderId(inspectOrderId);
                t.setOrderType(OrderTypeEnum.INSPECT.getValue());
                LocalDateTime beginTime = t.getBeginTime();
                LocalDateTime endTime = t.getEndTime();
                if(beginTime != null && endTime != null){
                    if(beginTime.isAfter(endTime)){
                        throw new RuntimeException("开始时间不能大于结束时间");
                    }
                    Long minutes = ChronoUnit.MINUTES.between(beginTime, endTime);
                    t.setCostTime(minutes.intValue());
                }
                workloadMapper.insert(t);
            }
        }

        if (dto.getSupportSupplierId()!=null){
            inspectOrderMapper.updateSupportSupplierIdById(entity.getId(),dto.getSupportSupplierId());
        }
        return Result.ok();
    }

    @Autowired
    private IWorkGroupUserService  workGroupUserService;
    @Transactional
    public Result<Void> receiveOrder(Long tenantId, Integer idType, OrderReceiveDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId())
                && !tenantId.equals(entity.getSupplierId())
                && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }

        // 检查工单是否是待派单状态
        if(!OrderStatusEnum.WAIT_RECEIVE.getValue().equals(entity.getStatus())){
            if(OrderStatusEnum.REVOKE.getValue().equals(entity.getStatus())){
                return Result.error("接单失败，工单已撤销");
            }
            return Result.error("接单失败，工单已被接收");
        }

        // 检查当前用户是否有派单权限
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();
        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.EXECUTING.getValue());

        // 修改接收人
        Integer updateExecuteUserIdCount = inspectOrderMapper.updateExecuteUserIdById(inspectOrderId, currentUserId);
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantId, idType);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantId, currentUserId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        // 派工巡检工单流程
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        tenantIsolation.setIdType(idType);
        String workGroupNameByUserId = workGroupUserService.getWorkGroupNameByUserId(OrderTypeEnum.INSPECT.getValue(), currentUserId, tenantIsolation);
        if (StrUtil.isNotBlank(workGroupNameByUserId)) {
            currentUserName = currentUserName + "(" +workGroupNameByUserId +")";
        }
        ResponsibleDto targetUser = new ResponsibleDto();
        targetUser.setId(currentUserId);
        targetUser.setName(currentUserName);
        Result<Void> dispatchResult = orderProcessService.receiveOrder(
                tenantId,
                inspectOrderId,
                entity.getProcessInstanceId(),
                targetUser,
                OrderTypeEnum.INSPECT.getValue(),
                isSuperRole
        );
        if(!dispatchResult.getSignal()){
            throw new BizException(dispatchResult.getMessage());
        }

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsource()) ? entity.getCustomerName() : null);

        return Result.ok();
    }


    @Transactional
    public Result<Void> revokeOrder(Long tenantId, Integer idType, InspectOrderRevokeDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是待派单状态
        if(!OrderStatusEnum.WAIT_DISPATCH.getValue().equals(entity.getStatus())&& !OrderStatusEnum.WAIT_RECEIVE.getValue().equals(entity.getStatus())) {
            return Result.error("撤销失败，工单已分派或已被接收");
        }
        
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 检查当前用户是否有撤销权限
        // 创建人是否是当前用户
        // if(!entity.getCreatorId().equals(currentUserId)){
        //     return Result.error("撤销失败，工单创建人才能撤销");
        // }

        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.REVOKE.getValue());

        // 撤销巡检工单流程
        Result<Void> undoResult = orderProcessService.undo(
            tenantId, 
            inspectOrderId, 
            entity.getProcessInstanceId(),
            OrderTypeEnum.INSPECT.getValue()
        );
        if(!undoResult.getSignal()){
            throw new BizException(undoResult.getMessage());
        }

        // 处理工单告警记录
        alarmRecordService.finishAlarm(inspectOrderId, OrderTypeEnum.INSPECT.getValue());

        return Result.ok();
    }

    @Transactional
    public Result<Void> undoDispatchOrder(Long tenantId, Integer idType, InspectOrderUndoDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是执行中状态
        if(!OrderStatusEnum.EXECUTING.getValue().equals(entity.getStatus())){
            return Result.error("撤回失败，工单不是执行中状态");
        }
        
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 检查当前用户是否有撤回权限
        // 创建人是否是当前用户
        // if(!entity.getCreatorId().equals(currentUserId)){
        //     return Result.error("撤销失败，工单创建人才能撤销");
        // }
        Boolean isAutoDispatch = false;
        Object variable = runtimeService.getVariable(entity.getProcessInstanceId(), AUTO_DISPATCH_FLAG);
        if (variable != null) {
            List<SelectValue> selectValues = JsonUtil.parseArray(JsonUtil.toJSONString(variable), SelectValue.class);
            if (CollUtil.isNotEmpty(selectValues)){
                isAutoDispatch = ObjectUtil.equals(selectValues.get(0).getValue(),"1");
            }
        }

        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId,isAutoDispatch?OrderStatusEnum.WAIT_RECEIVE.getValue(): OrderStatusEnum.WAIT_DISPATCH.getValue());

        // 撤回派工巡检工单流程
        Result<Void> undoResult = orderProcessService.withdrawTask(
                tenantId,
                inspectOrderId,
                entity.getProcessInstanceId(),
                dto.getReason(),
                OrderTypeEnum.INSPECT.getValue(),
                isAutoDispatch? OrderProcessOperationEnum.WITHDRAW_RECEIVE.getValue():OrderProcessOperationEnum.WITHDRAW_DISPATCH.getValue()
        );
        if(!undoResult.getSignal()){
            throw new BizException(undoResult.getMessage());
        }

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, isAutoDispatch? OrderStatusEnum.WAIT_RECEIVE: OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsource()) ? entity.getCustomerName() : null);

        return Result.ok();
    }

    @Transactional
    public Result<Void> undoExecuteOrder(Long tenantId, Integer idType, InspectOrderUndoDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是执行中状态
        if(!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(entity.getStatus())){
            return Result.error("撤回失败，工单不是执行中状态");
        }
        
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 检查当前用户是否有撤回权限
        // 创建人是否是当前用户
        // if(!entity.getCreatorId().equals(currentUserId)){
        //     return Result.error("撤销失败，工单创建人才能撤销");
        // }

        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.EXECUTING.getValue());

        // 撤回执行巡检工单流程
        Result<Void> undoResult = orderProcessService.withdrawTask(
                tenantId,
                inspectOrderId,
                entity.getProcessInstanceId(),
                dto.getReason(),
                OrderTypeEnum.INSPECT.getValue(),
                OrderProcessOperationEnum.WITHDRAW_EXECUTE.getValue()
        );
        if(!undoResult.getSignal()){
            throw new BizException(undoResult.getMessage());
        }

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsource()) ? entity.getCustomerName() : null);

        return Result.ok();
    }

    @Transactional
    public Result<Void> dispatchOrder(Long tenantId, Integer idType, InspectOrderDispatchDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是待派单状态
        if(!OrderStatusEnum.WAIT_DISPATCH.getValue().equals(entity.getStatus())){
            if(OrderStatusEnum.REVOKE.getValue().equals(entity.getStatus())){
                return Result.error("派单失败，工单已撤销");
            }
            return Result.error("派单失败，工单已分派");
        }
        
        // 检查当前用户是否有派单权限
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 检查接收人是否有工单执行权限
        Long receiveUserId = dto.getReceiveUserId();
        

        // 修改工单状态
        Integer updateStatusCount = inspectOrderMapper.updateStatusById(inspectOrderId, OrderStatusEnum.EXECUTING.getValue());

        // 修改接收人
        Integer updateExecuteUserIdCount = inspectOrderMapper.updateExecuteUserIdById(inspectOrderId, receiveUserId);
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantId, idType);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantId, currentUserId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        // 派工巡检工单流程
        ResponsibleDto targetUser = new ResponsibleDto(); 
        targetUser.setId(receiveUserId);
        targetUser.setName(dto.getWorkGroupName() + "_" + dto.getReceiveUserName());
        Result<Void> dispatchResult = orderProcessService.dispatch(
            tenantId, 
            inspectOrderId, 
            entity.getProcessInstanceId(), 
            targetUser,
            OrderTypeEnum.INSPECT.getValue(),
                isSuperRole
        );
        if(!dispatchResult.getSignal()){
            throw new BizException(dispatchResult.getMessage());
        }

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsource()) ? entity.getCustomerName() : null);

        return Result.ok();
    }
    
    @Transactional
    public Result<Void> forwardOrder(Long tenantId, Integer idType, InspectOrderForwardDto dto) {
        // 检查工单是否存在
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }
        
        // 检查工单是否是执行中状态
        if(!OrderStatusEnum.EXECUTING.getValue().equals(entity.getStatus())){
            return Result.error("转交失败，工单不是执行中状态");
        }
        
        // 检查当前用户是否有派单权限
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        // 检查接收人是否有工单执行权限
        Long receiveUserId = dto.getReceiveUserId();

        // 修改接收人
        Integer updateExecuteUserIdCount = inspectOrderMapper.updateExecuteUserIdById(inspectOrderId, receiveUserId);

        // 转交巡检工单流程
        ResponsibleDto targetUser = new ResponsibleDto(); 
        targetUser.setId(receiveUserId);
        targetUser.setName(dto.getWorkGroupName() + "_" + dto.getReceiveUserName());
        Result<Void> dispatchResult = orderProcessService.forward(
            tenantId, 
            inspectOrderId, 
            entity.getProcessInstanceId(), 
            targetUser,
            dto.getRemark(),
            OrderTypeEnum.INSPECT.getValue()
        );
        if(!dispatchResult.getSignal()){
            throw new BizException(dispatchResult.getMessage());
        }

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsource()) ? entity.getCustomerName() : null);

        return Result.ok();
    }

    @Transactional
    public Result<InspectOrderCreateDto> quickCreate(Long tenantId, String tenantName, Integer idType, InspectOrderQuickCreateDto createDto) {
        // 检查复制工单是否存在
        Long copyOrderId = createDto.getCopyOrderId();
        InspectOrderEntity copyOrderEntity = inspectOrderMapper.selectById(copyOrderId);
        if(copyOrderEntity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(copyOrderEntity.getTenantId()) 
            && !tenantId.equals(copyOrderEntity.getSupplierId()) 
            && !tenantId.equals(copyOrderEntity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }

        // 工单未完结时不可重复外委
        // 查询复制该工单且未完结的工单
        List<InspectOrderEntity> processingCopyOrderList = inspectOrderMapper.listProcessingCopyOrder(copyOrderId);
        if(processingCopyOrderList != null && processingCopyOrderList.size() > 0){
            return Result.error("工单已经外委，且正在处理中，不可重复外委");
        }

        InspectOrderCreateDto dto = new InspectOrderCreateDto();
        dto.setPlanInspectTime(copyOrderEntity.getPlanInspectTime());
        dto.setLevel(copyOrderEntity.getLevel());
        dto.setOutsource(OutsourceEnum.YES.getValue());
        dto.setAggregatorOutsource(YesNoEnum.YES.getValue());
        dto.setSupplierId(createDto.getSupplierId());
        dto.setSupplierName(createDto.getSupplierName());
        dto.setInspectType(createDto.getInspectType());
        dto.setCopyOrderId(createDto.getCopyOrderId());
        dto.setCreateSource(IdTypeEnum.SUPPLIER.getValue());

        List<OrderDeviceDto> deviceList = new ArrayList<>();

        // 根据复制工单id获取工单结果
        List<InspectResultEntity> resultList = inspectResultMapper.listByOrderId(copyOrderId);
        if(resultList != null){
            resultList.forEach(t -> {
                OrderDeviceDto od = new OrderDeviceDto();
                od.setDeviceId(t.getDeviceId());
                List<IdNameDto> standardList = inspectResultStandardMapper.listStandardIdNameByResultId(t.getId());
                od.setStandardList(standardList);
                deviceList.add(od);
            });
        }
        dto.setDeviceList(deviceList);
        
        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        return createInspectOrder(tenantId, tenantName, idType, dto, currentUserId, currentUserName);
    }

    @Transactional
    public Result<InspectOrderCreateDto> createInspectOrder(
        Long tenantId, String tenantName, Integer idType, InspectOrderCreateDto dto,
        Long userId, String userName
    ) {
        if(idType == null){
            return Result.error("缺少身份信息：idType");
        }
        if(tenantId == null){
            return Result.error("缺少租户信息：tenantId");
        }

        Integer outsource = dto.getOutsource();

        Boolean isDataRecord = false;
        if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
            // 供应商为客户创建（委外）
            if(!OutsourceEnum.YES.getValue().equals(outsource)){
                return Result.error("供应商创建计划，必须是委外");
            }
            if (!ObjectUtil.equals(dto.getAggregatorOutsource(), YesNoEnum.YES.getValue())) {
                dto.setSupplierId(tenantId);
                dto.setSupplierName(tenantName);
                Long customerId = dto.getCustomerId();
                if (customerId == null) {
                    return Result.error("缺少客户id信息：customerId");
                }
                String customerName = dto.getCustomerName();
                if (customerName == null) {
                    return Result.error("缺少客户名称信息：customerName");
                }
                TenantInfoEntity tenantInfo = tenantInfoService.getByPmoTenantId(customerId);
                Long pmoSourceTenantId = Optional.ofNullable(tenantInfo).map(BaseEntity::getTenantId).orElse(null);
                dto.setTenantId(pmoSourceTenantId != null ? pmoSourceTenantId : customerId);
                dto.setCreateSource(CreateSourceEnum.SUPPLIER.getValue());

                Result<Integer> tenantTypeResult = customerRelationService.getTenantType(dto.getCustomerId(), dto.getSupplierId(), idType);
                if (!tenantTypeResult.getSignal()) {
                    return Result.error("获取供应商类型失败" + tenantTypeResult.getMessage());
                }
                Integer tenantType = tenantTypeResult.getResult();
                isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType);
                dto.setCustomerId(pmoSourceTenantId != null ? pmoSourceTenantId : customerId);
            } else {
                // 集成商为供应商创建（委外）
                dto.setCustomerId(tenantId);
                dto.setCustomerName(tenantName);
                Long supplierId = dto.getSupplierId();
                if(supplierId == null){
                    return Result.error("缺少供应商id信息：supplierId");
                }
                String supplierName = dto.getSupplierName();
                if(supplierName == null){
                    return Result.error("缺少供应商名称信息：supplierName");
                }
                dto.setTenantId(tenantId);
                dto.setCreateSource(CreateSourceEnum.SUPPLIER.getValue());
                Result<Integer> tenantTypeResult = customerRelationService.getTenantType(tenantId, supplierId, idType);
                if(!tenantTypeResult.getSignal()){
                    return Result.error("获取供应商类型失败" + tenantTypeResult.getMessage());
                }
                Integer tenantType = tenantTypeResult.getResult();
                isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType);
            }
        }else if(IdTypeEnum.CUSTOMER.getValue().equals(idType)){
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                // 客户为供应商创建（委外）
                dto.setCustomerId(tenantId);
                dto.setCustomerName(tenantName);
                Long supplierId = dto.getSupplierId();
                if(supplierId == null){
                    return Result.error("缺少供应商id信息：supplierId");
                }
                String supplierName = dto.getSupplierName();
                if(supplierName == null){
                    return Result.error("缺少供应商名称信息：supplierName");
                }
                dto.setTenantId(tenantId);
                dto.setCreateSource(CreateSourceEnum.CUSTOMER.getValue());
                Result<Integer> tenantTypeResult = customerRelationService.getTenantType(tenantId, supplierId, idType);
                if(!tenantTypeResult.getSignal()){
                    return Result.error("获取供应商类型失败" + tenantTypeResult.getMessage());
                }
                Integer tenantType = tenantTypeResult.getResult();
                isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType);
            }else{
                // 客户为自己创建（不委外）
                dto.setSupplierId(null);
                dto.setSupplierName(null);
                dto.setCustomerId(tenantId);
                dto.setCustomerName(tenantName);
                dto.setTenantId(tenantId);
                dto.setCreateSource(CreateSourceEnum.CUSTOMER.getValue());
            }
        }else{
            return Result.error("身份信息异常：idType");
        }

        LocalDateTime now = LocalDateTime.now();
        // // 委外检查是否对设备进行了授权
        // if(OutsourceEnum.YES.getValue().equals(outsource)){
        //     List<OrderDeviceDto> deviceList = dto.getDeviceList();
        //     if(deviceList != null){
        //         for(OrderDeviceDto d:deviceList){
        //             boolean haveDeviceAuth = deviceEmpowerService.haveDeviceAuth(tenantId, dto.getSupplierId(), d.getDeviceId());
        //             if (!haveDeviceAuth) {
        //                 return Result.error("设备未授权给此供应商:" + dto.getSupplierName() + "，请先进行授权");
        //             }
        //         }
        //     }
        // }
        // 插入工单
        InspectOrderEntity orderEntity = new InspectOrderEntity();
        orderEntity.setOrderNumber("tmp");
        orderEntity.setTenantId(dto.getTenantId());
        orderEntity.setInspectPlanId(dto.getId());
        orderEntity.setStatus(OrderStatusEnum.WAIT_DISPATCH.getValue());
        orderEntity.setPlanInspectTime(dto.getPlanInspectTime());
        orderEntity.setOutsource(outsource);
        orderEntity.setCopyOrderId(dto.getCopyOrderId());
        orderEntity.setLevel(dto.getLevel());
        orderEntity.setTimeout(TimeoutEnum.NORMAL.getValue());
        
        orderEntity.setCustomerId(dto.getCustomerId());
        orderEntity.setCustomerName(dto.getCustomerName());
        orderEntity.setInspectType(dto.getInspectType());
        orderEntity.setCreateSource(dto.getCreateSource());

        if(OutsourceEnum.YES.getValue().equals(outsource)){
            orderEntity.setSupplierId(dto.getSupplierId());
            orderEntity.setSupplierName(dto.getSupplierName());
            Integer outsourceSupplierType = dto.getOutsourceSupplierType();
            if(outsourceSupplierType == null){
                outsourceSupplierType = SupplierTypeEnum.TENANT.getValue();
            }
            orderEntity.setOutsourceSupplierType(outsourceSupplierType);
            Integer outsourceCustomerType = dto.getOutsourceCustomerType();
            if(outsourceCustomerType == null){
                outsourceCustomerType = SupplierTypeEnum.TENANT.getValue();
            }
            orderEntity.setOutsourceCustomerType(outsourceCustomerType);
        }
        inspectOrderMapper.insert(orderEntity);
        Long inspectOrderId = orderEntity.getId();
        String orderNumber = serialNumberService.getNext(tenantId, now.toLocalDate(), InspectOrder.serialNumber);
        
        ResponsibleDto startUser = new ResponsibleDto(userId, userName, null);

        // 启动巡检工单流程
        Result<StartFlowDto> startFlowResult = null;
        try {
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                if(isDataRecord){
                    startFlowResult = orderProcessService.startFlow(
                        tenantId, 
                        idType, 
                        OrderTypeEnum.INSPECT.getValue(), 
                        OutsourceEnum.YES.getValue().equals(outsource),
                        isDataRecord,
                        inspectOrderId,
                        startUser
                    );
                }else{
                    startFlowResult = orderProcessService.startFlow(
                        dto.getTenantId(),
                        OrderTypeEnum.INSPECT.getValue(), 
                        inspectOrderId, 
                        startUser, 
                        dto.getSupplierId(),
                        isDataRecord,
                        dto.getAggregatorOutsource()
                    );
                }
            }else{
                startFlowResult = orderProcessService.startFlow(
                    tenantId, 
                    idType, 
                    OrderTypeEnum.INSPECT.getValue(), 
                    OutsourceEnum.YES.getValue().equals(outsource),
                    isDataRecord,
                    inspectOrderId,
                    startUser
                );
            }
        } catch (Exception e) {
            throw new RuntimeException("调用启动流程异常", e);
        }
        if(!startFlowResult.getSignal()){
            throw new BizException(startFlowResult.getMessage());
        }
        String processInstanceId = startFlowResult.getResult().getProcessInstanceId();
        if (startFlowResult.getResult().getIsAutoDispatch()){
            orderEntity.setStatus(OrderStatusEnum.WAIT_RECEIVE.getValue());
        }
        orderEntity.setOrderNumber(orderNumber);
        inspectOrderMapper.updateById(orderEntity);
        Integer count = inspectOrderMapper.updateProcessInstanceIdById(inspectOrderId, processInstanceId);

        // 复制标准，创建工单结果
        // 先查出计划涉及的设备和标准
        List<OrderDeviceDto> orderDeviceList = dto.getDeviceList();
        if(orderDeviceList != null){
            // 每个设备建一个工单结果
            orderDeviceList.forEach(orderDevice -> {
                InspectResultEntity inspectResultEntity = new InspectResultEntity();
                inspectResultEntity.setTenantId(dto.getTenantId());
                inspectResultEntity.setInspectOrderId(inspectOrderId);
                inspectResultEntity.setDeviceId(orderDevice.getDeviceId());
                inspectResultEntity.setStatus(InspectResultStatusEnum.NOT_INSPECT.getValue());
                inspectResultMapper.insert(inspectResultEntity);
                Long resultId = inspectResultEntity.getId();
                // 每个设备对应多个标准
                List<IdNameDto> standardList = orderDevice.getStandardList();
                if(standardList == null){
                    return;
                }
                standardList.forEach(inspectStandard -> {
                    InspectResultStandardEntity resultStandardEntity = new InspectResultStandardEntity();
                    resultStandardEntity.setTenantId(dto.getTenantId());
                    resultStandardEntity.setInspectResultId(resultId);
                    resultStandardEntity.setInspectStandardId(inspectStandard.getId());
                    inspectResultStandardMapper.insert(resultStandardEntity);
                    
                    // 根据标准创建工单结果项
                    List<InspectStandardItemEntity> standardItemList = inspectStandardItemMapper.listByStandardId(inspectStandard.getId());
                    if(standardItemList == null){
                        return;
                    }
                    standardItemList.forEach(standardItem -> {
                        InspectResultItemEntity resultItemEntity = new InspectResultItemEntity();
                        resultItemEntity.setTenantId(dto.getTenantId());
                        resultItemEntity.setInspectResultId(resultId);
                        resultItemEntity.setInspectStandardItemId(standardItem.getId());
                        inspectResultItemMapper.insert(resultItemEntity);
                    });
                });
            });
        }

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(processInstanceId, orderEntity.getOrderNumber(),
                orderEntity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT, startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE:OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(orderEntity.getTimeout()), OutsourceEnum.YES.getValue().equals(orderEntity.getOutsource()) ? orderEntity.getCustomerName() : null);

        // 如果是告警记录创建的工单，需要更新告警记录的工单id
        if (Objects.nonNull(dto.getAlarmRecordId())) {
            alarmRecordMapper.bindOrder(dto.getAlarmRecordId(), orderEntity.getId(), orderEntity.getOrderNumber(),
                    OrderTypeEnum.INSPECT.getValue(), startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE.getValue():OrderStatusEnum.WAIT_DISPATCH.getValue());
        }

        return Result.ok(dto);
    }

    private List<String> listOrderOperations(
        InspectOrderEntity orderEntity, 
        Long userId,
        Long tenantId,
        Set<String> processInstanceIds,
        Map<String, String> lastOperatorMap,
        Map<String,Boolean> forwardPermissionMap,
        Map<String, Boolean> acceptMap,
        Integer idType, 
        Boolean isSuperRole,
        Boolean todo
    ){
        String s = tenantId + ":" + userId;
        Boolean canUndo = s.equals(lastOperatorMap.get(orderEntity.getProcessInstanceId()));
        Boolean canForward = forwardPermissionMap.getOrDefault(orderEntity.getProcessInstanceId(), false);
        
        List<String> operations = new ArrayList<>();
        // 状态对应按钮
        Integer status = orderEntity.getStatus();

        Boolean isOutsource = OutsourceEnum.YES.getValue().equals(orderEntity.getOutsource());
        Boolean isCustomer = IdTypeEnum.CUSTOMER.getValue().equals(idType);

        OrderStatusEnum statusEnum = OrderStatusEnum.typeOfValue(status);

        switch (statusEnum) {
            case WAIT_DISPATCH:{
                // 管理员权限
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(isDataRecord) {
                                operations.add(OperationEnum.DISPATCH.getName());
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            }
                        } else {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceCustomerType());
                            if(isDataRecord) {
                                operations.add(OperationEnum.DISPATCH.getName());
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                operations.add(OperationEnum.DISPATCH.getName());
                                break;
                            }
                        }
                    } else {
                        // 内部工单
                        operations.add(OperationEnum.DISPATCH.getName());
                        operations.add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                // 待分派状态，显示分派按钮
                if (todo) {
                    operations.add(OperationEnum.DISPATCH.getName());
                }
                if (userId.equals(orderEntity.getCreatorId())) {
                    operations.add(OperationEnum.REVOKE.getName());
                }
                break;
            }
            case WAIT_RECEIVE:{
                // 管理员权限
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(isDataRecord) {
                                operations.add(OperationEnum.RECEIVE.getName());
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            }
                        } else {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceCustomerType());
                            if(isDataRecord) {
                                operations.add(OperationEnum.RECEIVE.getName());
                                operations.add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                operations.add(OperationEnum.RECEIVE.getName());
                                break;
                            }
                        }
                    } else {
                        // 内部工单
                        operations.add(OperationEnum.RECEIVE.getName());
                        operations.add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                // 待分派状态，显示分派按钮
                if (todo) {
                    operations.add(OperationEnum.RECEIVE.getName());
                }
                if (userId.equals(orderEntity.getCreatorId())) {
                    operations.add(OperationEnum.REVOKE.getName());
                }
                break;
            }
            case EXECUTING:{
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(!isDataRecord) {
                                // 当前是客户&&委外&&非数据型
                                break;
                            }
                        }
                    }
                    operations.add(OperationEnum.EXECUTE.getName());
                    operations.add(OperationEnum.FORWARD.getName());
                    operations.add(OperationEnum.UNDO_DISPATCH.getName());
                    break;
                }
                // 执行中状态，显示执行按钮
                if (todo) {
                    operations.add(OperationEnum.EXECUTE.getName());
                    if (canForward) {
                        operations.add(OperationEnum.FORWARD.getName());
                    }
                }
                if (canUndo) {
                    operations.add(OperationEnum.UNDO_DISPATCH.getName());
                }
                break;
            }
            case WAIT_ACCEPT:{
                if (isSuperRole){
                    if (isOutsource) {
                        if (isCustomer) {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceSupplierType());
                            if(!isDataRecord) {
                                // 当前是客户&&委外&&非数据型
                                operations.add(OperationEnum.ACCEPT.getName());
                                operations.add(OperationEnum.REJECT.getName());

                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i->i.get(orderEntity.getProcessInstanceId())).orElse(false) ){
                                    operations.clear();
                                }
                                break;
                            }
                        } else {
                            Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(orderEntity.getOutsourceCustomerType());
                            if(!isDataRecord) {
                                // 当前是供应商&&委外&&非数据型
                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i->i.get(orderEntity.getProcessInstanceId())).orElse(false) ){
                                    break;
                                }
                            }
                        }
                    }
                    operations.add(OperationEnum.ACCEPT.getName());
                    operations.add(OperationEnum.REJECT.getName());
                    operations.add(OperationEnum.UNDO_EXECUTE.getName());
                    break;
                }
                // 待验收状态，显示验收按钮
                if (todo) {
                    operations.add(OperationEnum.ACCEPT.getName());
                    operations.add(OperationEnum.REJECT.getName());
                }
                if (canUndo) {
                    operations.add(OperationEnum.UNDO_EXECUTE.getName());
                }
                break;
            }
            default:
                break;
        }

        return operations;
    }

    public Result<List<InspectOrderDto>> listByParams(Long tenantId,  Integer idType, InspectOrderParams params) {
        if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
            params.setOutsource(OutsourceEnum.YES.getValue());
        }
        
        LocalDateTime lastModifyStartDateTime = null;
        if(params.getLastModifyStartDate() != null){
            lastModifyStartDateTime = LocalDateTime.of(params.getLastModifyStartDate(), LocalTime.of(0, 0, 0));
        }
        LocalDateTime lastModifyEndDateTime = null;
        if(params.getLastModifyEndDate() != null){
            lastModifyEndDateTime = LocalDateTime.of(params.getLastModifyEndDate(), LocalTime.of(23, 59, 59));
        }

        List<InspectOrderDto> list = inspectOrderMapper.listByParams(tenantId, idType, params, lastModifyStartDateTime, lastModifyEndDateTime);

        return Result.ok(list);
    }

    public Result<InspectOrderPage> pageByParams(Long tenantId,  Integer idType, InspectOrderParams params, Page<InspectOrderEntity> page) {
        
        LocalDateTime lastModifyStartDateTime = null;
        if(params.getLastModifyStartDate() != null){
            lastModifyStartDateTime = LocalDateTime.of(params.getLastModifyStartDate(), LocalTime.of(0, 0, 0));
        }
        LocalDateTime lastModifyEndDateTime = null;
        if(params.getLastModifyEndDate() != null){
            lastModifyEndDateTime = LocalDateTime.of(params.getLastModifyEndDate(), LocalTime.of(23, 59, 59));
        }

        Page<InspectOrderDto> list = inspectOrderMapper.pageByParams(tenantId, idType, params, page, lastModifyStartDateTime, lastModifyEndDateTime);
        if(list == null){
            return null;
        }

        // 操作按钮
        Long currentUserId = JwtUserInfoUtils.getUserId();
        List<InspectOrderDto> records = list.getRecords();
        if(records != null){
            if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                records.forEach(i->{i.setSupportSupplierId(null);});
            }
            Result<List<TaskDto>> queryTodoTaskListResult = orderProcessService.queryTodoTaskList(currentUserId);
            if(!queryTodoTaskListResult.getSignal()){
                throw new BizException(queryTodoTaskListResult.getMessage());
            }
            List<TaskDto> result = queryTodoTaskListResult.getResult();
            List<String> processInstanceIdList = result.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
            Set<String> processInstanceIds = result.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toSet());
            List<String> collect = records.stream().map(InspectOrderDto::getProcessInstanceId)
            .filter(t -> {
                return t != null && !"".equals(t);
            })
            .collect(Collectors.toList());
            Result<Map<String, String>> queryLastOperatorByProcessInstancesResult = orderProcessService.queryLastOperatorByProcessInstances(collect);
            if(!queryLastOperatorByProcessInstancesResult.getSignal()){
                throw new BizException(queryLastOperatorByProcessInstancesResult.getMessage());
            }
            Map<String, String> lastOperatorMap = queryLastOperatorByProcessInstancesResult.getResult();

            Result<Map<String, Boolean>> forwardPermissionMapResult = orderProcessService.queryForwardPermissionByProcessInstances(collect);
            if(!forwardPermissionMapResult.getSignal()){
                throw new BizException(forwardPermissionMapResult.getMessage());
            }
            Map<String,Boolean> forwardPermissionMap = forwardPermissionMapResult.getResult();

            // 是否是超级角色
            Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantId, idType);
            Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantId, currentUserId, idType);
            if (!haveAuthResult.getSignal()) {
                throw new BizException(haveAuthResult.getMessage());
            }
            Boolean isReportHandleRole = haveAuthResult.getResult();
            Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;

            Result<Map<String, Boolean>> acceptMapResult = orderProcessService.queryAcceptStateByProcessInstances(collect, idType);
            if (!acceptMapResult.getSignal()) {
                throw new BizException(acceptMapResult.getMessage());
            }
            Map<String, Boolean> acceptMap = acceptMapResult.getResult();

            records.forEach(record -> {
                record.setOperations(listOrderOperations(
                    record, currentUserId, tenantId,
                    processInstanceIds, lastOperatorMap, forwardPermissionMap,
                    acceptMap,
                    idType, isSuperRole,
                    processInstanceIds.contains(record.getProcessInstanceId())
                ));
            });
        }

        InspectOrderPage p = new InspectOrderPage();
        p.setRecords(list.getRecords());
        p.setSize(list.getSize());
        p.setTotal(list.getTotal());
        p.setCurrent(list.getCurrent());
        
        params.setStatusList(null);

        Result<List<InspectOrderDto>> allResult = listByParams(tenantId, idType, params);
        if(!allResult.getSignal()){
            return Result.error(allResult.getMessage());
        }
        List<InspectOrderDto> all = allResult.getResult();

        Multiset<Integer> counter = HashMultiset.create();
        if(all != null){
            all.forEach(t -> {
                counter.add(t.getStatus());
            });
        }
        OrderStatusCountVo countVo = new OrderStatusCountVo();
        countVo.setWaitDispatchCount(counter.count(OrderStatusEnum.WAIT_DISPATCH.getValue()));
        countVo.setWaitReceiveCount(counter.count(OrderStatusEnum.WAIT_RECEIVE.getValue()));
        countVo.setExecutingCount(counter.count(OrderStatusEnum.EXECUTING.getValue()));
        countVo.setWaitAcceptCount(counter.count(OrderStatusEnum.WAIT_ACCEPT.getValue()));
        countVo.setAcceptedCount(counter.count(OrderStatusEnum.ACCEPTED.getValue()));
        countVo.setRejectCount(counter.count(OrderStatusEnum.REJECT.getValue()));
        countVo.setRevokeCount(counter.count(OrderStatusEnum.REVOKE.getValue()));
        p.setOrderStatusCount(countVo);

        return Result.ok(p);


    }

    public Result<InspectOrderSummaryVo> summary(Long tenantId, Integer idType) {
        
        // 当前用户id
        Long currentUserId = JwtUserInfoUtils.getUserId();

        List<InspectOrderEntity> entityList = inspectOrderMapper.listAll(tenantId, idType);
        if(entityList == null){
            entityList = new ArrayList<>();
        }

        InspectOrderSummaryVo vo = new InspectOrderSummaryVo();


        // status -> list
        Map<Integer, List<InspectOrderEntity>> map = entityList.stream().collect(Collectors.groupingBy(InspectOrderEntity::getStatus));

        // 待分派，待接收，执行中，待验收
        List<InspectOrderEntity> waitDispatchList = map.get(OrderStatusEnum.WAIT_DISPATCH.getValue());
        vo.setWaitDispatchCount(waitDispatchList == null?0:waitDispatchList.size());

        List<InspectOrderEntity> waitReceiveList = map.get(OrderStatusEnum.WAIT_RECEIVE.getValue());
        vo.setWaitReceiveCount(waitReceiveList == null?0:waitReceiveList.size());

        List<InspectOrderEntity> waitInspectingList = map.get(OrderStatusEnum.EXECUTING.getValue());
        vo.setInspectingCount(waitInspectingList == null?0:waitInspectingList.size());

        List<InspectOrderEntity> waitWaitAcceptList = map.get(OrderStatusEnum.WAIT_ACCEPT.getValue());
        vo.setWaitAcceptCount(waitWaitAcceptList == null?0:waitWaitAcceptList.size());

        // 待处理点巡检工单
        vo.setProcessingCount(
            vo.getWaitDispatchCount()
            + vo.getWaitReceiveCount()
            + vo.getInspectingCount()
            + vo.getWaitAcceptCount()
        );

        Set<Integer> statusSet = new HashSet<>();
        statusSet.add(OrderStatusEnum.WAIT_DISPATCH.getValue());
        statusSet.add(OrderStatusEnum.WAIT_RECEIVE.getValue());
        statusSet.add(OrderStatusEnum.EXECUTING.getValue());
        statusSet.add(OrderStatusEnum.WAIT_ACCEPT.getValue());
        Map<Integer, List<InspectOrderEntity>> group = entityList.stream()
        .filter(t -> {
            return statusSet.contains(t.getStatus());
        })
        .collect(Collectors.groupingBy(InspectOrderEntity::getOutsource));
        List<InspectOrderEntity> outsourceList = group.get(OutsourceEnum.YES.getValue());
        vo.setProcessingOutsourceCount(outsourceList == null?0:outsourceList.size());
        List<InspectOrderEntity> insourceList = group.get(OutsourceEnum.NO.getValue());
        vo.setProcessingInsourceCount(insourceList == null?0:insourceList.size());

        return Result.ok(vo);
    }

    public Result<Map<String, Long>> countOrderByDate(
        Long tenantId, Integer idType, LocalDate begin, LocalDate end
    ) {
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.of(0, 0, 0));
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.of(23, 59, 59));
        if(beginTime.isAfter(endTime)){
            return Result.error("开始时间不能大于结束时间");
        }
        List<InspectOrderEntity> list = inspectOrderMapper.listOrderByDate(tenantId, idType, beginTime, endTime);        
        if(list == null){
            list = new ArrayList<>();
        }
        if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
            // 只统计已经和当前供应商建立关系的客户的工单
            TenantIsolation tenantIsolation = new TenantIsolation();
            tenantIsolation.setTenantId(tenantId);
            tenantIsolation.setIdType(IdTypeEnum.SUPPLIER.getValue());
            Result<List<CustomerRelationEntity>> listTenantCustomerResult = customerRelationService.listTenantCustomer(tenantIsolation);
            if (!listTenantCustomerResult.getSignal()){
                return Result.error(listTenantCustomerResult.getMessage());
            }
            List<CustomerRelationEntity> customerList = listTenantCustomerResult.getResult();
            Set<Long> customerIds = new HashSet<>();
            if(customerList != null){
                customerList.forEach(t -> {
                    customerIds.add(t.getCustomerId());
                });
            }
            list = list.stream().filter(t -> {
                return customerIds.contains(t.getTenantId());
            }).collect(Collectors.toList());
        }

        Map<String, Long> countMap = list.stream().collect(Collectors.groupingBy(t -> {
            LocalDateTime planInspectTime = t.getPlanInspectTime();
            return yyyyMMdd.format(planInspectTime);
        }, Collectors.counting()));

        // if(countMap == null){
        //     countMap = new HashMap<>();
        // }
        // LocalDateTime tmp = beginTime;
        // while(tmp.isBefore(endTime)){
        //     String s = yyyyMMdd.format(tmp);
        //     if(!countMap.containsKey(s)){
        //         countMap.put(s, 0L);
        //     }
        //     tmp = tmp.plusDays(1);
        // }
        
        return Result.ok(countMap);
    }

    public Result<InspectOrderCountVo> countOrderByStatus(
        Long tenantId, Integer idType, LocalDateTime begin, LocalDateTime end
    ) {
        InspectOrderParams params = new InspectOrderParams();
        params.setPlanInspectTimeBegin(begin);
        params.setPlanInspectTimeEnd(end);
        Result<List<InspectOrderDto>> result = listByParams(tenantId, idType, params);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        List<InspectOrderDto> list = result.getResult();
        if(list == null){
            list = new ArrayList<>();
        }
        
        Map<Integer, Long> countMap = list.stream().collect(Collectors.groupingBy(t -> {
            return t.getStatus();
        }, Collectors.counting()));

        InspectOrderCountVo vo = new InspectOrderCountVo();
        if(countMap != null){
            Long a = countMap.get(OrderStatusEnum.WAIT_DISPATCH.getValue());
            vo.setWaitDispatchCount(a == null?0:a.intValue());
            Long b = countMap.get(OrderStatusEnum.WAIT_RECEIVE.getValue());
            vo.setWaitReceiveCount(b == null?0:b.intValue());
            Long c = countMap.get(OrderStatusEnum.EXECUTING.getValue());
            vo.setExecutingCount(c == null?0:c.intValue());
            Long d = countMap.get(OrderStatusEnum.WAIT_ACCEPT.getValue());
            vo.setWaitAcceptCount(d == null?0:d.intValue());
        }else{
            vo.setWaitDispatchCount(0);
            vo.setWaitReceiveCount(0);
            vo.setExecutingCount(0);
            vo.setWaitAcceptCount(0);
        }
        vo.setTotalWaitCount(
            vo.getWaitDispatchCount() + 
            vo.getWaitReceiveCount() + 
            vo.getExecutingCount() + 
            vo.getWaitAcceptCount()
        );
        
        return Result.ok(vo);
    }

    public Result<List<InspectOrderEntity>> list(Long tenantId, InspectOrderEntity entity) {
        List<InspectOrderEntity> list = inspectOrderMapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    public Result<Void> update(Long tenantId, InspectOrderEntity entity) {
        entity.setTenantId(tenantId);
        if (inspectOrderMapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    public Result<Void> deleteById(Long tenantId, Long entityId) {
        if (inspectOrderMapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    public Result<InspectOrderVo> getOrderDetailById(Long tenantId, Integer idType, Long inspectOrderId) {
        if(inspectOrderId == null){
            return Result.error("工单id不能为空");
        }
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单");
        }

        InspectOrderVo orderVo = new InspectOrderVo();
        BeanUtils.copyProperties(entity, orderVo);

        // 获取工单图片列表
        List<FileVo> orderFileList = fileMapper.listByBizIds(FileBizTypeEnum.INSPECT_ORDER_PICTURE.getValue(), new ArrayList<Long>(){{add(inspectOrderId);}});
        orderVo.setOrderFileList(orderFileList);
        
        // 获取工单设备列表
        List<InspectResultVo> resultList = inspectResultMapper.listbyOrderIdWithDeviceInfo(inspectOrderId);
        orderVo.setDeviceList(resultList);

        // 获取工单结果设备附件
        if(resultList != null && resultList.size() > 0){
            List<Long> bizIds = resultList.stream().map(InspectResultVo::getId).collect(Collectors.toList());
            if(bizIds != null && bizIds.size() > 0){
                List<FileVo> fileList = fileMapper.listByBizIds(FileBizTypeEnum.INSPECT_RESULT_PICTURE.getValue(), bizIds);
                if(fileList != null && fileList.size() > 0){
                    // bizId -> fileList
                    Map<Long, List<FileVo>> map = fileList.stream().collect(Collectors.groupingBy(FileVo::getBizId));
                    resultList.forEach(t -> {
                        Long bizId = t.getId();
                        List<FileVo> list = map.get(bizId);
                        t.setResultFileList(list);
                    });
                }
            }
        }

        // 获取工单检查项列表
        if(resultList != null){
            Integer inspectedDeviceCount = 0;
            Integer notInspectDeviceCount = 0;
            Integer errorDeviceCount = 0;
            Integer errorItemCount = 0;

            for(InspectResultVo vo:resultList){
                Long inspectResultId = vo.getId();
                Integer status = vo.getStatus();
                if(status == null){
                    notInspectDeviceCount++;
                }else{
                    if(InspectResultStatusEnum.INSPECTED.getValue().equals(status)){
                        inspectedDeviceCount++;
                    }else{
                        notInspectDeviceCount++;
                    }
                }
                List<DeviceInspectItemDto> itemList = inspectResultItemMapper.listByResultIdWithDetail(inspectResultId);
                if(itemList == null){
                    itemList = new ArrayList<>();
                }
                vo.setTotalItemCount(itemList.size());
                List<String> errorItemDescList = new ArrayList<>();
                Long errorCount = itemList.stream().filter(t -> {
                    if(t.getIsError() == null){
                        return false;
                    }
                    if(YesNoEnum.YES.getValue().equals(t.getIsError())){
                        String s = "部位：" + (StringUtils.isEmpty(t.getFullBomName())?"通用":t.getFullBomName()) + "；" + "检查项目：" + t.getItemName() + "；" + "检查记录：" + t.getResultValue();
                        errorItemDescList.add(s);
                        return true;
                    }
                    return false;
                }).count();
                errorItemCount += errorCount.intValue();
                vo.setErrorItemCount(errorCount.intValue());
                vo.setErrorItemDescList(errorItemDescList);

                List<InspectResultStandardVo> standardList = inspectResultStandardMapper.listStandardByResultId(vo.getId());
                if(standardList != null && standardList.size() > 0){
                    List<Long> standardIdList = new ArrayList<>();
                    List<String> standardNameList = new ArrayList<>();
                    standardList.forEach(t -> {
                        standardIdList.add(t.getInspectStandardId());
                        standardNameList.add(t.getStandardName());
                    });
                    vo.setStandardIdList(standardIdList);
                    vo.setStandardNameList(standardNameList);
                }

                Integer isError = vo.getIsError();
                if(isError != null && YesNoEnum.YES.getValue().equals(isError)){
                    errorDeviceCount++;
                }
            }
            orderVo.setTotalDeviceCount(resultList.size());
            orderVo.setInspectedDeviceCount(inspectedDeviceCount);
            orderVo.setNotInspectDeviceCount(notInspectDeviceCount);
            orderVo.setErrorDeviceCount(errorDeviceCount);
            orderVo.setErrorItemCount(errorItemCount);
            
        }else{
            orderVo.setTotalDeviceCount(0);
            orderVo.setInspectedDeviceCount(0);
            orderVo.setNotInspectDeviceCount(0);
            orderVo.setErrorDeviceCount(0);
            orderVo.setErrorItemCount(0);

        }

        Long currentUserId = JwtUserInfoUtils.getUserId();
        Result<List<TaskDto>> queryTodoTaskListResult = orderProcessService.queryTodoTaskList(currentUserId);
        if(!queryTodoTaskListResult.getSignal()){
            throw new BizException(queryTodoTaskListResult.getMessage());
        }
        List<TaskDto> result = queryTodoTaskListResult.getResult();
        Set<String> processInstanceIds = result.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toSet());
        String processInstanceId = entity.getProcessInstanceId();
        Result<Map<String, String>> queryLastOperatorByProcessInstancesResult = orderProcessService.queryLastOperatorByProcessInstances(Arrays.asList(processInstanceId));
        if(!queryLastOperatorByProcessInstancesResult.getSignal()){
            throw new BizException(queryLastOperatorByProcessInstancesResult.getMessage());
        }
        Map<String, String> lastOperatorMap = queryLastOperatorByProcessInstancesResult.getResult();

        Result<Map<String, Boolean>> forwardPermissionMapResult = orderProcessService.queryForwardPermissionByProcessInstances(Arrays.asList(processInstanceId));
        if(!forwardPermissionMapResult.getSignal()){
            throw new BizException(forwardPermissionMapResult.getMessage());
        }
        Map<String,Boolean> forwardPermissionMap = forwardPermissionMapResult.getResult();

        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantId, idType);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantId, currentUserId, idType);
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;

        Result<Map<String, Boolean>> acceptMapResult = orderProcessService.queryAcceptStateByProcessInstances(Arrays.asList(processInstanceId), idType);
        if (!acceptMapResult.getSignal()) {
            throw new BizException(acceptMapResult.getMessage());
        }
        Map<String, Boolean> acceptMap = acceptMapResult.getResult();


        orderVo.setOperations(listOrderOperations(
                entity, currentUserId, tenantId,
                processInstanceIds, lastOperatorMap, forwardPermissionMap,
                acceptMap,
                idType, isSuperRole,
                processInstanceIds.contains(entity.getProcessInstanceId())
        ));

        // 获取进度列表
        List<InspectOrderProgressVo> progressList = inspectOrderProgressMapper.listProgressbyOrderId(inspectOrderId);
        orderVo.setProgressList(progressList);

        // 人员工作量
        List<WorkloadEntity> workloadList = workloadMapper.listByOrderId(OrderTypeEnum.INSPECT.getValue(), inspectOrderId);
        orderVo.setWorkloadList(workloadList);
        return Result.ok(orderVo);
    }

    public Result<List<InspectOrderDto>> listMyWaitProcessOrder(
        Long tenantId, 
        Integer idType,
        InspectOrderParams params
    ) {
        // 当前用户id
        Long currentUserId = JwtUserInfoUtils.getUserId();
        
        
        Result<List<TaskDto>> queryTodoTaskListResult = orderProcessService.queryTodoTaskList(currentUserId);
        if(!queryTodoTaskListResult.getSignal()){
            return Result.error(queryTodoTaskListResult.getMessage());
        }
        List<TaskDto> todoTaskList = queryTodoTaskListResult.getResult();
        
        if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
            params.setOutsource(OutsourceEnum.YES.getValue());
        }

        // 根据状态查询工单
        List<InspectOrderDto> list = inspectOrderMapper.listByAuth(tenantId, idType, todoTaskList, currentUserId, params);

        return Result.ok(list);
    }

    public Result<Page<InspectOrderDto>> pageMyWaitProcessOrder(
        Long tenantId, 
        Integer idType,
        InspectOrderParams params,
        Page<InspectOrderEntity> page
    ) {
        Long currentUserId = JwtUserInfoUtils.getUserId();


        Result<List<TaskDto>> queryTodoTaskListResult = orderProcessService.queryTodoTaskList(currentUserId);
        if(!queryTodoTaskListResult.getSignal()){
            return Result.error(queryTodoTaskListResult.getMessage());
        }
        List<TaskDto> todoTaskList = queryTodoTaskListResult.getResult();

        // 根据状态查询工单
        Page<InspectOrderDto> list = inspectOrderMapper.pageByAuth(
            tenantId, idType, 
            todoTaskList, 
            currentUserId, 
            params, page
        );

        // 操作按钮
        if(list != null){
            List<InspectOrderDto> records = list.getRecords();
            if(records != null){
                if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                    records.forEach(i->{i.setSupportSupplierId(null);});
                }
                List<String> processInstanceIdList = todoTaskList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
                Set<String> processInstanceIds = todoTaskList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toSet());
                List<String> collect = records.stream().map(InspectOrderDto::getProcessInstanceId)
                .filter(t -> {
                    return t != null && !"".equals(t);
                })
                .collect(Collectors.toList());
                Result<Map<String, String>> queryLastOperatorByProcessInstancesResult = orderProcessService.queryLastOperatorByProcessInstances(collect);
                if(!queryLastOperatorByProcessInstancesResult.getSignal()){
                    throw new BizException(queryLastOperatorByProcessInstancesResult.getMessage());
                }
                Map<String, String> lastOperatorMap = queryLastOperatorByProcessInstancesResult.getResult();
                
                Result<Map<String, Boolean>> forwardPermissionMapResult = orderProcessService.queryForwardPermissionByProcessInstances(collect);
                if(!forwardPermissionMapResult.getSignal()){
                    throw new BizException(forwardPermissionMapResult.getMessage());
                }
                Map<String,Boolean> forwardPermissionMap = forwardPermissionMapResult.getResult();
                
                records.forEach(record -> {
                    record.setOperations(listOrderOperations(
                        record, currentUserId, tenantId,
                        processInstanceIds, lastOperatorMap, forwardPermissionMap,
                        new HashMap<>(),
                        idType, 
                        false,
                        true
                    ));
                });
            }
        }
        return Result.ok(list);
    }

    public Result<Page<InspectOrderDto>> pageMyAlreadyProcessOrder(
        Long tenantId, 
        Integer idType,
        InspectOrderParams params,
        Page<InspectOrderEntity> page
    ) {
        // 当前用户id
        Long currentUserId = JwtUserInfoUtils.getUserId();
        // 根据状态查询工单
        Page<InspectOrderDto> list = inspectOrderMapper.pageByProcessUserId(tenantId, idType, currentUserId, params, page);

        // 操作按钮
        if(list != null){
            List<InspectOrderDto> records = list.getRecords();
            if(records != null){
                if (IdTypeEnum.CUSTOMER.getValue().equals(idType)) {
                    records.forEach(i->{i.setSupportSupplierId(null);});
                }
                Result<List<TaskDto>> queryTodoTaskListResult = orderProcessService.queryTodoTaskList(currentUserId);
                if(!queryTodoTaskListResult.getSignal()){
                    throw new BizException(queryTodoTaskListResult.getMessage());
                }
                List<TaskDto> result = queryTodoTaskListResult.getResult();
                List<String> processInstanceIdList = result.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
                Set<String> processInstanceIds = result.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toSet());
                List<String> collect = records.stream().map(InspectOrderDto::getProcessInstanceId)
                .filter(t -> {
                    return t != null && !"".equals(t);
                })
                .collect(Collectors.toList());
                Result<Map<String, String>> queryLastOperatorByProcessInstancesResult = orderProcessService.queryLastOperatorByProcessInstances(collect);
                if(!queryLastOperatorByProcessInstancesResult.getSignal()){
                    throw new BizException(queryLastOperatorByProcessInstancesResult.getMessage());
                }
                Map<String, String> lastOperatorMap = queryLastOperatorByProcessInstancesResult.getResult();
                
                records.forEach(record -> {
                    record.setOperations(listOrderOperations(
                        record, currentUserId, tenantId,
                        processInstanceIds, lastOperatorMap, 
                        new HashMap<>(),
                        new HashMap<>(),
                        idType, 
                        false,
                        false
                    ));
                });
            }
        }

        return Result.ok(list);
    }

    public Result<DeviceInspectResultVo> getDeviceInspectResult(
        Long tenantId, Integer idType, Long inspectOrderId, Long deviceId
    ) {
        // 查出检查结果列表
        InspectResultEntity inspectResult = inspectResultMapper.getByOrderDeviceId(inspectOrderId, deviceId);
        DeviceInspectResultVo vo = new DeviceInspectResultVo();
        if(inspectResult == null){
            vo.setItemList(new ArrayList<>());
            return Result.ok(vo);
        }
        Long inspectResultId = inspectResult.getId();
        // 查出检查结果图片
        List<FileVo> resultFileList = fileMapper.listByBizIds(
            FileBizTypeEnum.INSPECT_RESULT_PICTURE.getValue(), 
            new ArrayList<Long>(){{add(inspectResultId);}}
        );
        vo.setResultFileList(resultFileList);
        // 查出检查结果项（关联标准项）
        List<DeviceInspectItemDto> inspectItemList = inspectResultItemMapper.listByResultIdWithDetail(inspectResultId);
        // 查出检查结果项标准附图
        if(inspectItemList != null && inspectItemList.size() > 0){
            List<Long> bizIds = inspectItemList.stream().map(DeviceInspectItemDto::getInspectStandardItemId).collect(Collectors.toList());
            if(bizIds != null && bizIds.size() > 0){
                List<FileVo> fileList = fileMapper.listByBizIds(FileBizTypeEnum.INSPECT_STANDARD_ITEM_PICTURE.getValue(), bizIds);
                if(fileList != null && fileList.size() > 0){
                    // bizId -> fileList
                    Map<Long, List<FileVo>> map = fileList.stream().collect(Collectors.groupingBy(FileVo::getBizId));
                    inspectItemList.forEach(t -> {
                        Long bizId = t.getInspectStandardItemId();
                        List<FileVo> list = map.get(bizId);
                        t.setStandardFileList(list);
                    });
                }
            }
        }
        // 查出检查结果项检查图片
        if(inspectItemList != null && inspectItemList.size() > 0){
            List<Long> bizIds = inspectItemList.stream().map(DeviceInspectItemDto::getId).collect(Collectors.toList());
            if(bizIds != null && bizIds.size() > 0){
                List<FileVo> fileList = fileMapper.listByBizIds(FileBizTypeEnum.INSPECT_RESULT_ITEM_PICTURE.getValue(), bizIds);
                if(fileList != null && fileList.size() > 0){
                    // bizId -> fileList
                    Map<Long, List<FileVo>> map = fileList.stream().collect(Collectors.groupingBy(FileVo::getBizId));
                    inspectItemList.forEach(t -> {
                        Long bizId = t.getId();
                        List<FileVo> list = map.get(bizId);
                        t.setInspectFileList(list);
                    });
                }
            }
        }
        vo.setItemList(inspectItemList);
        vo.setInspectBegin(inspectResult.getInspectBegin());
        vo.setInspectEnd(inspectResult.getInspectEnd());
        return Result.ok(vo);
    }

    public Result<DeviceInspectCountDto> countDeviceInspectOrder(
        Long tenantId, Integer idType, Long deviceId
    ) {
        DeviceInspectCountDto countDto = new DeviceInspectCountDto();
        {
            InspectOrderParams params = new InspectOrderParams();
            params.setDeviceId(deviceId);
            params.setOutsource(OutsourceEnum.NO.getValue());
            Page<InspectOrderEntity> page = new Page<>();
            page.setCurrent(1);
            page.setSize(10);
            Result<InspectOrderPage> r1 = pageByParams(tenantId, idType, params, page);
            InspectOrderPage p1 = r1.getResult();
            Long insourceCount = p1.getTotal();
            countDto.setInsourceCount(insourceCount.intValue());
        }
        {
            InspectOrderParams params = new InspectOrderParams();
            params.setDeviceId(deviceId);
            params.setOutsource(OutsourceEnum.YES.getValue());
            Page<InspectOrderEntity> page = new Page<>();
            page.setCurrent(1);
            page.setSize(10);
            Result<InspectOrderPage> r1 = pageByParams(tenantId, idType, params, page);
            InspectOrderPage p1 = r1.getResult();
            Long outsourceCount = p1.getTotal();
            countDto.setOutsourceCount(outsourceCount.intValue());
        }

        return Result.ok(countDto);
    }

    @Transactional
    public Result<Void> submitInspectOutboundOrder(Long tenantId, Integer idType, InspectOutboundOrderDto dto) {
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(entity == null){
            return Result.error("找不到工单");
        }
        List<WarehouseSparesDto> warehouseSparesList = dto.getWarehouseSparesList();
        if(warehouseSparesList == null || warehouseSparesList.size() <= 0){
            return Result.ok();
        }
        for(WarehouseSparesDto warehouseSpares:warehouseSparesList){
            Long sparesWarehouseId = warehouseSpares.getSparesWarehouseId();
            // todo调用出库接口
        }

        return Result.ok();
    }

    @Transactional
    public Result<Void> submitResultFile(Long tenantId, Integer idType, InspectResultFileDto dto) {
        Long inspectResultId = dto.getInspectResultId();
        InspectResultEntity entity = inspectResultMapper.selectById(inspectResultId);
        if(entity == null){
            return Result.error("找不到工单结果");
        }
        Long inspectOrderId = entity.getInspectOrderId();
        InspectOrderEntity orderEntity = inspectOrderMapper.selectById(inspectOrderId);
        if(!tenantId.equals(orderEntity.getTenantId()) 
            && !tenantId.equals(orderEntity.getSupplierId()) 
            && !tenantId.equals(orderEntity.getCustomerId())
        ){
            return Result.error("找不到工单结果");
        }

        Long bizId = inspectResultId;

        // 插入新文件
        List<FileVo> fileList = dto.getFileList();
        if(fileList != null && fileList.size() > 0){
            fileList.forEach(f -> {
                FileEntity e = new FileEntity();
                BeanUtils.copyProperties(f, e);
                e.setBizId(bizId);
                e.setTenantId(tenantId);
                e.setBizType(FileBizTypeEnum.INSPECT_RESULT_PICTURE.getValue());
                fileMapper.insert(e);
            });
        }

        return Result.ok();
    }

    public Result<Void> submitOrderFile(Long tenantId, Integer idType, InspectOrderFileDto dto) {
        Long inspectOrderId = dto.getInspectOrderId();
        InspectOrderEntity entity = inspectOrderMapper.selectById(inspectOrderId);
        if(!tenantId.equals(entity.getTenantId()) 
            && !tenantId.equals(entity.getSupplierId()) 
            && !tenantId.equals(entity.getCustomerId())
        ){
            return Result.error("找不到工单结果");
        }

        Long bizId = inspectOrderId;

        // 插入新文件
        List<FileVo> fileList = dto.getFileList();
        if(fileList != null && fileList.size() > 0){
            fileList.forEach(f -> {
                FileEntity e = new FileEntity();
                BeanUtils.copyProperties(f, e);
                e.setBizId(bizId);
                e.setTenantId(tenantId);
                e.setBizType(FileBizTypeEnum.INSPECT_ORDER_PICTURE.getValue());
                fileMapper.insert(e);
            });
        }

        return Result.ok();
    }

    public Result<InspectOrderMySummaryVo> mySummary(Long tenantId, Integer idType) {
        
        Long currentUserId = JwtUserInfoUtils.getUserId();
        Result<List<TaskDto>> queryTodoTaskListResult = orderProcessService.queryTodoTaskList(currentUserId);
        if(!queryTodoTaskListResult.getSignal()){
            return Result.error(queryTodoTaskListResult.getMessage());
        }
        List<TaskDto> todoTaskList = queryTodoTaskListResult.getResult();
        
        // 内委
        InspectOrderParams params1 = new InspectOrderParams();
        params1.setOutsource(OutsourceEnum.NO.getValue());
        if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
            params1.setOutsource(OutsourceEnum.YES.getValue());
        }
        Integer count1 = inspectOrderMapper.countByAuth(
            tenantId, idType, 
            todoTaskList, 
            currentUserId, 
            params1
        );

        // 外委
        InspectOrderParams params2 = new InspectOrderParams();
        params2.setOutsource(OutsourceEnum.YES.getValue());
        if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
            params2.setOutsource(OutsourceEnum.YES.getValue());
        }
        Integer count2 = inspectOrderMapper.countByAuth(
            tenantId, idType, 
            todoTaskList, 
            currentUserId, 
            params2
        );

        InspectOrderMySummaryVo vo = new InspectOrderMySummaryVo();
        vo.setMyProcessingInspectOrderCount(count1);
        vo.setMyProcessingOutsourceInspectOrderCount(count2);
        return Result.ok(vo);
    }

    /**
     * 工单超时监测
     * @return
     */
    @Transactional
    public Result orderTimeoutMonitor() {
        //查询所有未结束、未超时的工单
        List<InspectOrderEntity> orderList = inspectOrderMapper.listProcessingOrder();
        if (CollectionUtil.isEmpty(orderList)) {
            return Result.ok();
        }

        Map<Long, List<InspectOrderEntity>> supplierOrderMap = orderList.stream()
                .filter(order -> IdTypeEnum.SUPPLIER.getValue().equals(order.getCreateSource()))
                .collect(Collectors.groupingBy(InspectOrderEntity::getSupplierId));
        Map<Long, List<InspectOrderEntity>> customerOrderMap = orderList.stream()
                .filter(order -> IdTypeEnum.CUSTOMER.getValue().equals(order.getCreateSource()))
                .collect(Collectors.groupingBy(InspectOrderEntity::getTenantId));

        Set<Long> tenantIds = new HashSet<>();
        tenantIds.addAll(supplierOrderMap.keySet());
        tenantIds.addAll(customerOrderMap.keySet());

        //获取租户超时配置(启用)
        List<TimeoutConfigEntity> allTimeoutConfigList = timeoutConfigService.list(Wrappers.<TimeoutConfigEntity>lambdaQuery()
                .eq(TimeoutConfigEntity::getOrderType, OrderTypeEnum.INSPECT.getValue())
                .eq(TimeoutConfigEntity::getStatus, YesNoEnum.YES.getValue())
                .in(TimeoutConfigEntity::getTenantId, tenantIds));
        Map<Long, List<TimeoutConfigEntity>> tenantTimeoutMap = allTimeoutConfigList.stream()
                .collect(Collectors.groupingBy(TimeoutConfigEntity::getTenantId));

        LocalDateTime now = LocalDateTime.now();

        //遍历租户超时配置，判断工单是否超时
        tenantTimeoutMap.forEach((tenantId, tenantTimeoutList) -> {
            if (CollectionUtil.isEmpty(tenantTimeoutList)) {
                return;
            }
            List<InspectOrderEntity> supplierOrders = supplierOrderMap.getOrDefault(tenantId, new ArrayList<>());
            List<InspectOrderEntity> customerOrders = customerOrderMap.getOrDefault(tenantId, new ArrayList<>());
            List<InspectOrderEntity> tenantOrderList = new ArrayList<>();
            tenantOrderList.addAll(supplierOrders);
            tenantOrderList.addAll(customerOrders);
            if (CollectionUtil.isEmpty(tenantOrderList)) {
                return;
            }
            handle(tenantTimeoutList, tenantOrderList, now);
        });
        return Result.ok();
    }

    private void handle(List<TimeoutConfigEntity> timeoutConfigList, List<InspectOrderEntity> tenantOrderList,
                        LocalDateTime now) {
        Map<Integer, List<InspectOrderEntity>> sourceOrderMap = tenantOrderList.stream()
                .collect(Collectors.groupingBy(InspectOrderEntity::getOutsource));
        timeoutConfigList.forEach(timeoutConfig -> {
            Integer timeoutSecond = timeoutConfig.getTimeoutSecond();
            Integer configType = timeoutConfig.getConfigType();
            Long tenantId = timeoutConfig.getTenantId();
            Integer idType = timeoutConfig.getIdType();

            //获取工单通知渠道
            List<MessageStrategyConfigVo> messageNoticeConfigList = messageNoticeStrategyMapper.queryOrderTimeoutStrategyConfig(
                    timeoutConfig.getTenantId(), OrderTypeEnum.INSPECT.getValue(), configType, idType);

            List<InspectOrderEntity> orderList = Lists.newArrayList();
            if (TimeoutConfigTypeEnum.INSIDE.getValue().equals(configType)) {
                //内部工单
                if (sourceOrderMap.get(OutsourceEnum.NO.getValue()) != null) {
                    orderList = sourceOrderMap.get(OutsourceEnum.NO.getValue());
                }
            } else if (TimeoutConfigTypeEnum.OUTSIDE.getValue().equals(configType)) {
                //外委工单
                if(sourceOrderMap.get(OutsourceEnum.YES.getValue()) != null) {
                    orderList = sourceOrderMap.get(OutsourceEnum.YES.getValue()).stream()
                            .filter(order -> order.getCreateSource().equals(idType))
                            .collect(Collectors.toList());
                }
            }
            //超时判断
            orderList.forEach(order -> {
                LocalDateTime createTime = order.getCreateTime();
                //超时时间
                LocalDateTime timeoutTime = createTime.plusSeconds(timeoutSecond);
                if (now.isAfter(timeoutTime)) {
                    //设置超时
                    Integer count = inspectOrderMapper.orderTimeout(order.getId());

                    // 保存告警记录
                    saveAlramRecord(order, now, tenantId, idType);

                    //发送通知
                    if (CollectionUtil.isEmpty(messageNoticeConfigList)) {
                        return;
                    }
                    messageNoticeConfigList.forEach(messageNoticeConfig -> sendMessageNotice(order, messageNoticeConfig, now));
                }
            });
        });
    }

    private void saveAlramRecord(InspectOrderEntity inspectOrder, LocalDateTime alarmTime,
                                 Long tenantId, Integer idType) {
        AlarmRecordEntity alarmRecord = new AlarmRecordEntity();
        alarmRecord.setOrderId(inspectOrder.getId());
        alarmRecord.setAlarmType(AlarmTypeEnum.ORDER_TIMEOUT.getValue());
        alarmRecord.setAlarmTime(alarmTime);
        alarmRecord.setOrderNo(inspectOrder.getOrderNumber());
        alarmRecord.setOrderCustomerId(inspectOrder.getCustomerId());
        alarmRecord.setOrderSource(OrderTypeEnum.INSPECT.getValue());
        alarmRecord.setOrderStatus(inspectOrder.getStatus());
        alarmRecord.setTenantId(tenantId);
        alarmRecord.setIdType(idType);
        alarmRecord.setCreatorId(1L);
        alarmRecord.setCreator("定时任务");
        Result saveAlarmResult = alarmRecordService.saveAlarmRecord(alarmRecord);
        if (!saveAlarmResult.getSignal()) {
            log.error("保存巡检工单超时告警记录失败：{} {}", inspectOrder.getId(), saveAlarmResult.getMessage());
        }
    }

    private void sendMessageNotice(InspectOrderEntity inspectOrder, MessageStrategyConfigVo messageNoticeConfig, LocalDateTime now) {
        Integer idType = messageNoticeConfig.getIdType();
        String customerName = "";
        if (IdTypeEnum.SUPPLIER.getValue().equals(idType) && IdTypeEnum.SUPPLIER.getValue().equals(inspectOrder.getCreateSource())) {
            customerName = inspectOrder.getCustomerName();
        }

        OrderMessageDto orderMessageDto = OrderMessageDto.builder()
                .orderTypeEnum(OrderTypeEnum.INSPECT)
                .orderStatusEnum(OrderStatusEnum.typeOfValue(inspectOrder.getStatus()))
                .messageTypeEnum(MessageTypeEnum.ORDER_TIMEOUT)
                .timeoutEnum(TimeoutEnum.TIMEOUT)
                .orderNo(inspectOrder.getOrderNumber())
                .outsource(inspectOrder.getOutsource())
                .tenantId(messageNoticeConfig.getTenantId())
                .idType(messageNoticeConfig.getIdType())
                .customerName(customerName)
                .now(now)
                .build();

        asyncSendNoticeService.sendOrderTimeoutNotice(messageNoticeConfig, orderMessageDto);
    }

    @Transactional
    public Result orderOverdueMonitor() {
        List<InspectOrderEntity> orderList = inspectOrderMapper.selectList(Wrappers.<InspectOrderEntity>lambdaQuery()
                .eq(InspectOrderEntity::getOverdue, OverdueEnum.NO.getValue())
                .in(InspectOrderEntity::getStatus, Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(), OrderStatusEnum.EXECUTING.getValue())));
        if (CollectionUtil.isEmpty(orderList)) {
            return Result.ok();
        }
        LocalDate now = LocalDate.now();
        List<Long> overdueIdList = new ArrayList<>();
        orderList.forEach(order -> {
            if(order.getPlanInspectTime() == null){
                return;
            }
            LocalDate planInspectDate = order.getPlanInspectTime().toLocalDate();
            if (planInspectDate != null) {
                Long daysBetween = ChronoUnit.DAYS.between(now, planInspectDate);
                if (daysBetween < 0) {
                    order.setOverdue(OverdueEnum.YES.getValue());
                    overdueIdList.add(order.getId());
                }
            }
        });
        if (CollectionUtil.isNotEmpty(overdueIdList)) {
            Integer count = inspectOrderMapper.updateBatchOverdueByIds(overdueIdList);
        }
        return Result.ok();
    }

}
