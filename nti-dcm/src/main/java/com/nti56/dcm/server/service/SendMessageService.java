package com.nti56.dcm.server.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nti56.common.constant.Constant;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.MessageNoticeConfigEntity;
import com.nti56.dcm.server.entity.ReportRecordEntity;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.dcm.server.model.vo.MessageStrategyConfigVo;
import com.nti56.msg.center.common.tenant.TenantIsolation;
import com.nti56.msg.center.domain.entity.TemplateEntity;
import com.nti56.msg.center.domain.model.dto.send.NotifyDTO;
import com.nti56.msg.center.domain.model.dto.send.SendNotifyDTO;
import com.nti56.msg.center.domain.model.log.NotifyLogVo;
import com.nti56.msg.center.service.ISendNotifyService;
import com.nti56.msg.center.service.ITemplateService;
import com.nti56.user.sdk.response.UserOnlineTimePageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class SendMessageService {

    @Value("${dcm.ding-app-url}")
    private String dingAppUrl;

    @Value("${dcm.ding-pc-url}")
    private String dingPcUrl;

    @Value("${dcm.ding-base-url}")
    private String dingBaseUrl;

    @Value("${dcm.app-base-url}")
    private String appBaseUrl;

    @Value("${dcm.report-app-url}")
    private String reportAppUrl;

    @Value("${use-report.template-id}")
    private String useReportTemplateId;

    @Autowired
    private ITemplateService templateService;

    @Autowired
    private ISendNotifyService sendNotifyService;

    @Autowired
    private DeviceChartService deviceChartService;

    @Autowired
    private DeviceLocationService deviceLocationService;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    public void sendNotify(Long tenantId, List<String> receivers, MessageStrategyConfigVo messageNoticeConfig,
                                   String orderTypeNameDesc, String orderStatusNameDesc, String orderNo, String customerName,
                                   String title, String timeout,
                                   LocalDateTime now, DingCardMessageDto dingCardDto) {
        if (CollectionUtil.isEmpty(receivers)) {
            log.info("接收人为空，不发送通知");
            return;
        }
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);

        Long templateId = messageNoticeConfig.getTemplateId();
        TemplateEntity template = templateService.getByIdAndTenantIsolation(templateId, tenantIsolation);
        if (template == null) {
            log.error("渠道模板不存在：" + templateId);
            return;
        }

        Set<String> mobileSet = new HashSet<>(receivers);
        SendNotifyDTO sendNotifyDTO = new SendNotifyDTO();
        tenantIsolation.setApplicationId(tenantId);
        tenantIsolation.setAppCode(Constant.APPCODE_HEADER);
        sendNotifyDTO.setTenantIsolation(tenantIsolation);
        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setTemplateId(templateId);
        notifyDTO.setTemplateIds(Arrays.asList(templateId));
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("time", formatter.format(now));
        paramsMap.put("key1", title);
        paramsMap.put("key2", "类型：" + orderTypeNameDesc);
        paramsMap.put("key3", "编号：" + orderNo);
        paramsMap.put("key4", "进度：" + orderStatusNameDesc);
        paramsMap.put("key5", StringUtils.isNotEmpty(customerName) ? "客户：" + customerName : "");
        paramsMap.put("key6", "是否超时：" + timeout);

        if (Objects.nonNull(dingCardDto)) {
            String corpId = getCorpId(messageNoticeConfig.getParams());
            if (StringUtils.isEmpty(corpId)) {
                return;
            }
            // 把dingCardDto转成json字符串，再将json字符串进行base64编码
            String json = Base64.getEncoder().encodeToString(JSONObject.toJSONString(dingCardDto).getBytes());
            try {
                String baseUrl = dingBaseUrl + corpId;
                paramsMap.put("appUrl", "dingtalk://dingtalkclient/page/link?pc_slide=true&url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + dingAppUrl + json, "utf-8"));
                paramsMap.put("pcUrl", "dingtalk://dingtalkclient/action/openapp?corpid=" + corpId + "&container_type=work_platform&app_id=0_1365181033&redirect_type=jump&redirect_url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + dingPcUrl + json, "utf-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("钉钉卡片URL编码失败：" + e.getMessage());
                return;
            }
        }

        notifyDTO.setParams(JSONObject.toJSONString(paramsMap));
        sendNotifyDTO.setNotify(notifyDTO);

        // 将接收者按每批20个进行分组
        List<List<String>> batches = splitReceivers(new ArrayList<>(mobileSet), 20);
        for (List<String> batch : batches) {
            notifyDTO.setReceivers(String.join(",", batch));
            log.info("sendNotifyDTO is {}", JSONObject.toJSONString(sendNotifyDTO));
            com.nti56.msg.center.common.util.Result<NotifyLogVo> sendResult = sendNotifyService.sendSerial(sendNotifyDTO);
            // com.nti56.msg.center.common.util.Result<Void> sendResult = sendNotifyService.put2NotifyQueue(sendNotifyDTO);

            if (!sendResult.getSignal()) {
                log.error("发送通知失败，{} {} {}", orderTypeNameDesc, orderNo, sendResult.getMessage());
            }
        }
    }

    public void sendDeviceNotify(MessageStrategyConfigVo deviceNoticeConfig, List<String> receivers,
                                    SendMessageNoticeDto noticeDto, DingCardMessageDto dingCardDto) {
        com.nti56.msg.center.common.tenant.TenantIsolation tenantIsolation = new com.nti56.msg.center.common.tenant.TenantIsolation();
        tenantIsolation.setTenantId(deviceNoticeConfig.getTenantId());

        Long tenantId = deviceNoticeConfig.getTenantId();
        Long templateId = deviceNoticeConfig.getTemplateId();
        TemplateEntity template = templateService.getByIdAndTenantIsolation(templateId, tenantIsolation);
        if (template == null) {
            log.error("渠道模板不存在：" + templateId);
            return;
        }
        Integer deviceState = noticeDto.getDeviceState();
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.typeOfValue(deviceState);

        Set<String> mobileSet = new HashSet<>(receivers);
        SendNotifyDTO sendNotifyDTO = new SendNotifyDTO();
        tenantIsolation.setApplicationId(tenantId);
        tenantIsolation.setAppCode(Constant.APPCODE_HEADER);
        sendNotifyDTO.setTenantIsolation(tenantIsolation);
        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setTemplateId(templateId);
        notifyDTO.setTemplateIds(Arrays.asList(templateId));

        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("time", noticeDto.getAlarmTime());
        paramsMap.put("key2", messageTypeEnum.getTitle());
        paramsMap.put("key3", StringUtils.isNotEmpty(noticeDto.getCustomerName()) ? "客户：" + noticeDto.getCustomerName() : "");
        paramsMap.put("key5", deviceNoticeConfig.getSerialNumber());
        paramsMap.put("key6", deviceNoticeConfig.getDeviceTypeName());

        if (MessageTypeEnum.DEVICE_ERROR.getValue().equals(deviceState) || MessageTypeEnum.DEVICE_FAULT.getValue().equals(deviceState)
                || MessageTypeEnum.DEVICE_OFFLINE.getValue().equals(deviceState) || MessageTypeEnum.DEVICE_ONLINE.getValue().equals(deviceState)) {
            paramsMap.put("key7", deviceLocationService.getFullLocationPath(deviceNoticeConfig.getDeviceLocationId()));

            ITResult<Object> result = deviceChartService.queryOnLineTimeSumOfDevice(tenantId , deviceNoticeConfig.getDeviceId(), timeFormatter.format(LocalDateTime.now().minusDays(30)), timeFormatter.format(LocalDateTime.now()));
            if (ITResult.SUCCESS_CODE.equals(result.getCode())) {
                try {
                    JSONArray dataArray = JSONArray.parseArray(JSONObject.toJSONString(result.getData()));
                    if (dataArray != null && !dataArray.isEmpty()) {
                        JSONObject data = dataArray.getJSONObject(0);
                        if (data != null && data.containsKey("records")) {
                            JSONArray records = data.getJSONArray("records");
                            if (!records.isEmpty()) {
                                JSONObject record = records.getJSONObject(0);
                                JSONObject values = record.getJSONObject("values");
                                if (values != null && values.containsKey("durStr")) {
                                    String durStr = values.getString("durStr");
                                    paramsMap.put("key8", convertToChineseDuration(durStr));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("解析设备在线时长数据失败：", e);
                }
            }
        }

        if (deviceState.equals(MessageTypeEnum.DEVICE_MAINTENANCE_ADVENT.getValue())
                || deviceState.equals(MessageTypeEnum.DEVICE_WARRANTY_ADVENT.getValue())
                || deviceState.equals(MessageTypeEnum.DEVICE_SCRAP_ADVENT.getValue())
                || deviceState.equals(MessageTypeEnum.DEVICE_MAINTENANCE_EXPIRE.getValue())
                || deviceState.equals(MessageTypeEnum.DEVICE_WARRANTY_EXPIRE.getValue())
                || deviceState.equals(MessageTypeEnum.DEVICE_SCRAP_EXPIRE.getValue())) {
            paramsMap.put("key1", "设备数：" + noticeDto.getDeviceCount() + "台");
        } else {
            paramsMap.put("key1", "设备：" + deviceNoticeConfig.getDeviceName());
            if (deviceState.equals(AlarmTypeEnum.DEVICE_ERROR.getValue())) {
                // 故障描述
                paramsMap.put("key4", "故障描述:" + noticeDto.getFaultDesc());
                paramsMap.put("key9", noticeDto.getFaultLevel());
                paramsMap.put("key10", noticeDto.getFaultCode());
            } else if (deviceState.equals(AlarmTypeEnum.DEVICE_REPAIR.getValue())) {
                paramsMap.put("key4", "告警描述:" + noticeDto.getFaultDesc());
                paramsMap.put("key9", noticeDto.getFaultLevel());
            } else {
                paramsMap.put("key4", "");
            }
        }

        if (Objects.nonNull(dingCardDto)) {
            String corpId = getCorpId(deviceNoticeConfig.getParams());
            if (StringUtils.isEmpty(corpId)) {
                return;
            }

            try {
                // 把dingCardDto转成json字符串，再将json字符串进行base64编码
                String json = Base64.getUrlEncoder().encodeToString(JSONObject.toJSONString(dingCardDto).getBytes());

                String baseUrl = dingBaseUrl + corpId;
                paramsMap.put("appUrl", "dingtalk://dingtalkclient/page/link?pc_slide=true&url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + dingAppUrl + json, "utf-8"));
                paramsMap.put("pcUrl", "dingtalk://dingtalkclient/action/openapp?corpid=" + corpId + "&container_type=work_platform&app_id=0_1365181033&redirect_type=jump&redirect_url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + dingPcUrl + json, "utf-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("钉钉卡片URL编码失败：" + e.getMessage());
                return;
            }
        }
        notifyDTO.setParams(JSONObject.toJSONString(paramsMap));
        sendNotifyDTO.setNotify(notifyDTO);

        // 将接收者按每批20个进行分组
        List<List<String>> batches = splitReceivers(new ArrayList<>(mobileSet), 20);
        for (List<String> batch : batches) {
            notifyDTO.setReceivers(String.join(",", batch));
            log.info("sendNotifyDTO is {}", JSONObject.toJSONString(sendNotifyDTO));
            com.nti56.msg.center.common.util.Result<NotifyLogVo> sendResult = sendNotifyService.sendSerial(sendNotifyDTO);

            if (!sendResult.getSignal()) {
                log.error("发送通知失败，{} {} {}", messageTypeEnum.getTitle(), deviceNoticeConfig.getDeviceAliasName(), sendResult.getMessage());
            }
        }
    }

    public void sendStockNotify(List<String> receivers, MessageStrategyConfigVo messageNoticeConfig,
                           String itemName, String itemCode, String spareAlarmType, BigDecimal baseQty, LocalDateTime now,  DingCardMessageDto dingCardDto) {
        if (CollectionUtil.isEmpty(receivers)) {
            log.info("接收人为空，不发送通知");
            return;
        }
        Long tenantId = messageNoticeConfig.getTenantId();
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);

        Long templateId = messageNoticeConfig.getTemplateId();
        TemplateEntity template = templateService.getByIdAndTenantIsolation(templateId, tenantIsolation);
        if (template == null) {
            log.error("渠道模板不存在：" + templateId);
            return;
        }

        Set<String> mobileSet = new HashSet<>(receivers);
        SendNotifyDTO sendNotifyDTO = new SendNotifyDTO();
        tenantIsolation.setApplicationId(tenantId);
        tenantIsolation.setAppCode(Constant.APPCODE_HEADER);
        sendNotifyDTO.setTenantIsolation(tenantIsolation);
        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setTemplateId(templateId);
        notifyDTO.setTemplateIds(Arrays.asList(templateId));
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("key1", spareAlarmType);
        paramsMap.put("key2", itemCode);
        paramsMap.put("key3", itemName);
        paramsMap.put("key4", formatter.format(now));
        paramsMap.put("key5", baseQty.toString());

        if (Objects.nonNull(dingCardDto)) {
            String corpId = getCorpId(messageNoticeConfig.getParams());
            if (StringUtils.isEmpty(corpId)) {
                return;
            }
            // 把dingCardDto转成json字符串，再将json字符串进行base64编码
            String json = Base64.getEncoder().encodeToString(JSONObject.toJSONString(dingCardDto).getBytes());
            try {
                String baseUrl = dingBaseUrl + corpId;
                paramsMap.put("appUrl", "dingtalk://dingtalkclient/page/link?pc_slide=true&url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + dingAppUrl + json, "utf-8"));
                paramsMap.put("pcUrl", "dingtalk://dingtalkclient/action/openapp?corpid=" + corpId + "&container_type=work_platform&app_id=0_1365181033&redirect_type=jump&redirect_url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + dingPcUrl + json, "utf-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("钉钉卡片URL编码失败：" + e.getMessage());
                return;
            }
        }

        notifyDTO.setParams(JSONObject.toJSONString(paramsMap));
        sendNotifyDTO.setNotify(notifyDTO);

        // 将接收者按每批20个进行分组
        List<List<String>> batches = splitReceivers(new ArrayList<>(mobileSet), 20);
        for (List<String> batch : batches) {
            notifyDTO.setReceivers(String.join(",", batch));
            log.info("sendNotifyDTO is {}", JSONObject.toJSONString(sendNotifyDTO));
            com.nti56.msg.center.common.util.Result<NotifyLogVo> sendResult = sendNotifyService.sendSerial(sendNotifyDTO);

            if (!sendResult.getSignal()) {
                log.error("发送通知失败，{} {} {}", "备件库存告警", itemCode, sendResult.getMessage());
            }
        }
    }

    /**
     * 将接收者列表按批次大小分组
     * @param receivers 接收者列表
     * @param batchSize 每批大小
     * @return 分组后的批次列表
     */
    private List<List<String>> splitReceivers(List<String> receivers, int batchSize) {
        List<List<String>> batches = new ArrayList<>();
        int total = receivers.size();
        for (int i = 0; i < total; i += batchSize) {
            int end = Math.min(i + batchSize, total);
            batches.add(receivers.subList(i, end));
        }
        return batches;
    }

    /**
     * 发送报表通知
     * @param config
     * @param receivers
     * @param reportRecord
     */
    public void sendReportNotify(MessageNoticeConfigEditDto config,
                                 Set<String> reportRanges,
                                 List<String> receivers,
                                 ReportRecordEntity reportRecord) {
        com.nti56.msg.center.common.tenant.TenantIsolation tenantIsolation = new com.nti56.msg.center.common.tenant.TenantIsolation();
        tenantIsolation.setTenantId(config.getTenantId());

        Long tenantId = config.getTenantId();
        Long templateId = config.getTemplateId();
        TemplateEntity template = templateService.getByIdAndTenantIsolation(templateId, tenantIsolation);
        if (template == null) {
            log.error("渠道模板不存在：" + templateId);
            return;
        }

        Set<String> mobileSet = new HashSet<>(receivers);
        SendNotifyDTO sendNotifyDTO = new SendNotifyDTO();
        tenantIsolation.setApplicationId(tenantId);
        tenantIsolation.setAppCode(Constant.APPCODE_HEADER);
        sendNotifyDTO.setTenantIsolation(tenantIsolation);

        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setTemplateId(templateId);
        notifyDTO.setTemplateIds(Arrays.asList(templateId));

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.typeOfValue(reportRecord.getReportType());

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("key1", reportTypeEnum.getNameDesc());
        paramsMap.put("key2", reportRecord.getBelongDate());

        if (reportRanges.contains("1")) {
            paramsMap.put("visible1", "true");
            paramsMap.put("key3", String.valueOf(reportRecord.getNewCustomerCount()));
            paramsMap.put("key4", String.valueOf(reportRecord.getNewDeviceCount()));
            paramsMap.put("key5", String.valueOf(reportRecord.getNewConnectDeviceCount()));
        }
        if (reportRanges.contains("2")) {
            paramsMap.put("visible2", "true");
            paramsMap.put("key6", String.valueOf(reportRecord.getNewAlarmCount()));
            paramsMap.put("key7", String.valueOf(reportRecord.getNewErrorCount()));
        }
        if (reportRanges.contains("3")) {
            paramsMap.put("visible3", "true");
            paramsMap.put("key10", String.valueOf(reportRecord.getNewWarrantyAdventCusCount()));
            paramsMap.put("key11", String.valueOf(reportRecord.getNewWarrantyExpireCusCount()));
            paramsMap.put("key12", String.valueOf(reportRecord.getNewMaintenanceAdventCusCount()));
            paramsMap.put("key13", String.valueOf(reportRecord.getNewMaintenanceExpireCusCount()));
        }
        if (reportRanges.contains("4")) {
            paramsMap.put("visible4", "true");
            paramsMap.put("key8", String.valueOf(reportRecord.getServiceCustomerCount()));
            paramsMap.put("key9", String.valueOf(reportRecord.getFinishOrderCount()));
        }

        String corpId = "ding5033bbcf5247e61b35c2f4657eb6378f";
        try {
            ReportMessageDto reportMessageDto = ReportMessageDto.builder().id(reportRecord.getId()).idType(IdTypeEnum.SUPPLIER.getValue()).menuType("ReportRecord").build();
            // 把reportMessageDto转成json字符串，再将json字符串进行base64编码
            String json = Base64.getUrlEncoder().encodeToString(JSONObject.toJSONString(reportMessageDto).getBytes());
            String baseUrl = appBaseUrl + corpId;
            String url = "dingtalk://dingtalkclient/page/link?corpid=" + corpId + "&pc_slide=true&container_type=work_platform&app_id=0_1365181033&redirect_type=jump&url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + reportAppUrl + json, "utf-8");
            paramsMap.put("appUrl", url);
            paramsMap.put("pcUrl", url);
        } catch (UnsupportedEncodingException e) {
            log.error("钉钉卡片URL编码失败：" + e.getMessage());
            return;
        }

        notifyDTO.setParams(JSONObject.toJSONString(paramsMap));
        sendNotifyDTO.setNotify(notifyDTO);

        // 将接收者按每批20个进行分组
        List<List<String>> batches = splitReceivers(new ArrayList<>(mobileSet), 20);
        for (List<String> batch : batches) {
            notifyDTO.setReceivers(String.join(",", batch));
            log.info("sendNotifyDTO is {}", JSONObject.toJSONString(sendNotifyDTO));
            com.nti56.msg.center.common.util.Result<NotifyLogVo> sendResult = sendNotifyService.sendSerial(sendNotifyDTO);

            if (!sendResult.getSignal()) {
                log.error("发送报表通知失败，{} {} {}", reportTypeEnum.getNameDesc(), reportRecord.getBelongDate(), sendResult.getMessage());
            }
        }
    }

    /**
     * 发送云管使用情况通知
     */
    public void sendUseDcmReportNotify(List<UserOnlineTimePageResponse> dtoList, ReportTypeEnum reportTypeEnum,
                                       LocalDate startDate, LocalDate endDate, String belongDate, List<String> receivers) {
        Long tenantId = DictConstant.DEFAULT_TENANT_ID;
        com.nti56.msg.center.common.tenant.TenantIsolation tenantIsolation = new com.nti56.msg.center.common.tenant.TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        Long templateId = Long.parseLong(useReportTemplateId);
        TemplateEntity template = templateService.getByIdAndTenantIsolation(templateId, tenantIsolation);
        if (template == null) {
            log.error("渠道模板不存在：" + templateId);
            return;
        }

        SendNotifyDTO sendNotifyDTO = new SendNotifyDTO();
        tenantIsolation.setApplicationId(tenantId);
        tenantIsolation.setAppCode(Constant.APPCODE_HEADER);
        sendNotifyDTO.setTenantIsolation(tenantIsolation);

        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setTemplateId(templateId);
        notifyDTO.setTemplateIds(Arrays.asList(templateId));

        Integer userNum = dtoList.size();
        Long totalLoginTimes = dtoList.stream().map(UserOnlineTimePageResponse::getTotalLoginTimes).reduce(0L, Long::sum);
        BigDecimal totalOnlineTime = dtoList.stream().map(UserOnlineTimePageResponse::getTotalOnlineTime).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal avgOnlineTime = totalOnlineTime.divide(new BigDecimal(userNum), 2, RoundingMode.HALF_UP);

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("key1", reportTypeEnum.getNameDesc());
        paramsMap.put("key2", belongDate);
        paramsMap.put("key3", String.valueOf(userNum));
        paramsMap.put("key4", String.valueOf(totalLoginTimes));
        paramsMap.put("key5", String.valueOf(totalOnlineTime));
        paramsMap.put("key6", String.valueOf(avgOnlineTime));

        Map<String, Object> table = new HashMap<>();

        List<Map<String, Object>> data = new ArrayList<>();
        dtoList.forEach(dto -> {
            Map<String, Object> row = new HashMap<>();
            Map<String, Object> appInfo = new HashMap<>();
            appInfo.put("value", String.valueOf(dto.getTotalLoginTimes()));
            appInfo.put("fontSize", 14);
            row.put("appInfo", appInfo);

            Map<String, Object> rank = new HashMap<>();
            rank.put("value", dto.getEmpName());
            rank.put("fontSize", 14);
            row.put("rank", rank);

            Map<String, Object> uv = new HashMap<>();
            uv.put("value", String.valueOf(dto.getTotalOnlineTime()));
            uv.put("fontSize", 14);
            row.put("uv", uv);

            data.add(row);
        });

        List<Map<String, Object>> meta = new ArrayList<>();
        meta.add(new HashMap<String, Object>(){{
            put("aliasName",  "姓名");
            put("dataType",  "OBJECT");
            put("alias", "rank");
            put("weight", 30);
        }});
        meta.add(new HashMap<String, Object>(){{
            put("aliasName",  "登录次数");
            put("dataType",  "OBJECT");
            put("alias", "appInfo");
            put("weight", 30);
        }});
        meta.add(new HashMap<String, Object>(){{
            put("aliasName",  "在线时长(h)");
            put("dataType",  "OBJECT");
            put("alias", "uv");
            put("weight", 40);
        }});

        table.put("data", data);
        table.put("meta", meta);

        Map<String, Object> sysFullJsonObj = new HashMap<>();
        sysFullJsonObj.put("table", table);
        paramsMap.put("sys_full_json_obj", JSONObject.toJSONString(sysFullJsonObj));

        String corpId = "ding5033bbcf5247e61b35c2f4657eb6378f";
        try {
            ReportMessageDto reportMessageDto = ReportMessageDto.builder().start(startDate).end(endDate).idType(3).menuType("onlineStatistics").build();
            // 把reportMessageDto转成json字符串，再将json字符串进行base64编码
            String json = Base64.getUrlEncoder().encodeToString(JSONObject.toJSONString(reportMessageDto).getBytes());
            String baseUrl = appBaseUrl + corpId;
            String url = "dingtalk://dingtalkclient/page/link?corpid=" + corpId + "&pc_slide=true&container_type=work_platform&app_id=0_1365181033&redirect_type=jump&url=" + URLEncoder.encode(baseUrl + "&clientId=" + tenantId + "&returnUrl=" + reportAppUrl + json, "utf-8");
            paramsMap.put("appUrl", url);
            paramsMap.put("pcUrl", url);
        } catch (UnsupportedEncodingException e) {
            log.error("钉钉卡片URL编码失败：" + e.getMessage());
            return;
        }

        log.info("paramsMap is {}", JSONObject.toJSONString(paramsMap));
        notifyDTO.setParams(JSONObject.toJSONString(paramsMap));
        sendNotifyDTO.setNotify(notifyDTO);

        // 将接收者按每批20个进行分组
        List<List<String>> batches = splitReceivers(receivers, 20);
        for (List<String> batch : batches) {
            notifyDTO.setReceivers(String.join(",", batch));
            log.info("sendNotifyDTO is {}", sendNotifyDTO);
            com.nti56.msg.center.common.util.Result<NotifyLogVo> sendResult = sendNotifyService.sendSerial(sendNotifyDTO);

            if (!sendResult.getSignal()) {
                log.error("发送使用情况报表通知失败，{} {} {}", reportTypeEnum.getNameDesc(), belongDate, sendResult.getMessage());
            }
        }
    }

    private String getCorpId(String params) {
        if (StringUtils.isEmpty(params)) {
            return null;
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject.containsKey("corpId")) {
                String corpId = jsonObject.getString("corpId");
                return corpId;
            } else {
                // 处理corpId键不存在的情况
                return null;
            }
        } catch (Exception e) {
            // 记录异常信息
            log.error("Error parsing JSON: " + e.getMessage());
            return null;
        }
    }

    private String convertToChineseDuration(String durStr) {
        StringBuilder chineseDuration = new StringBuilder();
        String[] parts = durStr.split("(?<=[a-zA-Z])");
        for (String part : parts) {
            if (part.contains("w")) {
                chineseDuration.append(part.replace("w", "周"));
            } else if (part.contains("d")) {
                chineseDuration.append(part.replace("d", "天"));
            } else if (part.contains("h")) {
                chineseDuration.append(part.replace("h", "小时"));
            } else if (part.contains("m")) {
                chineseDuration.append(part.replace("m", "分钟"));
            } else if (part.contains("s")) {
                chineseDuration.append(part.replace("s", "秒"));
            }
        }
        return chineseDuration.toString();
    }

}
