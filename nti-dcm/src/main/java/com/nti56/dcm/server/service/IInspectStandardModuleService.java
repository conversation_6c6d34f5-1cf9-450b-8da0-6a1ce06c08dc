package com.nti56.dcm.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.dcm.server.entity.InspectStandardModuleEntity;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import java.util.List;

/**
 * <p>
 * 点巡检标准模块表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-07-04 09:39:39
 * @since JDK 1.8
 */
public interface IInspectStandardModuleService extends IService<InspectStandardModuleEntity> {

    Result<InspectStandardModuleEntity> create(TenantIsolation tenantIsolation, InspectStandardModuleEntity entity);

    Result<List<InspectStandardModuleEntity>> listByStandardId(TenantIsolation tenantIsolation, Long standardId);

    Result<Void> update(TenantIsolation tenantIsolation, InspectStandardModuleEntity entity);

    Result<Void> deleteById(Long id);

    Long getModuleIdByModuleName(Long tenantId, Long standardId, String moduleName);
}
