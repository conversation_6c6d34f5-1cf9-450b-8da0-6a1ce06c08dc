package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 保养标准明细 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-29 11:05:12
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("maintenance_standard_item")
public class MaintenanceStandardItemEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 保养明细ID
    */
    private Long id;

    /**
    * 保养标准ID
    */
    private Long maintenanceStandardId;

    /**
    * 所属模块ID
    */
    private Long maintenanceStandardModuleId;

    /**
    * 保养部位
    */
    private String position;

    /**
    * 保养标准
    */
    private String standardDesc;

    /**
    * 备注
    */
    private String remark;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 租户ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
     * 完整部位名称
     */
    private String fullBomName;

}
