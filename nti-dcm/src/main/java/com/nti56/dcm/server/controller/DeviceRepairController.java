package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.entity.InspectOrderEntity;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.DeviceRepairVo;
import com.nti56.dcm.server.service.*;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 设备维修工单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequestMapping("/deviceRepair")
@Tag(name = "设备维修工单")
@Slf4j
public class DeviceRepairController {
    @Autowired
    private IDeviceRepairService deviceRepairService;

    @Autowired
    private UserRoleAuthService userRoleAuthService;
    @PostMapping("pageForQuickCreate")
    @Operation(summary = "快捷委外分页查询")
    public R pageForQuickCreate(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairQueryDto queryDto) {
        Page<DeviceRepairQueryDto> page = queryDto.toPage(DeviceRepairQueryDto.class);
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsSuperRole(tenantIsolation);
        if (currentUserIsAdministrator) {
            queryDto.setQueryType( DictConstant.QUERY_TYPE_ALL);
        }else {
            queryDto.setQueryType( DictConstant.QUERY_TYPE_WAIT_HANDLE);
        }
        return R.result( deviceRepairService.getPage(queryDto, page, tenantIsolation));
    }
    @PostMapping("")
    @Operation(summary = "新增设备维修工单")
    public R createDeviceRepair(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                @RequestBody @Validated DeviceRepairDto dto) {
        return R.result(deviceRepairService.create(dto, tenantIsolation));
    }


    @PostMapping("/page")
    @Operation(summary = "获取设备维修工单分页")
    public R<Page<DeviceRepairDto>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairQueryDto queryDto) {
        Page<DeviceRepairQueryDto> page = queryDto.toPage(DeviceRepairQueryDto.class);
        return R.result(deviceRepairService.getPage(queryDto, page, tenantIsolation));
    }

    @PostMapping("/revoke")
    @Operation(summary = "撤销维修工单")
    public R revoke(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairProgressDto progressDto) {
        return R.result(deviceRepairService.revoke(progressDto, tenantIsolation));
    }

    @PostMapping("/dispatch")
    @Operation(summary = "派工维修工单")
    public R dispatch(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated DeviceRepairDispatchDto dto) {
        return R.result(deviceRepairService.dispatch(dto, tenantIsolation));
    }

    @PostMapping("/receive")
    @Operation(summary = "接收维修工单")
    public R dispatch(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated OrderReceiveDto dto) {
        return R.result(deviceRepairService.receiveOrder(dto, tenantIsolation));
    }

    @PostMapping("/forward")
    @Operation(summary = "转交维修工单")
    public R forward(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated DeviceRepairDispatchDto dto) {
        return R.result(deviceRepairService.forward(dto, tenantIsolation));
    }

    @PostMapping("/undo")
    @Operation(summary = "撤回维修工单")
    public R undo(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated DeviceRepairUndoDto dto) {
        return R.result(deviceRepairService.undo(dto, tenantIsolation));
    }

    @PostMapping("/reject")
    @Operation(summary = "驳回维修工单")
    public R reject(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated DeviceRepairRejectDto dto) {
        return R.result(deviceRepairService.reject(dto, tenantIsolation));
    }

    @PostMapping("/execute")
    @Operation(summary = "执行维修工单")
    public R execute(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairProgressDto progressDto) {
        return R.result(deviceRepairService.execute(progressDto, tenantIsolation));
    }
    @PostMapping("/tempExecute")
    @Operation(summary = "暂存维修工单")
    public R tempExecute(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairProgressDto progressDto) {
        return R.result(deviceRepairService.tempExecute(progressDto, tenantIsolation));
    }

    @PostMapping("/accept")
    @Operation(summary = "验收维修工单")
    public R accept(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairProgressDto progressDto) {
        return R.result(deviceRepairService.accept(progressDto, tenantIsolation));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id查找设备维修详情")
    public R<DeviceRepairVo> getDetail(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id) {
        return R.result(deviceRepairService.getDetail(id, tenantIsolation));
    }


    @GetMapping("/queryStatusCount")
    @Operation(summary = "查询客户主页 维修工单数量统计信息")
    public R queryStatusCount(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(deviceRepairService.queryStatusCount(tenantIsolation));
    }

    @GetMapping("/queryStatusCountAggregator")
    @Operation(summary = "查询集成商主页 维修工单数量统计信息")
    public R queryStatusCountAggregator(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("begin") String begin, @RequestParam("end") String end) {
        return R.result(deviceRepairService.queryStatusCountAggregator(tenantIsolation, begin, end));
    }

    @GetMapping("/querySupplierAllWaitRepairCount")
    @Operation(summary = "查询供应商主页 所有待维修的工单数量")
    public R querySupplierAllWaitRepairCount(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(deviceRepairService.querySupplierAllWaitRepairCount(tenantIsolation));
    }


    @GetMapping("/queryCustomerRepairCountTop10")
    @Operation(summary = "查询供应商主页 近30天客户报修工单数TOP10")
    public R queryCustomerRepairCountTop10(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(deviceRepairService.queryCustomerRepairCountTop10(tenantIsolation));
    }


    @GetMapping("/queryDeviceRepairCountTop10")
    @Operation(summary = "查询供应商主页 近30天设备报修次数TOP10")
    public R queryDeviceRepairCountTop10(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(deviceRepairService.queryDeviceRepairCountTop10(tenantIsolation));
    }

    @GetMapping("/queryCustomerServiceOverview")
    @Operation(summary = "查询集成商首页-客户服务概况")
    public R queryCustomerServiceOverview(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(deviceRepairService.queryCustomerServiceOverview(tenantIsolation));
    }

    @GetMapping("/queryCalendarCount")
    @Operation(summary = "查询主页 维修工单工作日历数据")
    public R queryCalendarCount(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("begin") String begin, @RequestParam("end") String end) {
        return R.result(deviceRepairService.queryCalendarCount(tenantIsolation, begin, end));
    }

    @PostMapping("/quickCreate")
    @Operation(summary = "委外快速创建")
    public R accept(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated DeviceRepairCopyDto copyDto) {
        return R.result(deviceRepairService.copy(copyDto, tenantIsolation));
    }

    /**
     * 查询设备明细工单记录数
     *
     * @param deviceId 设备id
     */
    @GetMapping("/countDeviceOrder")
    public R<DeviceCountVo> countDeviceInspectOrder(
            @RequestHeader("dcm_headers") TenantIsolation tenant,
            @RequestParam(name = "deviceId") Long deviceId
    ) {
        Result<DeviceCountVo> result = deviceRepairService.countDeviceOrder(
                tenant, deviceId
        );
        return R.result(result);
    }


    /**
     * 查询设备维保工单记录
     *
     * @param deviceId 设备id
     */
    @GetMapping("/getDeviceOrderRecord")
    public R<DeviceOrderRecordVo> getDeviceOrderRecord(
            @RequestHeader("dcm_headers") TenantIsolation tenant,
            @RequestParam(name = "deviceId") Long deviceId
    ) {
        Result<DeviceOrderRecordVo> result = deviceRepairService.getDeviceOrderRecord(
                tenant, deviceId
        );
        return R.result(result);
    }


    @Operation(summary = "新增资料附件")
    @PostMapping(value = "/saveDataAttachment", name = "新增资料附件")
    public R saveDataAttachment(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,@Validated @RequestBody DeviceRepairSaveFileDto dto) {
            return R.result(deviceRepairService.saveDataAttachment(tenantIsolation, dto));

    }

    @Autowired
    private MaintenanceOrderService maintenanceOrderService;

    @Autowired
    private IDeviceRepairReportService deviceRepairReportService;

    @Autowired
    private InspectOrderService inspectOrderService;

    @GetMapping("monitor")
    public R monitor() {
        deviceRepairService.orderTimeoutMonitor();
        deviceRepairReportService.reportTimeoutMonitor();
        maintenanceOrderService.orderTimeoutMonitor();
        inspectOrderService.orderTimeoutMonitor();
        return R.result(Result.ok());
    }
}
