package com.nti56.dcm.server.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nti56.dcm.server.entity.WorkloadEntity;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.nlink.common.util.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceRepairDto extends PageParam implements Serializable {


    /**
     * ID
     */
    private Long id;

    /**
     * 维修设备id
     */
    @NotNull(message = "请传入要维修的设备id")
    private Long deviceId;

    /**
     * 设备类型id
     */
    private Long deviceTypeId;

    /**
     * 维修设备名称
     */
    private String deviceName;

    /**
     * 维修设备类型名称
     */
    private String deviceTypeName;


    /**
     * 报修设备部位id
     */
    private Long repairDeviceBomId;
    /**
     * 报修设备部位名称
     */
    private String repairDeviceBomName;

    /**
     * 执行设备部位id
     */
    private Long executeDeviceBomId;
    /**
     * 执行设备部位名称
     */
    private String executeDeviceBomName;
    /**
     * 维修设备编号
     */
    private String deviceNo;

    /**
     * 维修工单编号
     */
    private String orderNumber;
    /**
     * 故障时间
     */
    @NotNull(message = "故障时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime faultTime;

    /**
     * 故障等级
     */
    @NotNull(message = "故障等级不能为空")
    private Integer faultLevel;

    /**
     * 故障描述
     */
    @NotBlank(message = "故障描述不能为空")
    @Size(max = 512, message = "故障描述最大不能超过512个字符！")
    private String faultDescribe;

    /**
     * 附图
     */
    private List<FileVo> faultImages;
    /**
     * 视频
     */
    private List<FileVo> faultVideos;

    /**
     * 委外状态（0-内部，1-委外）
     */
    private Integer outsourceStatus;


    /**
     * 集成商委外供应商(0-否，1-是)
     */
    private Integer aggregatorOutsource;
    /**
     *委外执行工单时，支持的供应商id
     */
    private Long supportSupplierId;
    /**
     * 委外供应商id
     */
    private Long outsourceSupplierId;

    /**
     * 供应商类型，2 为数据型供应商
     */
    private Integer outsourceSupplierType;
    /**
     * 委外客户类型，1-租户型，2 为数据型供应商
     */
    private Integer outsourceCustomerType;

    /**
     * 委外供应商名称
     */
    private String outsourceSupplierName;
    /**
     * 来源客户
     */
    private String customerName;

    /**
     * 委外租户id
     */
    private Long outsourceTenantId;

    /**
     * 快速委外关联原始单据id
     */
    private Long outsourceOriginId;


    /**
     * 快速委外关联原始单据工单编号
     */
    private String outsourceOriginOrderNumber;
    /**
     * 快速委外关联原始单据工客户id
     */
    private Long outsourceOriginCustomerId;
    /**
     * 快速委外关联原始单据客户名称
     */
    private String outsourceOriginCustomerName;
    /**
     * 维修状态
     * 参考OrderStatusEnum
     */
    private Integer status;

    private String statusName;
    /**
     * 接收人id
     */
    private Long receiveUserId;
    /**
     * 进度执行人id
     */
    private Long executeUserId;
    /**
     * 进度执行人名称
     */
    private String executeUserName;
    /**
     * 故障类型
     */
    private Long faultType;

    /**
     * 故障原因
     */
    private String faultReason;

    /**
     * 处理过程
     */
    private String repairProcess;

    /**
     * 维修开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repairStartTime;

    /**
     * 维修结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repairEndTime;

    /**
     * 维修耗时（时分格式）
     */
    private String repairUseTime;

    /**
     * 是否加入经验库(0-否，1-是)
     */
    private Integer repairSaveExp;

    /**
     * 供应商内部设备类型ID
     */
    private Long innerDeviceTypeId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 修改人ID
     */
    private Long updatorId;

    /**
     * 修改人
     */
    private String updator;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 所属身份 1-供应商 2-客户
     */
    private Integer idType;

    /**
     * 外委工单创建来源 1-供应商/集成商 2-客户
     */
    private Integer createSource;
    /**
     * 是否超时 1-正常 2-超时
     */
    private Integer timeout;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 人员工作量
     */
    private List<WorkloadEntity> workloadList;
    /**
     * 操作权限 参考OrderOperationEnum
     */
    private List<String> operations = new ArrayList<>();

    /**
     * 是否是超级角色（管理员或者班组长）
     */
    private Boolean isSuperRole;

    /**
     * 告警记录ID
     */
    private Long alarmRecordId;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}