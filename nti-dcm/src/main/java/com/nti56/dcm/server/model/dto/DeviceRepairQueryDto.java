package com.nti56.dcm.server.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.nlink.common.util.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceRepairQueryDto extends PageParam implements Serializable {


    /**
     * 维修设备名称
     */
    @Schema(description = "维修设备名称")
    private String deviceName;

    /**
     * 维修设备id
     */
    @Schema(description = "维修设备id")
    private Long deviceId;

    /**
     * 维修设备编号
     */
    @Schema(description = "维修设备编号")
    private String deviceNo;

    /**
     * 维修工单编号
     */
    @Schema(description = "维修工单编号")
    private String orderNumber;
    /**
     * 维修状态
     * 参考OrderStatusEnum
     */
    @Schema(description = "维修状态   1-待分派，2-待接收，3-执行中，4-待验收，5-验收通过，6-验收不通过，7-已撤销")
    private Integer status;
    /**
     * 维修状态集合  快捷外委查询时使用
     * 参考OrderStatusEnum
     */
    @Schema(description = "多选维修状态查询，值参考维修状态")
    private List<Integer> statusList;

    /**
     * 工单执行人id
     */
    @Schema(description = "工单执行人id")
    private Long executeUserId;
    /**
     * 是否拥有分配权限
     */
    @Schema(description = "是否拥有派工权限")
    private Boolean haveDispatchAuth;
    /**
     * 是否拥有执行权限
     */
    @Schema(description = "是否拥有执行权限")
    private Boolean haveExecuteAuth;
    /**
     * 是否拥有验收权限
     */
    @Schema(description = "是否拥有验收权限")
    private Boolean haveAcceptAuth;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 身份
     */
    @Schema(description = "身份  1-供应商，2-客户")
    private Integer idType;


    /**
     * 外委状态
     */
    @Schema(description = "外委状态  0-内部工单，1-外委工单")
    private Integer outsourceStatus;
    /**
     * 开始时间
     */
    @Schema(description = "工单创建查询开始时间")
    private LocalDateTime startDate;
    /**
     * 结束时间
     */
    @Schema(description = "工单创建查询结束时间")
    private LocalDateTime endDate;

    /**
     * 维修结束时间开始
     */
    private LocalDateTime repairFinishTimeBegin;

    /**
     * 维修结束时间结束
     */
    private LocalDateTime repairFinishTimeEnd;

    /**
     * 最后更新开始时间
     */
    @Schema(description = "工单最后更新时间 查询范围——开始时间")
    private LocalDateTime lastModifyStartDate;
    /**
     * 最后更新结束时间
     */
    @Schema(description = "工单最后更新时间 查询范围——结束时间")
    private LocalDateTime lastModifyEndDate;
    /**
     * 界面查询类型
     */
    @Schema(description = "界面查询类型")
    private String queryType;

    /**
     * 委外供应商id
     */
    @Schema(description = "委外供应商id")
    private Long outsourceSupplierId;
    /**
     * 待处理流程实例id列表
     */
    private List<String> todoProcessInstanceIdList;
    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerId;
    /**
     * 是否超时 1-正常 2-超时
     */
    private Integer timeout;
    /**
     * 快速委外关联原始单据工单编号
     */
    @Schema(description = "快速委外关联原始单据工单编号")
    private String outsourceOriginOrderNumber;
    /**
     * 快速委外关联原始单据工客户id
     */
    @Schema(description = "快速委外关联原始单据工客户id")
    private Long outsourceOriginCustomerId;
}