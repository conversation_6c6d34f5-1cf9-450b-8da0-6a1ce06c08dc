package com.nti56.dcm.server.model.dto;

import com.nti56.dcm.server.model.vo.FileVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/2/29 11:46<br/>
 * @since JDK 1.8
 */
@Data
public class MaintenanceStandardItemDto {

    /**
     * 保养明细ID
     */
    private Long id;

    /**
     * 保养标准ID
     */
    @NotNull(message = "保养标准ID不能为空")
    private Long maintenanceStandardId;

    /**
     * 保养标准模块ID
     */
    private Long maintenanceStandardModuleId;

    /**
     * 保养部位
     */
    @NotBlank(message = "项目名称不能为空")
    private String position;

    /**
     * 保养标准
     */
    //@NotBlank(message = "保养标准不能为空")
    private String standardDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附图
     */
    private List<FileVo> fileList;


    /**
     * 完整部位名称
     */
    private String fullBomName;

}
