package com.nti56.dcm.server.model.dto;

import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.nti56.common.util.StringJsonDeserializer;
import com.nti56.dcm.server.model.vo.FileVo;

import lombok.Data;

@Data
public class InspectResultItemDto {

    /**
     * 检查结果项id
     */
    private Long id;

    /**
    * 所属检查结果id
    */
    private Long inspectResultId;

    /**
    * 检查项目id
    */
    private Long inspectStandardItemId;

    /**
     * 检查图片列表
     */
    private List<FileVo> fileList;

    /**
    * 检查值，选项名（多选的话逗号分隔）、数值、文本
    */
    @JsonDeserialize(using = StringJsonDeserializer.class)
    private String resultValue;

    /**
    * 是否异常，1-异常，0-正常
    */
    private Integer isError;

    /**
     * 备注
     */
    private String remark;

}
