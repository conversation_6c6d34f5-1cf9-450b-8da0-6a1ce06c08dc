package com.nti56.dcm.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.dcm.server.entity.MaintenanceStandardModuleEntity;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import java.util.List;

/**
 * <p>
 * 保养标准模块表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-07-04 09:39:39
 * @since JDK 1.8
 */
public interface IMaintenanceStandardModuleService extends IService<MaintenanceStandardModuleEntity> {

    Result<MaintenanceStandardModuleEntity> create(TenantIsolation tenantIsolation, MaintenanceStandardModuleEntity entity);

    Result<List<MaintenanceStandardModuleEntity>> listByStandardId(TenantIsolation tenantIsolation, Long standardId);

    Result<Void> update(TenantIsolation tenantIsolation, MaintenanceStandardModuleEntity entity);

    Result<Void> deleteById(Long id);

    Long getModuleIdByModuleName(Long tenantId, Long standardId, String moduleName);

}
