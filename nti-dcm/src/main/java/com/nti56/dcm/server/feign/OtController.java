package com.nti56.dcm.server.feign;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.dcm.server.model.vo.DeviceStateVo;
import com.nti56.dcm.server.model.vo.PropertyValueWithTime;
import com.nti56.dcm.server.model.vo.StateValueWithTime;
import com.nti56.dcm.server.model.vo.ThingModelVo;
import com.nti56.nlink.common.util.Result;

import feign.Body;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

public interface OtController {

    @RequestLine("POST /device/debug/property-of-device-by-names?property={property}")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    Result<Map<String, Integer>> propertyOfDeviceByNames(@Param("property") String property,
                                @Param("otHeaders") String otHeaders,
                                @Param("body") String body);

    @RequestLine("POST /device/debug/property-of-device-by-names-without-tenant?property={property}")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    Result<Map<String, Integer>> propertyOfDeviceByNamesWithoutTenant(@Param("property") String property,
                                @Param("otHeaders") String otHeaders,
                                @Param("body") String body);

    @RequestLine("POST /device/debug/property-and-time-of-device-by-names?property={property}")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    Result<Map<String,StateValueWithTime>> propertyAndTimeOfDeviceByNames(@Param("property") String property,
                                @Param("otHeaders") String otHeaders,
                                @Param("body") String body);

    @RequestLine("POST /device/debug/property-and-time-of-device-by-names-without-tenant?property={property}")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    Result<Map<String,StateValueWithTime>> propertyAndTimeOfDeviceByNamesWithoutTenant(@Param("property") String property,
                                @Param("otHeaders") String otHeaders,
                                @Param("body") String body);

    @RequestLine("GET /device/debug/properties-of-device-by-name?deviceName={deviceName}&properties={properties}")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    Result<Map<String, Object>> propertiesOfDeviceByName(@Param("deviceName") String deviceName,
                                @Param("otHeaders") String otHeaders,
                                @Param("properties") String properties);
    

    @RequestLine("GET /device/debug/properties-and-times-of-device-by-name?deviceName={deviceName}&properties={properties}")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    Result<Map<String, PropertyValueWithTime>> propertiesAndTimesOfDeviceByName(@Param("deviceName") String deviceName,
                                @Param("otHeaders") String otHeaders,
                                @Param("properties") String properties);
    
    @RequestLine("POST /device/debug/device-names-to-ids")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    Result<List<String>> deviceNamesToIds(@Param("otHeaders") String otHeaders, 
                                @Param("body")String body);
    
    
    @RequestLine("POST /device/debug/device-id-name-map-by-names")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    Result<Map<Long, String>> deviceIdNameMapByNames(@Param("otHeaders") String otHeaders, 
                                @Param("body")String body);
    
    @RequestLine("POST /it/device/do-service-by-name")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    ITResult<Object> doServiceByName(@Param("otHeaders") String otHeaders,
                               @Param("body") String body);
    
    @RequestLine("POST /it/device/do-service-by-name-without-context")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    @Body("{body}")
    ITResult<Object> doServiceByNameWithoutContext(@Param("otHeaders") String otHeaders,
                               @Param("body") String body);

    
    @RequestLine("GET /device/debug/page/state-of-device?deviceName={deviceName}&size={size}&current={current}&begin={begin}&end={end}")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}"})
    Result<Page<DeviceStateVo>> pageStateOfDevice(@Param("otHeaders") String otHeaders, 
        @Param("deviceName") String deviceName, 
        @Param("size") Integer size, 
        @Param("current") Integer current, 
        @Param("begin") String begin, 
        @Param("end") String end);


    @RequestLine("POST /app/init")
    @Headers({"Content-Type: application/json;charset=UTF-8","tenantId: {tenantId}","ot_headers: {otHeaders}","loginToken: {loginToken}"})
    Result<Boolean> initData(@Param("tenantId") Long tenantId,@Param("otHeaders") String otHeaders, @Param("loginToken") String loginToken);

    @RequestLine("POST /it/device/endFault")
    @Headers({"Content-Type: application/json;charset=UTF-8", "tenantId: {tenantId}","ot_headers: {otHeaders}"})
    @Body("{body}")
    ITResult<Object> endFault(@Param("tenantId") Long tenantId, @Param("otHeaders") String otHeaders, @Param("body")String body);

    @RequestLine("POST /it/list/model/byDeviceName")
    @Headers({"Content-Type: application/json;charset=UTF-8","tenantId: {tenantId}"})
    ITResult<Object> listModelByDeviceName(@Param("tenantId") Long tenantId, @RequestBody List<String> deviceNames);

}
