package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.InspectStandardService;
import com.nti56.dcm.server.entity.InspectStandardEntity;
import com.nti56.dcm.server.model.dto.InspectStandardDto;
import com.nti56.dcm.server.model.dto.StandardCopyDto;
import com.nti56.dcm.server.model.dto.StandardImportDto;
import com.nti56.dcm.server.model.dto.StandardOverrideDto;
import com.nti56.dcm.server.model.vo.ImportFailInfo;
import com.nti56.dcm.server.model.vo.InspectStandardVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;


/**
 * 点巡检标准表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/inspectStandard")
@Slf4j
public class InspectStandardController {

    @Autowired
    private InspectStandardService service;

    /**
     * 创建点巡检标准
     * 
     */
    @PostMapping("/create")
    public R<InspectStandardEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectStandardEntity entity){
        Result<InspectStandardEntity> result = service.create(tenant.getTenantId(), tenant.getIdType(), entity);
        return R.result(result);
    }

    /**
     * 获取点巡检标准分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<InspectStandardVo>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,InspectStandardDto dto){
        Page<InspectStandardEntity> page = pageParam.toPage(InspectStandardEntity.class);
        Result<Page<InspectStandardVo>> result = service.getPage(tenant.getTenantId(), tenant.getIdType(), dto,page);
        return R.result(result);
    }

    // /**
    //  * 获取点巡检标准列表
    //  */
    // @GetMapping("/list")
    // public R<List<InspectStandardEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, InspectStandardEntity entity){
    //     Result<List<InspectStandardEntity>> result = service.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    /**
     * 更新点巡检标准
     */
    @PutMapping("/update")
    public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectStandardEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, InspectStandardEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
        }
        Result<Void> result = service.update(tenant.getTenantId(), tenant.getIdType(), entity);
        return R.result(result);
    }

    /**
     * 删除点巡检标准
     * @param entityId 对象id
     */
    @DeleteMapping("/{entityId}")
    public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    /**
     * 获取点巡检标准
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<InspectStandardEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<InspectStandardEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
    /**
     * 复制巡检标准
     */
    @PostMapping("/copy")
    public R<Void> copy(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody StandardCopyDto dto){
        Result<Void> result = service.copy(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }

    /**
     * 导出标准
     * @throws IOException 
     */
    @GetMapping("/export")
    public void exportStandard(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(name = "standardId") Long standardId, 
        HttpServletResponse response
    ) throws IOException{
        service.exportStandard(tenant.getTenantId(), standardId, response);
    }

    /**
     * 导入标准
     */
    @PostMapping("/import")
    public Result<List<ImportFailInfo>> importStandard(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        StandardImportDto dto, 
        @RequestParam("file") MultipartFile file
    ) throws IOException{
        return service.importStandard(tenant.getTenantId(), tenant.getIdType(), dto, file);
    }

    /**
     * 导入标准
     */
    @PostMapping("/override")
    public Result<List<ImportFailInfo>> overrideStandard(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        StandardOverrideDto dto, 
        @RequestParam("file") MultipartFile file
    ) throws IOException{
        return service.overrideStandard(tenant.getTenantId(), tenant.getIdType(), dto, file);
    }

    /**
     * 导出标准模板
     * @throws IOException 
     */
    @GetMapping("/download-template")
    public void downloadTemplate(HttpServletResponse response, Integer type) throws IOException {
        service.downloadTemplate(response, type);
    }


}
