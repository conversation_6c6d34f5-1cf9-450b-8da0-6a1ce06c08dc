package com.nti56.dcm.server.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.CustomerRelationEntity;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;

import cn.hutool.core.util.RandomUtil;

import com.nti56.dcm.server.entity.FileEntity;
import com.nti56.dcm.server.entity.TenantInfoEntity;
import com.nti56.dcm.server.mapper.TenantInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Nullable;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 租户信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-05-20 13:43:15
 * @since JDK 1.8
 */
@Service
@Slf4j
public class TenantInfoService {

    @Autowired
    private TenantInfoMapper tenantInfoMapper;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private UserCenterService userCenterService;

    public Result<TenantInfoEntity> save(Long tenantId, TenantInfoEntity entity) {
        entity.setTenantId(tenantId);
        if (tenantInfoMapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    public Result<Page<TenantInfoEntity>> getPage(Long tenantId,  TenantInfoEntity entity, Page<TenantInfoEntity> page) {
        Page<TenantInfoEntity> list = tenantInfoMapper.selectPage(page, new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    public Result<List<TenantInfoEntity>> list(Long tenantId, TenantInfoEntity entity) {
        List<TenantInfoEntity> list = tenantInfoMapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    public Result<Void> update(Long tenantId, TenantInfoEntity entity) {
        entity.setTenantId(tenantId);
        if (tenantInfoMapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    public Result<Void> deleteById(Long tenantId, Long entityId) {
        if (tenantInfoMapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    public Result<TenantInfoEntity> getById(Long tenantId, Long entityId) {
        TenantInfoEntity entity = tenantInfoMapper.selectById(entityId);
        return Result.ok(entity);
    }


    public Map<Long,TenantInfoEntity> getTenantMap(){
        List<TenantInfoEntity> allTenant = tenantInfoMapper.listAllTenant();
        Map<Long, TenantInfoEntity> collect = allTenant.stream().filter(i->StrUtil.isNotBlank(i.getTenantName())).collect(Collectors.toMap(TenantInfoEntity::getId, i->i));
        return collect;
    }


    public Integer getTenantIsPmoTenant(TenantIsolation tenantIsolation) {
        if (IdTypeEnum.SUPPLIER.getValue().equals(tenantIsolation.getIdType())){
            return PmoTenantEnum.NOT_PMO_TENANT.getValue();
        }
        TenantInfoEntity entity = tenantInfoMapper.selectById(tenantIsolation.getTenantId());
        return entity!=null ? entity.getIsPmoTenant() : PmoTenantEnum.NOT_PMO_TENANT.getValue();
    }

    public Result initTenantCode(Long tenantId,String tenantName,String orgName) {
        Integer isPmoTenant = PmoTenantEnum.NOT_PMO_TENANT.getValue();
        Long pmoTenantId = null;
        if (StrUtil.isNotBlank(orgName)){
            if (!ObjUtil.equals(DictConstant.DEFAULT_TENANT_ID,tenantId)){
                Long customerId = getPmoTenantCustomerId(orgName);
                if (ObjUtil.isNotNull(customerId)){
                    isPmoTenant = PmoTenantEnum.IS_PMO_TENANT.getValue();
                    pmoTenantId = customerId;
                }
            }
        }

        TenantInfoEntity info = tenantInfoMapper.selectById(tenantId);
        if (info != null){
            return Result.error();
        }

        // 根据租户名称查询是否存在已经注销的租户
        List<TenantInfoEntity> list = tenantInfoMapper.selectList(Wrappers.<TenantInfoEntity>lambdaQuery()
                .eq(TenantInfoEntity::getTenantName, tenantName)
                .eq(TenantInfoEntity::getDeleted, YesNoEnum.NO.getValue()));
        if (CollectionUtil.isNotEmpty(list)){
            tenantInfoMapper.deleteBatchIds(list);
        }

        for(int i=0;i<20;i++){
            String randomStr4 = RandomUtil.randomStringUpper(4);
            QueryWrapper<TenantInfoEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", randomStr4);
            TenantInfoEntity same = tenantInfoMapper.selectOne(queryWrapper);
            if(same == null){
                // 没有相同的
                TenantInfoEntity entity = new TenantInfoEntity();
                entity.setId(tenantId);
                entity.setTenantName(tenantName);
                entity.setIsPmoTenant(isPmoTenant);
                entity.setPmoTenantId(pmoTenantId);
                entity.setCode(randomStr4);
                tenantInfoMapper.insert(entity);
                if (ObjUtil.equals(isPmoTenant,PmoTenantEnum.IS_PMO_TENANT.getValue())){
                    customerRelationService.lambdaUpdate().set(CustomerRelationEntity::getType,SupplierTypeEnum.TENANT.getValue())
                            .eq(CustomerRelationEntity::getCustomerId,pmoTenantId)
                            .eq(CustomerRelationEntity::getSupplierId,DictConstant.DEFAULT_TENANT_ID)
                            .eq(CustomerRelationEntity::getIdType,IdTypeEnum.SUPPLIER.getValue())
                            .eq(CustomerRelationEntity::getStatus, RelationStatusEnum.APPROVE.getValue())
                            .update();
                    CustomerRelationEntity add = getCustomerRelationEntity(tenantId, orgName, pmoTenantId);
                    customerRelationService.save(add);
                }
                return Result.ok();
            }
            // 相同，继续尝试
        }
        return Result.ok();
    }

    private static CustomerRelationEntity getCustomerRelationEntity(Long tenantId, String orgName, Long pmoTenantId) {
        CustomerRelationEntity add = new CustomerRelationEntity();
        add.setSupplierId(DictConstant.DEFAULT_TENANT_ID);
        add.setSupplierName("今天国际");
        add.setCustomerId(pmoTenantId);
        add.setCustomerName(orgName);
        add.setStatus(RelationStatusEnum.APPROVE.getValue());
        add.setType(SupplierTypeEnum.TENANT.getValue());
        add.setIdType(IdTypeEnum.CUSTOMER.getValue());
        add.setTenantId(tenantId);
        return add;
    }

    /**
     * 判断租户的名称是否为今天国际的客户，如果是返回pmo的客户id，否则返回null
     * @param tenantName
     * @return
     */
    public Long getPmoTenantCustomerId(String tenantName){
        List<CustomerRelationEntity> list = customerRelationService.list(Wrappers.<CustomerRelationEntity>lambdaQuery()
                .eq(CustomerRelationEntity::getCustomerName, tenantName)
                .eq(CustomerRelationEntity::getSupplierId, DictConstant.DEFAULT_TENANT_ID)
                .eq(CustomerRelationEntity::getIdType, IdTypeEnum.SUPPLIER.getValue())
                .eq(CustomerRelationEntity::getType, SupplierTypeEnum.SELF_DEFINING.getValue()));
        if(CollUtil.isNotEmpty(list)){
            return list.get(0).getCustomerId();
        }
        return null;
    }


    
    public void checkTenantBind(Long tenantId) {
        if (ObjUtil.equals(DictConstant.DEFAULT_TENANT_ID,tenantId)){
            return;
        }
        String tenantName = userCenterService.getOrgNameByTenantId(tenantId);
        Integer isPmoTenant = PmoTenantEnum.NOT_PMO_TENANT.getValue();
        Long pmoTenantId = null;
        if (StrUtil.isNotBlank(tenantName)) {
            CustomerRelationEntity customerRelationEntity = new CustomerRelationEntity();
            // 后续租户认证会将pmo租户类型从自定义升级为租户，不再根据供应商类型来判断
//            customerRelationEntity.setType( SupplierTypeEnum.SELF_DEFINING.getValue());
            customerRelationEntity.setIdType(IdTypeEnum.SUPPLIER.getValue());
            customerRelationEntity.setSupplierId(DictConstant.DEFAULT_TENANT_ID);
            customerRelationEntity.setCustomerName(tenantName);
            List<CustomerRelationEntity> list = customerRelationService.listWithOutDataPermission(customerRelationEntity);
            if (CollUtil.isNotEmpty(list)) {
                isPmoTenant = PmoTenantEnum.IS_PMO_TENANT.getValue();
                if (list.size()>1){
                    pmoTenantId = list.stream().filter(i->i.getType().equals(SupplierTypeEnum.SELF_DEFINING.getValue())).map(CustomerRelationEntity::getCustomerId).findFirst().orElse(null);
                    if (pmoTenantId == null){
                        pmoTenantId = list.get(0).getCustomerId();
                    }
                }else {
                    pmoTenantId = list.get(0).getCustomerId();
                }
            }
        }
        TenantInfoEntity info = tenantInfoMapper.selectById(tenantId);
        if (info != null) {
            Long oldPmoTenantId = info.getPmoTenantId();
            boolean hasChange = false;
            if (!ObjUtil.equals(info.getIsPmoTenant(), isPmoTenant)&& !ObjUtil.equals(info.getPmoTenantId(),pmoTenantId)) {
                hasChange = true;
                info.setIsPmoTenant(isPmoTenant);
                info.setPmoTenantId(pmoTenantId);
            }
            if (pmoTenantId != null && !ObjUtil.equals(info.getPmoTenantId(), pmoTenantId)) {
                hasChange = true;
                info.setIsPmoTenant(PmoTenantEnum.IS_PMO_TENANT.getValue());
                info.setPmoTenantId(pmoTenantId);
            }
            if (hasChange) {
                tenantInfoMapper.updateTenantInfo(info.getId(),info.getPmoTenantId(),info.getIsPmoTenant());
                if (info.getIsPmoTenant().equals(PmoTenantEnum.IS_PMO_TENANT.getValue()) && info.getPmoTenantId()!=null){
                    tenantInfoMapper.clearExistTenantInfo(info.getId(),info.getPmoTenantId());
                    // 新绑定了一个pmo租户  将客户关系中自定义的客户升级为租户类型
                    customerRelationService.lambdaUpdate().set(CustomerRelationEntity::getType,SupplierTypeEnum.TENANT.getValue())
                            .eq(CustomerRelationEntity::getCustomerId,pmoTenantId)
                            .eq(CustomerRelationEntity::getSupplierId,DictConstant.DEFAULT_TENANT_ID)
                            .eq(CustomerRelationEntity::getIdType,IdTypeEnum.SUPPLIER.getValue())
                            .eq(CustomerRelationEntity::getStatus, RelationStatusEnum.APPROVE.getValue())
                            .update();
                    // 存在旧的绑定，将客户关系解绑后再新增新的
                    if (oldPmoTenantId!=null){
                        customerRelationService.lambdaUpdate().set(CustomerRelationEntity::getStatus,RelationStatusEnum.DISSOLVE.getValue())
                                .eq(CustomerRelationEntity::getCustomerId,oldPmoTenantId)
                                .eq(CustomerRelationEntity::getSupplierId,DictConstant.DEFAULT_TENANT_ID)
                                .eq(CustomerRelationEntity::getIdType,IdTypeEnum.CUSTOMER.getValue())
                                .eq(CustomerRelationEntity::getStatus, RelationStatusEnum.APPROVE.getValue())
                                .update();
                    }
                    CustomerRelationEntity add = getCustomerRelationEntity(tenantId, tenantName, pmoTenantId);
                    customerRelationService.save(add);
                }else {
                    if (oldPmoTenantId!=null){
                        // 解除绑定时，将客户关系降级为自定义类型。
                        customerRelationService.lambdaUpdate().set(CustomerRelationEntity::getType,SupplierTypeEnum.SELF_DEFINING.getValue())
                               .eq(CustomerRelationEntity::getCustomerId,oldPmoTenantId)
                               .eq(CustomerRelationEntity::getSupplierId,DictConstant.DEFAULT_TENANT_ID)
                              .eq(CustomerRelationEntity::getIdType,IdTypeEnum.SUPPLIER.getValue())
                              .eq(CustomerRelationEntity::getStatus, RelationStatusEnum.APPROVE.getValue())
                              .update();

                        customerRelationService.lambdaUpdate().set(CustomerRelationEntity::getStatus,RelationStatusEnum.DISSOLVE.getValue())
                                .eq(CustomerRelationEntity::getCustomerId,oldPmoTenantId)
                                .eq(CustomerRelationEntity::getSupplierId,DictConstant.DEFAULT_TENANT_ID)
                                .eq(CustomerRelationEntity::getIdType,IdTypeEnum.CUSTOMER.getValue())
                                .eq(CustomerRelationEntity::getStatus, RelationStatusEnum.APPROVE.getValue())
                                .update();
                    }
                }
            }
        }
    }

    public TenantInfoEntity getByPmoTenantId(Long pmoTenantId){
        QueryWrapper<TenantInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pmo_tenant_id", pmoTenantId);
        queryWrapper.eq("is_pmo_tenant", PmoTenantEnum.IS_PMO_TENANT.getValue());
        queryWrapper.eq("deleted", 0);
        queryWrapper.last("limit 1");
        return tenantInfoMapper.selectOne(queryWrapper);
    }
    public List<TenantInfoEntity> listByPmoTenantIdList(List<Long> pmoTenantIdList){
        QueryWrapper<TenantInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("pmo_tenant_id", pmoTenantIdList);
        queryWrapper.eq("is_pmo_tenant", PmoTenantEnum.IS_PMO_TENANT.getValue());
        queryWrapper.eq("deleted", 0);
        return tenantInfoMapper.selectList(queryWrapper);
    }
    public List<TenantInfoEntity> listByTenantIdList(List<Long> tenantIdList){
        QueryWrapper<TenantInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("tenant_id", tenantIdList);
        queryWrapper.eq("is_pmo_tenant", PmoTenantEnum.IS_PMO_TENANT.getValue());
        queryWrapper.eq("deleted", 0);
        return tenantInfoMapper.selectList(queryWrapper);
    }

}
