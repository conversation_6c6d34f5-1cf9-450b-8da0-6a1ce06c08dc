package com.nti56.dcm.server.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.dcm.server.domain.InspectOrder;
import com.nti56.dcm.server.domain.InspectPlan;
import com.nti56.dcm.server.domain.OrderPlanner;
import com.nti56.dcm.server.entity.InspectOrderEntity;
import com.nti56.dcm.server.entity.InspectOrderProgressEntity;
import com.nti56.dcm.server.entity.InspectPlanDeviceEntity;
import com.nti56.dcm.server.entity.InspectPlanDeviceStandardEntity;
import com.nti56.dcm.server.entity.InspectPlanEntity;
import com.nti56.dcm.server.entity.InspectResultEntity;
import com.nti56.dcm.server.entity.InspectResultItemEntity;
import com.nti56.dcm.server.entity.InspectResultStandardEntity;
import com.nti56.dcm.server.entity.InspectStandardItemEntity;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.mapper.InspectOrderMapper;
import com.nti56.dcm.server.mapper.InspectOrderProgressMapper;
import com.nti56.dcm.server.mapper.InspectPlanDeviceMapper;
import com.nti56.dcm.server.mapper.InspectPlanDeviceStandardMapper;
import com.nti56.dcm.server.mapper.InspectPlanMapper;
import com.nti56.dcm.server.mapper.InspectResultItemMapper;
import com.nti56.dcm.server.mapper.InspectResultMapper;
import com.nti56.dcm.server.mapper.InspectResultStandardMapper;
import com.nti56.dcm.server.mapper.InspectStandardItemMapper;
import com.nti56.dcm.server.model.vo.CountVo;
import com.nti56.dcm.server.model.vo.InspectPlanDeviceStandardVo;
import com.nti56.dcm.server.model.vo.InspectPlanDeviceVo;
import com.nti56.dcm.server.model.vo.InspectPlanVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 点巡检计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Service
@Slf4j
public class InspectPlanService {

    @Autowired
    private InspectPlanMapper inspectPlanMapper;

    @Autowired
    private InspectPlanDeviceMapper inspectPlanDeviceMapper;

    @Autowired
    private InspectPlanDeviceStandardMapper inspectPlanDeviceStandardMapper;

    @Autowired
    private InspectOrderMapper inspectOrderMapper;

    @Autowired
    private InspectResultMapper inspectResultMapper;

    @Autowired
    private InspectResultItemMapper inspectResultItemMapper;

    @Autowired
    private InspectOrderProgressMapper inspectOrderProgressMapper;

    @Autowired
    private InspectStandardItemMapper inspectStandardItemMapper;
    
    @Autowired
    private SerialNumberService serialNumberService;
    
    @Autowired
    private InspectResultStandardMapper inspectResultStandardMapper;

    @Autowired
    private DeviceEmpowerService deviceEmpowerService;

    @Autowired
    private UserRoleAuthService userRoleAuthService;

    @Autowired
    private OrderProcessService orderProcessService;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private IMessageService messageService;

    @Autowired
    private AsyncSendNoticeService asyncSendNoticeService;

    private Set<Integer> parsePeriodInspectDay(String periodInspectDayStr){
        Set<Integer> periodInspectDaySet = new HashSet<>();
        if(periodInspectDayStr != null && !"".equals(periodInspectDayStr)){
            String[] split = periodInspectDayStr.split(",");
            for(String s:split){
                periodInspectDaySet.add(Integer.valueOf(s));
            }
        }
        return periodInspectDaySet;
    }

    @Transactional
    public void createOrderDetails(
        Long tenantId,
        Integer idType,
        LocalDateTime planInspectTime, 
        InspectPlanEntity planEntity, 
        LocalDateTime now, 
        Long userId, 
        String userName
    ){
        // 插入工单
        InspectOrderEntity orderEntity = new InspectOrderEntity();
        orderEntity.setOrderNumber("tmp");
        orderEntity.setTenantId(tenantId);
        orderEntity.setInspectPlanId(planEntity.getId());
        orderEntity.setStatus(OrderStatusEnum.WAIT_DISPATCH.getValue());
        orderEntity.setPlanInspectTime(planInspectTime);
        orderEntity.setCustomerId(planEntity.getCustomerId());
        orderEntity.setCustomerName(planEntity.getCustomerName());
        orderEntity.setInspectType(planEntity.getInspectType());
        orderEntity.setLevel(planEntity.getLevel());
        orderEntity.setCreateSource(planEntity.getCreateSource());
        orderEntity.setCreatorId(userId);
        orderEntity.setCreator(userName);
        orderEntity.setTimeout(TimeoutEnum.NORMAL.getValue());

        Integer outsource = planEntity.getOutsource();
        orderEntity.setOutsource(outsource);
        if(OutsourceEnum.YES.getValue().equals(outsource)){
            orderEntity.setSupplierId(planEntity.getSupplierId());
            orderEntity.setSupplierName(planEntity.getSupplierName());
            Integer outsourceSupplierType = planEntity.getOutsourceSupplierType();
            if(outsourceSupplierType == null){
                outsourceSupplierType = SupplierTypeEnum.TENANT.getValue();
            }
            orderEntity.setOutsourceSupplierType(outsourceSupplierType);
            Integer outsourceCustomerType = planEntity.getOutsourceCustomerType();
            if(outsourceCustomerType == null){
                outsourceCustomerType = SupplierTypeEnum.TENANT.getValue();
            }
            orderEntity.setOutsourceCustomerType(outsourceCustomerType);
        }
        inspectOrderMapper.insert(orderEntity);
        
        Long inspectOrderId = orderEntity.getId();
        Boolean isDataRecord = false;
        if(IdTypeEnum.CUSTOMER.getValue().equals(idType)){
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                Result<Integer> tenantTypeResult = customerRelationService.getTenantType(orderEntity.getCustomerId(), orderEntity.getSupplierId(), idType);
                if(!tenantTypeResult.getSignal()){
                    throw new BizException("获取供应商类型失败" + tenantTypeResult.getMessage());
                }
                Integer tenantType = tenantTypeResult.getResult();
                isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType);
            }
        }else{
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                Result<Integer> tenantTypeResult = customerRelationService.getTenantType(orderEntity.getCustomerId(), orderEntity.getSupplierId(), idType);
                if(!tenantTypeResult.getSignal()){
                    throw new BizException("获取供应商类型失败" + tenantTypeResult.getMessage());
                }
                Integer tenantType = tenantTypeResult.getResult();
                isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType); 
            }
        }

        // 启动巡检工单流程
        Result<StartFlowDto> startFlowResult = null;
        ResponsibleDto startUser = new ResponsibleDto(userId, userName, null);

        try {
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                if(isDataRecord){
                    startFlowResult = orderProcessService.startFlow(
                        tenantId, 
                        idType, 
                        OrderTypeEnum.INSPECT.getValue(), 
                        OutsourceEnum.YES.getValue().equals(outsource),
                        isDataRecord,
                        inspectOrderId,
                        startUser
                    );
                }else{
                    startFlowResult = orderProcessService.startFlow(
                        orderEntity.getCustomerId(), 
                        OrderTypeEnum.INSPECT.getValue(), 
                        inspectOrderId, 
                        startUser, 
                        orderEntity.getSupplierId(),
                        isDataRecord,
                            YesNoEnum.NO.getValue()
                    );
                }

            }else{
                startFlowResult = orderProcessService.startFlow(
                    tenantId, 
                    idType, 
                    OrderTypeEnum.INSPECT.getValue(), 
                    OutsourceEnum.YES.getValue().equals(outsource),
                    isDataRecord,
                    inspectOrderId,
                    startUser
                );
            }
        } catch (Exception e) {
            log.error("startFlow fail {}, {}, {}, {}, {}, {}, {}", 
                tenantId, idType, OrderTypeEnum.INSPECT.getValue(), 
                OutsourceEnum.YES.getValue().equals(outsource),
                isDataRecord, inspectOrderId, startUser
            );
            throw new RuntimeException("调用启动流程异常", e);
        }
        if(!startFlowResult.getSignal()){
            throw new BizException(startFlowResult.getMessage());
        }
        String processInstanceId = startFlowResult.getResult().getProcessInstanceId();
        inspectOrderMapper.updateProcessInstanceIdById(inspectOrderId, processInstanceId);


        if (startFlowResult.getResult().getIsAutoDispatch()){
            orderEntity.setStatus(OrderStatusEnum.WAIT_RECEIVE.getValue());
        }
        String orderNumber = serialNumberService.getNext(tenantId, now.toLocalDate(), InspectOrder.serialNumber);
        orderEntity.setOrderNumber(orderNumber);
        inspectOrderMapper.updateById(orderEntity);

        // 复制标准，创建工单结果
        // 先查出计划涉及的设备和标准
        Long inspectPlanId = planEntity.getId();
        List<InspectPlanDeviceEntity> planDeviceList = inspectPlanDeviceMapper.listByPlanId(inspectPlanId);
        if(planDeviceList != null){
            // 每个设备建一个工单结果
            planDeviceList.forEach(planDevice -> {
                InspectResultEntity inspectResultEntity = new InspectResultEntity();
                inspectResultEntity.setTenantId(tenantId);
                inspectResultEntity.setInspectOrderId(inspectOrderId);
                inspectResultEntity.setDeviceId(planDevice.getDeviceId());
                inspectResultEntity.setStatus(InspectResultStatusEnum.NOT_INSPECT.getValue());
                inspectResultMapper.insert(inspectResultEntity);
                Long resultId = inspectResultEntity.getId();
                // 每个设备对应多个标准
                List<InspectPlanDeviceStandardEntity> planDeviceStandards = inspectPlanDeviceStandardMapper.listByPlanDeviceId(planDevice.getId());
                if(planDeviceStandards == null){
                    return;
                }
                planDeviceStandards.forEach(planDeviceStandard -> {
                    Long inspectStandardId = planDeviceStandard.getInspectStandardId();
                    InspectResultStandardEntity resultStandardEntity = new InspectResultStandardEntity();
                    resultStandardEntity.setTenantId(tenantId);
                    resultStandardEntity.setInspectResultId(resultId);
                    resultStandardEntity.setInspectStandardId(inspectStandardId);
                    inspectResultStandardMapper.insert(resultStandardEntity);
                    
                    // 根据标准创建工单结果项
                    List<InspectStandardItemEntity> standardItemList = inspectStandardItemMapper.listByStandardId(inspectStandardId);
                    if(standardItemList == null){
                        return;
                    }
                    standardItemList.forEach(standardItem -> {
                        InspectResultItemEntity resultItemEntity = new InspectResultItemEntity();
                        resultItemEntity.setTenantId(tenantId);
                        resultItemEntity.setInspectResultId(resultId);
                        resultItemEntity.setInspectStandardItemId(standardItem.getId());
                        inspectResultItemMapper.insert(resultItemEntity);
                    });
                });
            });
        }

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(processInstanceId, orderEntity.getOrderNumber(),
                orderEntity.getOutsource(), tenantId, idType, OrderTypeEnum.INSPECT,startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE: OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(orderEntity.getTimeout()), orderEntity.getCustomerName());

    }

    @Transactional
    public Result<Void> stopPlan(Long tenantId, Integer idType, Long inspectPlanId) {
        if(inspectPlanId == null){
            return Result.error("计划id不能为空");
        }
        // 检查计划是否存在
        InspectPlanEntity entity = inspectPlanMapper.selectById(inspectPlanId);
        if(entity == null){
            return Result.error("计划不存在");
        }

        Long currentUserId = JwtUserInfoUtils.getUserId();
        if(!entity.getCreatorId().equals(currentUserId)){
            return Result.error("创建人才能删除计划");
        }
        // 停止计划
        Integer count = inspectPlanMapper.stopPlan(inspectPlanId);
        Integer c2 = inspectPlanMapper.finishPlanStatusById(inspectPlanId);

        // 删除未结束的工单
        Integer c = inspectOrderMapper.deleteUnFinishOrderByPlanId(inspectPlanId);
        return Result.ok();
    }

    private static final DateTimeFormatter yyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Transactional
    public Result<InspectPlanEntity> create(Long tenantId, Integer idType, String tenantName, InspectPlanDto dto) {
        if(idType == null){
            return Result.error("缺少身份信息：idType");
        }
        if(tenantId == null){
            return Result.error("缺少租户信息：tenantId");
        }
        if(dto.getOrderCreateStrategy() == null){
            return Result.error("创建失败，请选择创建策略");
        }

        Integer outsource = dto.getOutsource();

        if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
            // 供应商为客户创建（委外）
            if(!OutsourceEnum.YES.getValue().equals(outsource)){
                return Result.error("供应商创建计划，必须是委外");
            }
            dto.setSupplierId(tenantId);
            dto.setSupplierName(tenantName);
            Long customerId = dto.getCustomerId();
            if(customerId == null){
                return Result.error("缺少客户id信息：customerId");
            }
            String customerName = dto.getCustomerName();
            if(customerName == null){
                return Result.error("缺少客户名称信息：customerName");
            }
            dto.setTenantId(customerId);
            dto.setCreateSource(CreateSourceEnum.SUPPLIER.getValue());

            Integer outsourceSupplierType = dto.getOutsourceSupplierType();
            if(outsourceSupplierType == null){
                outsourceSupplierType = SupplierTypeEnum.TENANT.getValue();
            }
            dto.setOutsourceSupplierType(outsourceSupplierType);
            Integer outsourceCustomerType = dto.getOutsourceCustomerType();
            if(outsourceCustomerType == null){
                outsourceCustomerType = SupplierTypeEnum.TENANT.getValue();
            }
            dto.setOutsourceCustomerType(outsourceCustomerType);

        }else if(IdTypeEnum.CUSTOMER.getValue().equals(idType)){
            if(OutsourceEnum.YES.getValue().equals(outsource)){
                // 客户为供应商创建（委外）
                dto.setCustomerId(tenantId);
                dto.setCustomerName(tenantName);
                Long supplierId = dto.getSupplierId();
                if(supplierId == null){
                    return Result.error("缺少供应商id信息：supplierId");
                }
                String supplierName = dto.getSupplierName();
                if(supplierName == null){
                    return Result.error("缺少供应商名称信息：supplierName");
                }
                dto.setTenantId(tenantId);
                dto.setCreateSource(CreateSourceEnum.CUSTOMER.getValue());
                
                Integer outsourceSupplierType = dto.getOutsourceSupplierType();
                if(outsourceSupplierType == null){
                    outsourceSupplierType = SupplierTypeEnum.TENANT.getValue();
                }
                dto.setOutsourceSupplierType(outsourceSupplierType);
                Integer outsourceCustomerType = dto.getOutsourceCustomerType();
                if(outsourceCustomerType == null){
                    outsourceCustomerType = SupplierTypeEnum.TENANT.getValue();
                }
                dto.setOutsourceCustomerType(outsourceCustomerType);
            }else{
                dto.setOutsource(OutsourceEnum.NO.getValue());
                // 客户为自己创建（不委外）
                dto.setSupplierId(null);
                dto.setSupplierName(null);
                dto.setCustomerId(tenantId);
                dto.setCustomerName(tenantName);
                dto.setTenantId(tenantId);
                dto.setCreateSource(CreateSourceEnum.CUSTOMER.getValue());
            }
        }else{
            return Result.error("身份信息异常：idType");
        }

        LocalDate today = LocalDate.now();
        LocalDateTime now = LocalDateTime.now();

        String serialNumber = serialNumberService.getNext(tenantId, today, InspectPlan.serialNumber);
        dto.setPlanNumber(serialNumber);
        dto.setStatus(PlanStatusEnum.PROCESSING.getValue());
        dto.setIdType(idType);

        // 计划名不能重复
        List<InspectPlanEntity> sameNamePlanList = inspectPlanMapper.listByName(dto.getCustomerId(), dto.getPlanName());
        if(sameNamePlanList != null && sameNamePlanList.size() > 0){
            return Result.error("创建失败，计划名已存在");
        }

        // // 委外检查是否对设备进行了授权
        // if(OutsourceEnum.YES.getValue().equals(outsource)){
        //     List<InspectPlanDeviceDto> deviceList = dto.getDeviceList();
        //     if(deviceList != null){
        //         for(InspectPlanDeviceDto d:deviceList){
        //             boolean haveDeviceAuth = deviceEmpowerService.haveDeviceAuth(tenantId, dto.getSupplierId(), d.getDeviceId());
        //             if (!haveDeviceAuth) {
        //                 return Result.error("设备未授权给此供应商:" + dto.getSupplierName() + "，请先进行授权");
        //             }
        //         }
        //     }
        // }
        
        // 工单生成策略
        Result<OrderPlanner> plannerResult = OrderPlanner.checkInfo(dto);
        if(!plannerResult.getSignal()){
            return Result.error(plannerResult.getMessage());
        }
        OrderPlanner orderPlanner = plannerResult.getResult();

        if (inspectPlanMapper.insert(dto) != 1) {
            return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
        }

        // 插入计划关联的设备及标准
        List<InspectPlanDeviceDto> planDeviceDtos = dto.getDeviceList();
        if(planDeviceDtos != null){
            planDeviceDtos.forEach(t -> {
                // 插入关联设备
                InspectPlanDeviceEntity entity = new InspectPlanDeviceEntity();
                BeanUtils.copyProperties(t, entity);
                entity.setInspectPlanId(dto.getId());
                entity.setId(null);
                entity.setTenantId(tenantId);
                inspectPlanDeviceMapper.insert(entity);
                // 插入设备关联的标准
                List<IdNameDto> standardList = t.getStandardList();
                if(standardList != null){
                    standardList.forEach(standard -> {
                        InspectPlanDeviceStandardEntity e = new InspectPlanDeviceStandardEntity();
                        e.setInspectPlanDeviceId(entity.getId());
                        e.setInspectStandardId(standard.getId());
                        e.setId(null);
                        e.setTenantId(tenantId);
                        inspectPlanDeviceStandardMapper.insert(e);
                    });
                }
            });
        }
        
        if(OrderCreateStrategyEnum.BEFORE.getValue().equals(dto.getOrderCreateStrategy())){
            // 提前生成

            // 第一次生成时间不能小于当前时间
            LocalDateTime firstCreateTime = orderPlanner.getFirstCreateTime();
            if(firstCreateTime == null){
                throw new RuntimeException("无法获取第一次工单生成时间");
            }
            if(firstCreateTime.isBefore(now)){
                throw new RuntimeException("工单生成时间不能小于当前时间");
            }

            // 更新下次工单生成时间
            Integer count = inspectPlanMapper.updateNextOrderCreateTime(dto.getId(), firstCreateTime);
            
            // 剩下的交给定时器

            return Result.ok(dto);
        }

        // 立即生成

        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();


        final AtomicInteger orderCreateCount = new AtomicInteger(0);
        orderPlanner.iterateEach(t -> {
            createOrderDetails(
                tenantId,
                idType,
                t, 
                dto, 
                now, 
                currentUserId, 
                currentUserName
            );
            orderCreateCount.incrementAndGet();
        });
        if(orderCreateCount.get() <= 0){
            throw new BizException("计划创建的工单数量不能为0，请选择有效的时间和周期");
        }

        return Result.ok(dto);

    }

    public Result<Page<InspectPlanVo>> getPage(Long tenantId,  Integer idType, InspectPlanParams params, Page<InspectPlanEntity> page) {
        if(IdTypeEnum.CUSTOMER.getValue().equals(idType)){
        }else if(IdTypeEnum.SUPPLIER.getValue().equals(idType)){
        }else{
            return Result.error("身份参数错误：idType");
        }
        LocalDateTime lastModifyStartDateTime = null;
        if(params.getLastModifyStartDate() != null){
            lastModifyStartDateTime = LocalDateTime.of(params.getLastModifyStartDate(), LocalTime.of(0, 0, 0));
        }
        LocalDateTime lastModifyEndDateTime = null;
        if(params.getLastModifyEndDate() != null){
            lastModifyEndDateTime = LocalDateTime.of(params.getLastModifyEndDate(), LocalTime.of(23, 59, 59));
        }
        LocalDateTime nextOrderCreateStartDateTime = null;
        if(params.getNextOrderCreateStartDate() != null){
            nextOrderCreateStartDateTime = LocalDateTime.of(params.getNextOrderCreateStartDate(), LocalTime.of(0, 0, 0));
        }
        LocalDateTime nextOrderCreateEndDateTime = null;
        if(params.getNextOrderCreateEndDate() != null){
            nextOrderCreateEndDateTime = LocalDateTime.of(params.getNextOrderCreateEndDate(), LocalTime.of(23, 59, 59));
        }
        Page<InspectPlanVo> entityPage = inspectPlanMapper.pageByParams(
            tenantId, idType, params, page, 
            lastModifyStartDateTime, lastModifyEndDateTime,
            nextOrderCreateStartDateTime, nextOrderCreateEndDateTime
        );
        
        Page<InspectPlanVo> voPage = new Page<>();
        BeanUtils.copyProperties(entityPage, voPage);
        List<InspectPlanVo> voList = new ArrayList<>();
        Map<Long, InspectPlanVo> voMap = new HashMap<>();
        voPage.setRecords(voList);

        List<InspectPlanVo> records = entityPage.getRecords();
        if(records != null && records.size() > 0){
            // 构建vo
            records.forEach(t -> {
                InspectPlanVo vo = new InspectPlanVo();
                BeanUtils.copyProperties(t, vo);
                vo.setAllOrderCount(0);
                vo.setFinishOrderCount(0);
                vo.setProcessingOrderCount(0);
                voList.add(vo);
                voMap.put(t.getId(), vo);
            });

            // 工单数统计
            List<Long> planIds = records.stream().map(InspectPlanEntity::getId).collect(Collectors.toList());
            List<InspectOrderEntity> orderList = inspectOrderMapper.listByPlanIds(planIds);
            if(orderList != null){
                orderList.forEach(t -> {
                    Long inspectPlanId = t.getInspectPlanId();
                    InspectPlanVo vo = voMap.get(inspectPlanId);
                    if(vo != null){
                        vo.setAllOrderCount(vo.getAllOrderCount() + 1);
                        if(OrderStatusEnum.ACCEPTED.getValue().equals(t.getStatus())
                            || OrderStatusEnum.REJECT.getValue().equals(t.getStatus())
                        ){
                            vo.setFinishOrderCount(vo.getFinishOrderCount() + 1);
                        }else{
                            vo.setProcessingOrderCount(vo.getProcessingOrderCount() + 1);
                        }
                    }
                });
            }

            // 设备数统计
            Map<String, CountVo> countMap = inspectPlanDeviceMapper.countByInspectPlanIds(planIds);
            voList.forEach(t -> {
                CountVo countVo = countMap.get(t.getId().toString());
                if(countVo != null){
                    t.setInspectDeviceCount(countVo.getValue());
                }else{
                    t.setInspectDeviceCount(0);
                }
            });

            // 操作按钮
            Long currentUserId = JwtUserInfoUtils.getUserId();
            voList.forEach(vo -> {
                vo.setOperations(listPlanOperations(vo, currentUserId));
            });
        }

        return Result.ok(voPage);
    }

    private List<String> listPlanOperations(InspectPlanVo vo, Long currentUserId){
        List<String> operations = new ArrayList<>();
        // 停止按钮
        if(vo.getCreatorId().equals(currentUserId)){
            if(PlanStatusEnum.PROCESSING.getValue().equals(vo.getStatus())){
                operations.add(OperationEnum.STOP.getName());
            }
        }
        return operations;
    }

    public Result<List<InspectPlanEntity>> list(Long tenantId, InspectPlanEntity entity) {
        List<InspectPlanEntity> list = inspectPlanMapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    public Result<Void> update(Long tenantId, InspectPlanEntity entity) {
        entity.setTenantId(tenantId);
        if (inspectPlanMapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    public Result<Void> deleteById(Long tenantId, Long entityId) {
        if (inspectPlanMapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    public Result<InspectPlanDto> getPlanDetailById(Long tenantId, Long inspectPlanId) {
        // 获取计划
        InspectPlanEntity entity = inspectPlanMapper.selectById(inspectPlanId);
        if(entity == null){
            return Result.error("点巡检计划不存在");
        }

        InspectPlanDto dto = new InspectPlanDto();
        BeanUtils.copyProperties(entity, dto);

        // 获取计划关联的设备
        List<InspectPlanDeviceVo> planDeviceList = inspectPlanDeviceMapper.listByPlanIdWithDeviceInfo(inspectPlanId);
        if(planDeviceList == null){
            planDeviceList = new ArrayList<>();
        }
        
        // 获取设备关联的标准
        List<Long> planDeviceIds = planDeviceList.stream().map(InspectPlanDeviceVo::getId).collect(Collectors.toList());
        List<InspectPlanDeviceStandardVo> planDeviceStandards = inspectPlanDeviceStandardMapper.listByPlanDeviceIds(planDeviceIds);
        if(planDeviceStandards == null){
            planDeviceStandards = new ArrayList<>();
        }
        // inspectPlanDeviceId -> standardList
        Map<Long, List<InspectPlanDeviceStandardVo>> map = planDeviceStandards.stream().collect(Collectors.groupingBy(InspectPlanDeviceStandardVo::getInspectPlanDeviceId));
        
        List<InspectPlanDeviceDto> planDevices = new ArrayList<>();
        planDeviceList.forEach(t -> {
            InspectPlanDeviceDto inspectPlanDeviceDto = new InspectPlanDeviceDto();
            BeanUtils.copyProperties(t, inspectPlanDeviceDto);
            Long inspectPlanDeviceId = t.getId();
            List<InspectPlanDeviceStandardVo> standardList = map.get(inspectPlanDeviceId);
            if(standardList == null){
                standardList = new ArrayList<>();
            }
            List<IdNameDto> dtoList = standardList.stream().map(v -> {
                IdNameDto d = new IdNameDto();
                d.setId(v.getInspectStandardId());
                d.setName(v.getStandardName());
                return d;
            }).collect(Collectors.toList());
            inspectPlanDeviceDto.setStandardList(dtoList);
            planDevices.add(inspectPlanDeviceDto);
        });

        dto.setDeviceList(planDevices);

        return Result.ok(dto);
    }

}
