package com.nti56.dcm.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.dcm.server.entity.InspectStandardModuleEntity;
import com.nti56.dcm.server.mapper.InspectStandardItemMapper;
import com.nti56.dcm.server.mapper.InspectStandardModuleMapper;
import com.nti56.dcm.server.service.IInspectStandardModuleService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 点巡检标准模块表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-07-04 09:39:39
 * @since JDK 1.8
 */
@Service
public class InspectStandardModuleServiceImpl extends ServiceImpl<InspectStandardModuleMapper, InspectStandardModuleEntity>
        implements IInspectStandardModuleService {

    @Autowired
    InspectStandardModuleMapper mapper;

    @Autowired
    private InspectStandardItemMapper inspectStandardItemMapper;

    @Override
    public Result<InspectStandardModuleEntity> create(TenantIsolation tenantIsolation, InspectStandardModuleEntity entity) {
        if (!unique(null, tenantIsolation.getTenantId(), entity.getInspectStandardId(), entity.getModuleName())) {
            return Result.error("模块名称不允许重复！");
        }
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<List<InspectStandardModuleEntity>> listByStandardId(TenantIsolation tenantIsolation, Long standardId) {
        List<InspectStandardModuleEntity> list = this.list(Wrappers.<InspectStandardModuleEntity>lambdaQuery()
                .eq(InspectStandardModuleEntity::getInspectStandardId, standardId)
                .eq(InspectStandardModuleEntity::getTenantId, tenantIsolation.getTenantId()));
        return Result.ok(list);
    }

    @Override
    public Result<Void> update(TenantIsolation tenantIsolation, InspectStandardModuleEntity entity) {
        if (!unique(entity.getId(), tenantIsolation.getTenantId(), entity.getInspectStandardId(), entity.getModuleName())) {
            return Result.error("模块名称不允许重复！");
        }
        if (mapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    @Transactional
    public Result<Void> deleteById(Long entityId) {
        if (mapper.deleteById(entityId) > 0) {
            // 删除关联的巡检标准项
            inspectStandardItemMapper.deleteByModuleId(entityId);
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Long getModuleIdByModuleName(Long tenantId, Long standardId, String moduleName) {
        InspectStandardModuleEntity moduleEntity = this.getOne(Wrappers.<InspectStandardModuleEntity>lambdaQuery()
                .eq(InspectStandardModuleEntity::getModuleName, moduleName)
                .eq(InspectStandardModuleEntity::getInspectStandardId, standardId)
                .eq(InspectStandardModuleEntity::getTenantId, tenantId)
                .last("limit 1"));
        if (moduleEntity != null) {
            return moduleEntity.getId();
        }

        Long moduleId = IdGenerator.generateId();
        InspectStandardModuleEntity newModule = InspectStandardModuleEntity.builder()
                .id(moduleId)
                .tenantId(tenantId)
                .inspectStandardId(standardId)
                .moduleName(moduleName)
                .build();
        mapper.insert(newModule);
        return moduleId;
    }

    public boolean unique(Long id, Long tenantId, Long standardId, String name){
        Long count = new LambdaQueryChainWrapper<>(mapper)
                .ne(id != null, InspectStandardModuleEntity::getId, id)
                .eq(InspectStandardModuleEntity::getTenantId, tenantId)
                .eq(InspectStandardModuleEntity::getInspectStandardId, standardId)
                .eq(InspectStandardModuleEntity::getModuleName, name)
                .count();
        if (count > 0) {
            return false;
        }
        return true;
    }

}
