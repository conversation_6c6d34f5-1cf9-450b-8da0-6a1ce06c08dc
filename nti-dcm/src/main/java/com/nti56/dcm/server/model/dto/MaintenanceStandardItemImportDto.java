package com.nti56.dcm.server.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/7/15 17:01<br/>
 * @since JDK 1.8
 */
@Data
public class MaintenanceStandardItemImportDto {

    /**
     * 完整部位名称
     */
    @ExcelProperty(value = "模块名称", index = 0)
    private String fullBomName;

    /**
     * 保养部位
     */
    @ExcelProperty(value = "项目名称", index = 1)
    private String position;

    /**
     * 保养标准
     */
    @ExcelProperty(value = "备注", index = 2)
    private String standardDesc;

}
