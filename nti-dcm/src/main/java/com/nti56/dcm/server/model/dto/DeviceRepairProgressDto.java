package com.nti56.dcm.server.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nti56.dcm.server.entity.WorkloadEntity;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.nlink.common.util.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceRepairProgressDto extends PageParam implements Serializable {


    /**
     * ID
     */
    private Long id;

    /**
     * 设备维修工单id
     */
    private Long deviceRepairId;

    /**
     *委外执行工单时，支持的供应商id
     */
    private Long supportSupplierId;
    /**
     * 流程类型 参考DeviceRepairProgressEnum
     */
    private Integer progressType;

    /**
     * 执行设备部位id
     */
    private Long executeDeviceBomId;
    /**
     * 执行设备部位名称
     */
    private String executeDeviceBomName;
    /**
     * 进度执行人id
     */
    private Long executeUserId;

    /**
     * 进度执行人名称
     */
    private String executeUserName;

    /**
     * 进度执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 维修附图
     */
    private List<FileVo> repairImages;
    /**
     * 委外执行（0-内部，1-供应商）
     */
    private Integer outsourceExecute;

    /**
     * 接收工单人
     */
    private String receiveUser;

    /**
     * 故障类型
     */
    private Long faultType;

    /**
     * 故障原因
     */
    private String faultReason;

    /**
     * 是否加入经验库(0-否，1-是)
     */
    private Integer repairSaveExp;
    /**
     * 内部设备类型id
     */
    private Long innerDeviceTypeId;
    /**
     * 处理过程
     */
    private String repairProcess;

    /**
     * 维修开始时间
     */
    private LocalDateTime repairStartTime;

    /**
     * 维修结束时间
     */
    private LocalDateTime repairEndTime;

    /**
     * 维修耗时（时分格式）
     */
    private String repairUseTime;

    /**
     * 验收状态 0 验收不通过 1 验收通过
     */
    private Integer acceptState;
    /**
     * 验收说明
     */
    private String acceptExplain;
    /**
     * 创建人
     */
    private String creator;
    /**
     *创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     *修改人ID
     */
    private Long updatorId;

    /**
     * 修改人
     */
    private String updator;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 人员工作量列表
     */
    private List<WorkloadEntity> workloadList;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 所属身份 1-供应商 2-客户
     */
    private Integer idType;

    /**
     * 是否展示处理按钮  0-不展示  1-展示
     */
    private boolean showHandle;

    /**
     * 是否展示处理按钮  0-不展示  1-展示
     */
    private boolean showRevoke;




    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}