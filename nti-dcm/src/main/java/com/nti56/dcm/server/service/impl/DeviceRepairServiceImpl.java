package com.nti56.dcm.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.Lists;
import com.google.common.collect.Multiset;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.DeviceRepair;
import com.nti56.dcm.server.domain.enums.*;
import com.nti56.dcm.server.entity.*;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.mapper.*;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.*;
import com.nti56.dcm.server.service.*;
import com.nti56.flowable.common.dto.TaskDto;
import com.nti56.flowable.common.dto.flow.SelectValue;
import com.nti56.flowable.common.service.biz.IRemoteService;
import com.nti56.flowable.common.utils.JsonUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.nti56.flowable.common.constants.ProcessInstanceConstant.VariableKey.AUTO_DISPATCH_FLAG;

/**
 * <p>
 * 设备维修工单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
@Slf4j
public class DeviceRepairServiceImpl extends ServiceImpl<DeviceRepairMapper, DeviceRepairEntity> implements IDeviceRepairService {

    @Autowired
    private SerialNumberService serialNumberService;
    @Autowired
    private FileMapper fileMapper;
    @Autowired
    private OrderProcessService orderProcessService;
    @Autowired
    private OrderProcessRecordService orderProcessRecordService;

    @Autowired
    private DeviceRepairMapper deviceRepairMapper;
    @Autowired
    private IDeviceRepairProgressService deviceRepairProgressService;
    @Autowired
    private WorkloadMapper workloadMapper;
    @Autowired
    private CustomerRelationService customerRelationService;
    @Autowired
    private CustomerRelationMapper customerRelationMapper;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TenantInfoService tenantInfoService;
    @Autowired
    private DeviceTypeService deviceTypeService;
    @Autowired
    private IFaultTypeService faultTypeService;
    @Autowired
    private IDeviceRepairExpService deviceRepairExpService;
    @Autowired
    private MaintenanceOrderService maintenanceOrderService;
    @Autowired
    private InspectOrderService inspectOrderService;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private UserRoleAuthService userRoleAuthService;

    @Autowired
    private TimeoutConfigService timeoutConfigService;

    @Autowired
    private IRemoteService remoteService;

    @Autowired
    private IMessageService messageService;

    @Autowired
    private AlarmRecordService alarmRecordService;

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private MessageNoticeStrategyMapper messageNoticeStrategyMapper;

    @Autowired
    private AsyncSendNoticeService asyncSendNoticeService;

    @Autowired
    private AlarmRecordMapper alarmRecordMapper;
    @Resource
    private RuntimeService runtimeService;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Override
    @Transactional
    public Result create(DeviceRepairDto dto, TenantIsolation tenantIsolation) {
        Result<String> tenantNameResult = userCenterService.getTenantNameById(tenantIsolation.getTenantId());
        if (!tenantNameResult.getSignal()) {
            return Result.error(tenantNameResult.getMessage());
        }
        String tenantName = tenantNameResult.getResult();
        DeviceRepairEntity deviceRepairEntity = BeanUtilsIntensifier.copyBean(dto, DeviceRepairEntity.class);
        LocalDate today = LocalDate.now();
        String serialNumber = serialNumberService.getNext(tenantIsolation.getTenantId(), today, DeviceRepair.SERIALNUMBER);
        deviceRepairEntity.setOrderNumber(serialNumber);
        deviceRepairEntity.setStatus(OrderStatusEnum.WAIT_DISPATCH.getValue());
        long generateId = IdGenerator.generateId();
        deviceRepairEntity.setTenantId(tenantIsolation.getTenantId());
        deviceRepairEntity.setId(generateId);
        deviceRepairEntity.setCustomerName(tenantName);
        deviceRepairEntity.setCreateSource(DictConstant.ID_TYPE_CUSTOMER);
        deviceRepairEntity.setTimeout(TimeoutEnum.NORMAL.getValue());
        Long customerId = tenantIsolation.getTenantId();
        // 委外工单查询是否为数据型供应商或者数据型客户
        if (ObjectUtil.equals(DictConstant.HAS_OUTSOURCE, dto.getOutsourceStatus())  ) {
            // 外委单据 由供应商或者集成商创建  调转租户和供应商关系  当前租户是外委供应商   外委方位单据租户id
            if (!ObjectUtil.equals(YesNoEnum.YES.getValue(),dto.getAggregatorOutsource()) && DictConstant.ID_TYPE_SUPPLIER.equals(dto.getCreateSource())){
                TenantInfoEntity tenantInfo = tenantInfoService.getByPmoTenantId( dto.getOutsourceSupplierId());
                Long pmoSourceTenantId = Optional.ofNullable(tenantInfo).map(BaseEntity::getTenantId).orElse(null);
                deviceRepairEntity.setTenantId(pmoSourceTenantId!=null?pmoSourceTenantId:dto.getOutsourceSupplierId());
                deviceRepairEntity.setCustomerName(dto.getOutsourceSupplierName());
                deviceRepairEntity.setOutsourceSupplierId(tenantIsolation.getTenantId());
                deviceRepairEntity.setOutsourceSupplierName(tenantName);
                deviceRepairEntity.setCreateSource(DictConstant.ID_TYPE_SUPPLIER);
                customerId = dto.getOutsourceSupplierId();
            }
            CustomerRelationEntity customerRelationEntity = new CustomerRelationEntity();
            customerRelationEntity.setCustomerId(customerId);
            customerRelationEntity.setSupplierId(deviceRepairEntity.getOutsourceSupplierId());
            customerRelationEntity.setIdType(tenantIsolation.getIdType());
            customerRelationEntity.setStatus(RelationStatusEnum.APPROVE.getValue());
            List<CustomerRelationEntity> customerRelationEntities = customerRelationMapper.listCustomerByEntity(customerRelationEntity);
            if (CollUtil.isEmpty(customerRelationEntities)) {
                return Result.error("不存在客户关系");
            }
            CustomerRelationEntity result = customerRelationEntities.get(0);
            Integer type = result.getType();
            if (DictConstant.ID_TYPE_SUPPLIER.equals(dto.getCreateSource())) {
                deviceRepairEntity.setOutsourceCustomerType(type);
                deviceRepairEntity.setOutsourceSupplierType(SupplierTypeEnum.TENANT.getValue());
            } else {
                deviceRepairEntity.setOutsourceSupplierType(type);
                deviceRepairEntity.setOutsourceCustomerType(SupplierTypeEnum.TENANT.getValue());
            }
        }
        Result<StartFlowDto> startFlowResult = Result.error();
        // 客户类型为2的情况下  为数据型供应商或者客户
        Boolean isDataRecord = ObjectUtil.equals(deviceRepairEntity.getOutsourceSupplierType(), 2) || ObjectUtil.equals(deviceRepairEntity.getOutsourceCustomerType(), 2);
        // 调用工作流启动流程获取流程实例id
        if (ObjectUtil.equals(DictConstant.HAS_OUTSOURCE, dto.getOutsourceStatus())) {
            // 数据型数据调用内部工作流
            if (isDataRecord) {
                startFlowResult = orderProcessService.startFlow(
                        tenantIsolation.getTenantId(),
                        tenantIsolation.getIdType(),
                        OrderTypeEnum.REPAIR.getValue(),
                        true,
                        true,
                        generateId,
                        new ResponsibleDto(JwtUserInfoUtils.getUserId(), JwtUserInfoUtils.getUserName(), false));
            } else {
                // 外委的租户型工单调用单独的启动流程，此流程跨租户
                startFlowResult = orderProcessService.startFlow(
                        deviceRepairEntity.getTenantId(),
                        OrderTypeEnum.REPAIR.getValue(),
                        generateId,
                        new ResponsibleDto(JwtUserInfoUtils.getUserId(), JwtUserInfoUtils.getUserName(), false),
                        deviceRepairEntity.getOutsourceSupplierId(),
                        isDataRecord
                ,dto.getAggregatorOutsource());
            }
        } else {
            startFlowResult = orderProcessService.startFlow(
                    deviceRepairEntity.getTenantId(),
                    tenantIsolation.getIdType(),
                    OrderTypeEnum.REPAIR.getValue(),
                    ObjectUtil.equals(deviceRepairEntity.getOutsourceStatus(), DictConstant.HAS_OUTSOURCE),
                    isDataRecord,
                    generateId,
                    new ResponsibleDto(JwtUserInfoUtils.getUserId(), JwtUserInfoUtils.getUserName(), false));
        }
        if (!startFlowResult.getSignal()) {
            throw new BizException(startFlowResult.getMessage());
        }
        deviceRepairEntity.setProcessInstanceId(startFlowResult.getResult().getProcessInstanceId());
        if (startFlowResult.getResult().getIsAutoDispatch()){
            deviceRepairEntity.setStatus(OrderStatusEnum.WAIT_RECEIVE.getValue());
        }
        this.save(deviceRepairEntity);
        if (CollectionUtil.isNotEmpty(dto.getFaultImages())) {
            dto.getFaultImages().forEach(item -> {
                item.setId(IdGenerator.generateId());
                item.setBizId(generateId);
                item.setBizType(FileBizTypeEnum.DEVICE_REPAIR_PICTURE.getValue());
                item.setTenantId(tenantIsolation.getTenantId());
                FileEntity fileEntity = BeanUtil.copyProperties(item, FileEntity.class);
                fileMapper.insert(fileEntity);
            });
        }
        if (CollectionUtil.isNotEmpty(dto.getFaultVideos())) {
            dto.getFaultVideos().forEach(item -> {
                item.setId(IdGenerator.generateId());
                item.setBizId(generateId);
                item.setBizType(FileBizTypeEnum.DEVICE_REPAIR_VIDEO.getValue());
                item.setTenantId(tenantIsolation.getTenantId());
                FileEntity fileEntity = BeanUtil.copyProperties(item, FileEntity.class);
                fileMapper.insert(fileEntity);
            });
        }

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(deviceRepairEntity.getProcessInstanceId(), deviceRepairEntity.getOrderNumber(),
                deviceRepairEntity.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE:OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(deviceRepairEntity.getTimeout()), OutsourceEnum.YES.getValue().equals(deviceRepairEntity.getOutsourceStatus()) ? deviceRepairEntity.getCustomerName() : null);

        // 如果是告警记录创建的工单，需要更新告警记录的工单id
        if (Objects.nonNull(dto.getAlarmRecordId())) {
            alarmRecordMapper.bindOrder(dto.getAlarmRecordId(), deviceRepairEntity.getId(), deviceRepairEntity.getOrderNumber(),
                    OrderTypeEnum.REPAIR.getValue(),startFlowResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE.getValue(): OrderStatusEnum.WAIT_DISPATCH.getValue());
        }

        return Result.ok(deviceRepairEntity);
    }

    private Result<Object> validOutsourceCreateAuth(DeviceRepairDto dto, TenantIsolation tenantIsolation) {
        if (ObjectUtil.equals(DictConstant.HAS_OUTSOURCE, dto.getOutsourceStatus())) {

            if (ObjectUtil.isNull(dto.getOutsourceSupplierId()) || ObjectUtil.isNull(dto.getOutsourceSupplierName())) {
                return Result.error("委外创建维修工单时，请传入供应商id和名称!");
            }
        }
        return Result.ok();
    }

    @Override
    public Result getPage(DeviceRepairQueryDto queryDto, Page<DeviceRepairQueryDto> page, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(queryDto.getQueryType())) {
            return Result.error("请传入查询类型！");
        }
        switch (queryDto.getQueryType()) {
            case DictConstant.QUERY_TYPE_ALL:
                return this.pageOrderByAdmin(queryDto, page, tenantIsolation);
            case DictConstant.QUERY_TYPE_ABOUT_ME:
                return this.pageOrderWithMe(queryDto, page, tenantIsolation);
            case DictConstant.QUERY_TYPE_WAIT_HANDLE:
                return this.pageOrderWaitProcess(queryDto, page, tenantIsolation);
            default:
                log.error("传入类型不合法！" + JSONObject.toJSONString(queryDto));
                return Result.error("传入查询类型不合法！");
        }
    }


    @Override
    public Result revoke(DeviceRepairProgressDto dto, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(dto.getDeviceRepairId())) {
            return Result.error("未传入维修工单id！");
        }
        Long id = dto.getDeviceRepairId();
        DeviceRepairEntity byId = this.getById(id);
        Result<Long> revokeAuth = this.validRevokeAuth(byId, tenantIsolation);
        if (!revokeAuth.getSignal()) {
            return revokeAuth;
        }
        Result<Void> undo = orderProcessService.undo(tenantIsolation.getTenantId(), id, byId.getProcessInstanceId(), OrderTypeEnum.REPAIR.getValue());
        if (!undo.getSignal()) {
            throw new BizException(undo.getMessage());
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getStatus, OrderStatusEnum.REVOKE.getValue())
                .eq(DeviceRepairEntity::getId, dto.getDeviceRepairId())
        );

        // 处理工单告警记录
        alarmRecordService.finishAlarm(id, OrderTypeEnum.REPAIR.getValue());
        return Result.ok();
    }

    private Result<Long> validRevokeAuth(DeviceRepairEntity byId, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(byId)) {
            return Result.error("找不到维修工单");
        }
        boolean innerOrderFlag = ObjectUtil.equals(DictConstant.NOT_OUTSOURCE, byId.getOutsourceStatus());
        if (innerOrderFlag) {
            if (!ObjectUtil.equals(byId.getTenantId(), tenantIsolation.getTenantId())) {
                return Result.error("找不到内部维修工单");
            }
        }
        if (!ObjectUtil.equals(OrderStatusEnum.WAIT_DISPATCH.getValue(), byId.getStatus()) &&
                !ObjectUtil.equals(OrderStatusEnum.WAIT_RECEIVE.getValue(), byId.getStatus())) {
            return Result.error("工单已分派或已接收，无法撤销！");
        }

        Long userId = JwtUserInfoUtils.getUserId();
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        if (!isSuperRole) {
            // 检查当前用户是否有撤销权限
            if (!ObjectUtil.equals(userId, byId.getCreatorId())) {
                return Result.error("不是工单创建人，无法撤销工单！");
            }
        }
        return Result.ok(userId);
    }

    @Autowired
    private IWorkGroupUserService workGroupUserService;
    /**
     * 执行派单操作
     *
     * @param dto
     * @param tenantIsolation
     * @return
     */
    @Override
    @Transactional
    public Result receiveOrder(OrderReceiveDto dto, TenantIsolation tenantIsolation) {
        Long orderId = dto.getId();
        DeviceRepairEntity byId = this.getById(orderId);
        Result<Long> existRes = this.validOrderExist(byId, tenantIsolation);
        if (!existRes.getSignal()) {
            return existRes;
        }
        if (!ObjectUtil.equals(OrderStatusEnum.WAIT_RECEIVE.getValue(), byId.getStatus())) {
            if (OrderStatusEnum.REVOKE.getValue().equals(byId.getStatus())) {
                return Result.error("接单失败，工单已撤销");
            }
            return Result.error("接单失败，工单已被接收");
        }
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);

        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getRealname();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), currentUserId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        String workGroupNameByUserId = workGroupUserService.getWorkGroupNameByUserId(OrderTypeEnum.REPAIR.getValue(), currentUserId, tenantIsolation);
        if (StrUtil.isNotBlank(workGroupNameByUserId)) {
            currentUserName = currentUserName + "(" +workGroupNameByUserId +")";
        }
        Result<Void> receive = orderProcessService.receiveOrder(tenantIsolation.getTenantId(), orderId, byId.getProcessInstanceId(), new ResponsibleDto(currentUserId, currentUserName, false), OrderTypeEnum.REPAIR.getValue(), isSuperRole);
        if (!receive.getSignal()) {
            throw new BizException(receive.getMessage());
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getStatus, OrderStatusEnum.EXECUTING.getValue())
                .set(DeviceRepairEntity::getReceiveUserId,currentUserId)
                .eq(DeviceRepairEntity::getId, orderId)
        );

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(byId.getProcessInstanceId(), byId.getOrderNumber(),
                byId.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(byId.getTimeout()), OutsourceEnum.YES.getValue().equals(byId.getOutsourceStatus()) ? byId.getCustomerName() : null);

        return Result.ok();
    }

    /**
     * 执行派单操作
     *
     * @param dto
     * @param tenantIsolation
     * @return
     */
    @Override
    @Transactional
    public Result dispatch(DeviceRepairDispatchDto dto, TenantIsolation tenantIsolation) {
        Long id = dto.getId();
        DeviceRepairEntity byId = this.getById(id);
        Result<Long> existRes = this.validOrderExist(byId, tenantIsolation);
        if (!existRes.getSignal()) {
            return existRes;
        }
        if (!ObjectUtil.equals(OrderStatusEnum.WAIT_DISPATCH.getValue(), byId.getStatus())) {
            if (OrderStatusEnum.REVOKE.getValue().equals(byId.getStatus())) {
                return Result.error("派单失败，工单已撤销");
            }
            return Result.error("派单失败，工单已分派");
        }
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);

        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();
        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        Result<Void> dispatch = orderProcessService.dispatch(tenantIsolation.getTenantId(), id, byId.getProcessInstanceId(), new ResponsibleDto(dto.getReceiveUserId(), dto.getReceiveUserName() + "(" + dto.getWorkGroupName() + ")", false), OrderTypeEnum.REPAIR.getValue(), isSuperRole);
        if (!dispatch.getSignal()) {
            throw new BizException(dispatch.getMessage());
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getStatus, OrderStatusEnum.EXECUTING.getValue())
                .set(DeviceRepairEntity::getReceiveUserId, dto.getReceiveUserId())
                .eq(DeviceRepairEntity::getId, dto.getId())
        );

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(byId.getProcessInstanceId(), byId.getOrderNumber(),
                byId.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(byId.getTimeout()), OutsourceEnum.YES.getValue().equals(byId.getOutsourceStatus()) ? byId.getCustomerName() : null);

        return Result.ok();
    }

    private Result<Long> validOrderExist(DeviceRepairEntity byId, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(byId)) {
            return Result.error("找不到维修工单");
        }
        boolean innerOrderFlag = ObjectUtil.equals(DictConstant.NOT_OUTSOURCE, byId.getOutsourceStatus());
        if (innerOrderFlag) {
            if (!ObjectUtil.equals(byId.getTenantId(), tenantIsolation.getTenantId())) {
                return Result.error("找不到内部维修工单");
            }
        } else {
            if (ObjectUtil.equals(byId.getOutsourceSupplierType(), 1) && !ObjectUtil.equals(byId.getOutsourceSupplierId(), tenantIsolation.getTenantId())) {
                return Result.error("找不到外委维修工单");
            }
        }
        return Result.ok();
    }

    @Autowired
    private IDanswerUserCharSessionService danswerUserCharSessionService;

    @Override
    @Transactional
    public Result execute(DeviceRepairProgressDto dto, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(dto.getDeviceRepairId()) || ObjectUtil.isNull(dto.getFaultType()) || ObjectUtil.isNull(dto.getFaultReason())
                || ObjectUtil.isNull(dto.getRepairProcess()) || ObjectUtil.isNull(dto.getRepairSaveExp())) {
            return Result.error("请完整填写表单！");
        }
        Long id = dto.getDeviceRepairId();
        DeviceRepairEntity byId = this.getById(id);

        Result<Long> executeAuth = this.validExecuteAuth(byId, tenantIsolation);
        if (!executeAuth.getSignal()) {
            return executeAuth;
        }
        List<WorkloadEntity> workloadList = dto.getWorkloadList();
        // 删除人员工作量
        Integer deleteCount = workloadMapper.deleteByOrderId(OrderTypeEnum.REPAIR.getValue(), id);
        if (CollUtil.isNotEmpty(workloadList)) {
            // 插入人员工作量
            for (WorkloadEntity t : workloadList) {
                t.setId(null);
                t.setOrderId(id);
                t.setOrderType(OrderTypeEnum.REPAIR.getValue());
                LocalDateTime beginTime = t.getBeginTime();
                LocalDateTime endTime = t.getEndTime();
                if (beginTime != null && endTime != null) {
                    if (beginTime.isAfter(endTime)) {
                        throw new RuntimeException("开始时间不能大于结束时间");
                    }
                    Long minutes = ChronoUnit.MINUTES.between(beginTime, endTime);
                    t.setCostTime(minutes.intValue());
                }
                workloadMapper.insert(t);
            }
        }
        DeviceEntity deviceEntity = deviceService.getById(byId.getDeviceId());
        if (ObjectUtil.isNull(deviceEntity)) {
            return Result.error("维修设备不存在！");
        }
        Long deviceTypeId = deviceEntity.getDeviceTypeId();
        String deviceName = deviceEntity.getDeviceName();
        // 如果是委外单据 且 界面设定的内部设备类型id不为空
        if (ObjectUtil.equals(DictConstant.HAS_OUTSOURCE, byId.getOutsourceStatus()) && ObjectUtil.isNotNull(dto.getInnerDeviceTypeId())) {
            deviceTypeId = dto.getInnerDeviceTypeId();
        }
        DeviceTypeEntity deviceTypeEntity = deviceTypeService.getById(deviceTypeId);
        if (ObjectUtil.isNull(deviceTypeEntity)) {
            return Result.error("维修设备的设备类型不存在！");
        }
        Result saveRepairExpRes = saveRepairExp(dto, tenantIsolation, byId, deviceTypeEntity, deviceName);
        if (!saveRepairExpRes.getSignal()) {
            return saveRepairExpRes;
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getStatus, OrderStatusEnum.WAIT_ACCEPT.getValue())
                .set(DeviceRepairEntity::getFaultType, dto.getFaultType())
                .set(DeviceRepairEntity::getFaultReason, dto.getFaultReason())
                .set(DeviceRepairEntity::getRepairProcess, dto.getRepairProcess())
                .set(DeviceRepairEntity::getExecuteDeviceBomId, dto.getExecuteDeviceBomId())
                .set(DeviceRepairEntity::getExecuteDeviceBomName, dto.getExecuteDeviceBomName())
                .set(DeviceRepairEntity::getRepairSaveExp, dto.getRepairSaveExp())
                .set(DeviceRepairEntity::getInnerDeviceTypeId, dto.getInnerDeviceTypeId())
                .set(DeviceRepairEntity::getSupportSupplierId, dto.getSupportSupplierId())
                .eq(DeviceRepairEntity::getId, dto.getDeviceRepairId())
        );
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();

        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        Result<Void> executeResult = orderProcessService.doTask(tenantIsolation.getTenantId(), id, byId.getProcessInstanceId(), OrderTypeEnum.REPAIR.getValue(), isSuperRole);
        if (!executeResult.getSignal()) {
            throw new BizException(executeResult.getMessage());
        }
        if (CollectionUtil.isNotEmpty(dto.getRepairImages())) {
            dto.getRepairImages().forEach(item -> {
                item.setBizId(dto.getDeviceRepairId());
                item.setBizType(FileBizTypeEnum.DEVICE_EXECUTE_REPAIR_PICTURE.getValue());
                item.setTenantId(tenantIsolation.getTenantId());
                FileEntity fileEntity = BeanUtil.copyProperties(item, FileEntity.class);
                fileMapper.insert(fileEntity);
            });
        }
        // 将维修记录转成文件发送给danswer
        danswerUserCharSessionService.sendFileToDanswer(dto.getRepairProcess(), String.format("设备%s(设备类型为:%s)%s维修记录.txt", deviceName, deviceTypeEntity.getTypeName(), DateUtil.formatDateTime(new Date())), OrderTypeEnum.REPAIR);
        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(byId.getProcessInstanceId(), byId.getOrderNumber(),
                byId.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, OrderStatusEnum.WAIT_ACCEPT,
                TimeoutEnum.typeOfValue(byId.getTimeout()), OutsourceEnum.YES.getValue().equals(byId.getOutsourceStatus()) ? byId.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    @Transactional
    public Result forward(DeviceRepairDispatchDto dto, TenantIsolation tenantIsolation) {
        Long id = dto.getId();
        DeviceRepairEntity byId = this.getById(id);
        Result<Long> existRes = this.validOrderExist(byId, tenantIsolation);
        if (!existRes.getSignal()) {
            return existRes;
        }
        if (!ObjectUtil.equals(OrderStatusEnum.EXECUTING.getValue(), byId.getStatus())) {
            return Result.error("转交失败，工单处于执行状态才能转交！");
        }
        Result<Void> forwardRes = orderProcessService.forward(tenantIsolation.getTenantId(), id, byId.getProcessInstanceId(), new ResponsibleDto(dto.getReceiveUserId(), dto.getReceiveUserName() + "(" + dto.getWorkGroupName() + ")", false), dto.getRemark(), OrderTypeEnum.REPAIR.getValue());
        if (!forwardRes.getSignal()) {
            throw new BizException(forwardRes.getMessage());
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getReceiveUserId, dto.getReceiveUserId())
                .eq(DeviceRepairEntity::getId, dto.getId())
        );

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(byId.getProcessInstanceId(), byId.getOrderNumber(),
                byId.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, OrderStatusEnum.EXECUTING,
                TimeoutEnum.typeOfValue(byId.getTimeout()), OutsourceEnum.YES.getValue().equals(byId.getOutsourceStatus()) ? byId.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    public Result tempExecute(DeviceRepairProgressDto dto, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(dto.getDeviceRepairId())) {
            return Result.error("请传入维修工单id！");
        }
        Long id = dto.getDeviceRepairId();
        DeviceRepairEntity byId = this.getById(id);

        Result<Long> executeAuth = this.validExecuteAuth(byId, tenantIsolation);
        if (!executeAuth.getSignal()) {
            return executeAuth;
        }
        List<WorkloadEntity> workloadList = dto.getWorkloadList();
        // 删除人员工作量
        Integer deleteCount = workloadMapper.deleteByOrderId(OrderTypeEnum.REPAIR.getValue(), id);
        if (CollUtil.isNotEmpty(workloadList)) {
            // 插入人员工作量
            for (WorkloadEntity t : workloadList) {
                t.setId(null);
                t.setOrderId(id);
                t.setOrderType(OrderTypeEnum.REPAIR.getValue());
                LocalDateTime beginTime = t.getBeginTime();
                LocalDateTime endTime = t.getEndTime();
                if (beginTime != null && endTime != null) {
                    if (beginTime.isAfter(endTime)) {
                        throw new RuntimeException("开始时间不能大于结束时间");
                    }
                    Long minutes = ChronoUnit.MINUTES.between(beginTime, endTime);
                    t.setCostTime(minutes.intValue());
                }
                workloadMapper.insert(t);
            }
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getFaultType, dto.getFaultType())
                .set(DeviceRepairEntity::getFaultReason, dto.getFaultReason())
                .set(DeviceRepairEntity::getRepairProcess, dto.getRepairProcess())
                .set(DeviceRepairEntity::getRepairSaveExp, dto.getRepairSaveExp())
                .set(DeviceRepairEntity::getExecuteDeviceBomId, dto.getExecuteDeviceBomId())
                .set(DeviceRepairEntity::getExecuteDeviceBomName, dto.getExecuteDeviceBomName())
                .set(DeviceRepairEntity::getInnerDeviceTypeId, dto.getInnerDeviceTypeId())
                .set(DeviceRepairEntity::getSupportSupplierId, dto.getSupportSupplierId())
                .eq(DeviceRepairEntity::getId, dto.getDeviceRepairId())
        );
        return Result.ok();
    }

    private Result<Long> validExecuteAuth(DeviceRepairEntity byId, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(byId)) {
            return Result.error("找不到维修工单");
        }
        boolean innerOrderFlag = ObjectUtil.equals(DictConstant.NOT_OUTSOURCE, byId.getOutsourceStatus());
        if (innerOrderFlag) {
            if (!ObjectUtil.equals(byId.getTenantId(), tenantIsolation.getTenantId())) {
                return Result.error("找不到内部维修工单");
            }
        } else {
            if (ObjectUtil.equals(byId.getOutsourceSupplierType(), 1) && !ObjectUtil.equals(byId.getOutsourceSupplierId(), tenantIsolation.getTenantId())) {
                return Result.error("找不到外委维修工单");
            }
        }
        if (!ObjectUtil.equals(OrderStatusEnum.EXECUTING.getValue(), byId.getStatus())) {
            return Result.error("执行维修失败，工单不是维修中状态");
        }
        return Result.ok();
    }

    private Result saveRepairExp(DeviceRepairProgressDto dto, TenantIsolation tenantIsolation, DeviceRepairEntity byId, DeviceTypeEntity deviceTypeEntity, String deviceName) {
        if (ObjectUtil.equals(dto.getRepairSaveExp(), DictConstant.REPAIR_SAVE_EXP)) {

            DeviceRepairExpDto build = DeviceRepairExpDto.builder().deviceId(byId.getDeviceId())
                    .deviceTypeId(deviceTypeEntity.getId())
                    .deviceTypeName(deviceTypeEntity.getTypeName())
                    .faultType(dto.getFaultType())
                    .faultReason(dto.getFaultReason())
                    .deviceBomId(dto.getExecuteDeviceBomId())
                    .deviceBomName(dto.getExecuteDeviceBomName())
                    .repairProcess(dto.getRepairProcess()).build();
            // 客户身份执行维修才加入设备名称
            if (ObjectUtil.equals(IdTypeEnum.CUSTOMER.getValue(), tenantIsolation.getIdType())) {
                build.setDeviceName(deviceName);
            }
            deviceRepairExpService.saveDeviceRepairExp(build, tenantIsolation);
        }
        return Result.ok();
    }

    @Override
    public Result accept(DeviceRepairProgressDto dto, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(dto.getDeviceRepairId()) || ObjectUtil.isNull(dto.getAcceptState()) || ObjectUtil.isNull(dto.getAcceptExplain())) {
            return Result.error("请完整填写表单！");
        }
        Long id = dto.getDeviceRepairId();
        DeviceRepairEntity byId = this.getById(id);

        if (ObjectUtil.isNull(byId)) {
            return Result.error("找不到维修工单");
        }
        boolean innerOrderFlag = ObjectUtil.equals(DictConstant.NOT_OUTSOURCE, byId.getOutsourceStatus());
        if (innerOrderFlag) {
            if (!ObjectUtil.equals(byId.getTenantId(), tenantIsolation.getTenantId())) {
                return Result.error("找不到内部维修工单");
            }
        }
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Long userId = JwtUserInfoUtils.getUserId();
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();

        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;
        boolean isOutsourceAndNoData = ObjectUtil.equals(byId.getOutsourceCustomerType(), 1) && ObjectUtil.equals(byId.getOutsourceSupplierType(), 1) && ObjectUtil.equals(byId.getOutsourceStatus(), DictConstant.HAS_OUTSOURCE);
        Result<Void> completeRes = Result.error();
        if (isOutsourceAndNoData && IdTypeEnum.SUPPLIER.getValue().equals(tenantIsolation.getIdType())) {
            completeRes = orderProcessService.vendorAcceptance(byId.getOutsourceSupplierId(), id, byId.getProcessInstanceId(), dto.getAcceptExplain(), OrderTypeEnum.REPAIR.getValue(), isSuperRole);

            // 发送站内信
            asyncSendNoticeService.sendOrderNoticeSingle(byId.getProcessInstanceId(), byId.getOrderNumber(),
                    byId.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, OrderStatusEnum.WAIT_ACCEPT,
                    TimeoutEnum.typeOfValue(byId.getTimeout()),null);
        } else {
            completeRes = orderProcessService.complete(isOutsourceAndNoData ? byId.getOutsourceSupplierId() : tenantIsolation.getTenantId(), id, byId.getProcessInstanceId(), ObjectUtil.equals(dto.getAcceptState(), DictConstant.ACCEPT_PASS), dto.getAcceptExplain(), OrderTypeEnum.REPAIR.getValue(), isSuperRole);
        }
        if (!completeRes.getSignal()) {
            throw new BizException(completeRes.getMessage());
        }
        // 创建工单的租户id和当前租户一致才进行验收状态修改
        if (tenantIsolation.getTenantId().equals(byId.getTenantId())) {
            // 更新维修单据
            this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                    .set(DeviceRepairEntity::getStatus, ObjectUtil.equals(dto.getAcceptState(), DictConstant.ACCEPT_PASS) ? OrderStatusEnum.ACCEPTED.getValue() : OrderStatusEnum.REJECT.getValue())
                    .set(DeviceRepairEntity::getRepairEndTime, LocalDateTime.now())
                    .eq(DeviceRepairEntity::getId, dto.getDeviceRepairId())
            );
        } else {
            // 外委 && 自定义租户类型 && 创建方是供应商  则代表是自定义客户工单
            if (ObjectUtil.equals(byId.getOutsourceCustomerType(), 2)) {
                // 更新维修单据
                this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                        .set(DeviceRepairEntity::getStatus, ObjectUtil.equals(dto.getAcceptState(), DictConstant.ACCEPT_PASS) ? OrderStatusEnum.ACCEPTED.getValue() : OrderStatusEnum.REJECT.getValue())
                        .set(DeviceRepairEntity::getRepairEndTime, LocalDateTime.now())
                        .eq(DeviceRepairEntity::getId, dto.getDeviceRepairId())
                );
            }
        }

        // 处理工单告警记录
        alarmRecordService.finishAlarm(id, OrderTypeEnum.REPAIR.getValue());

        return Result.ok();
    }

    @Override
    public Result undo(DeviceRepairUndoDto dto, TenantIsolation tenantIsolation) {
        Long id = dto.getId();
        DeviceRepairEntity byId = this.getById(id);
        Result<Long> existRes = this.validOrderExist(byId, tenantIsolation);
        if (!existRes.getSignal()) {
            return existRes;
        }
        Integer orderStatus = byId.getStatus();
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.typeOfValue(orderStatus);
        if (!OrderStatusEnum.EXECUTING.getValue().equals(orderStatusEnum.getValue()) && !OrderStatusEnum.WAIT_ACCEPT.getValue().equals(orderStatusEnum.getValue())) {
            return Result.error("只有执行中和待验收的工单才有撤回操作！");
        }
        String operationName = "";
        Integer newStatus = null;
        Boolean isAutoDispatch = false;
        Object variable = runtimeService.getVariable(byId.getProcessInstanceId(), AUTO_DISPATCH_FLAG);
        if (variable != null) {
            List<SelectValue> selectValues = JsonUtil.parseArray(JsonUtil.toJSONString(variable), SelectValue.class);
            if (CollUtil.isNotEmpty(selectValues)){
               isAutoDispatch = ObjectUtil.equals(selectValues.get(0).getValue(),"1");
            }
        }
        if (OrderStatusEnum.EXECUTING.getValue().equals(orderStatusEnum.getValue())) {
            if (isAutoDispatch){
                operationName = OrderProcessOperationEnum.WITHDRAW_RECEIVE.getValue();
                newStatus = OrderStatusEnum.WAIT_RECEIVE.getValue();
            } else {
                operationName = OrderProcessOperationEnum.WITHDRAW_DISPATCH.getValue();
                newStatus = OrderStatusEnum.WAIT_DISPATCH.getValue();
            }
        }
        if (OrderStatusEnum.WAIT_ACCEPT.getValue().equals(orderStatusEnum.getValue())) {
            operationName = OrderProcessOperationEnum.WITHDRAW_EXECUTE.getValue();
            newStatus = OrderStatusEnum.EXECUTING.getValue();
        }
        Result<Void> withdrawResult = orderProcessService.withdrawTask(tenantIsolation.getTenantId(), id, byId.getProcessInstanceId(), null, OrderTypeEnum.REPAIR.getValue(), operationName);
        if (!withdrawResult.getSignal()) {
            throw new BizException(withdrawResult.getMessage());
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getStatus, newStatus)
                .eq(DeviceRepairEntity::getId, dto.getId())
        );

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(byId.getProcessInstanceId(), byId.getOrderNumber(),
                byId.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, OrderStatusEnum.typeOfValue(newStatus),
                TimeoutEnum.typeOfValue(byId.getTimeout()), OutsourceEnum.YES.getValue().equals(byId.getOutsourceStatus()) ? byId.getCustomerName() : null);

        return Result.ok();
    }

    @Override
    public Result reject(DeviceRepairRejectDto dto, TenantIsolation tenantIsolation) {
        Long id = dto.getId();
        DeviceRepairEntity byId = this.getById(id);
        if (ObjectUtil.isNull(byId)) {
            return Result.error("找不到维修工单");
        }
        boolean innerOrderFlag = ObjectUtil.equals(DictConstant.NOT_OUTSOURCE, byId.getOutsourceStatus());
        if (innerOrderFlag) {
            if (!ObjectUtil.equals(byId.getTenantId(), tenantIsolation.getTenantId())) {
                return Result.error("找不到内部维修工单");
            }
        }
        Integer orderStatus = byId.getStatus();
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.typeOfValue(orderStatus);
        if (!OrderStatusEnum.WAIT_ACCEPT.getValue().equals(orderStatusEnum.getValue())) {
            return Result.error("只有待验收的工单才有驳回操作！");
        }
        Result<Void> rejectRes = Result.error();
        boolean isOutsourceAndNoData = ObjectUtil.equals(byId.getOutsourceSupplierType(), 1) && ObjectUtil.equals(byId.getOutsourceStatus(), DictConstant.HAS_OUTSOURCE);
        if (isOutsourceAndNoData) {
            rejectRes = orderProcessService.rejectedTask(byId.getOutsourceSupplierId(), id, byId.getProcessInstanceId(), dto.getRemark(), OrderTypeEnum.REPAIR.getValue(), isOutsourceAndNoData, IdTypeEnum.typeOfValue(tenantIsolation.getIdType()));
        } else {
            rejectRes = orderProcessService.rejectedTask(tenantIsolation.getTenantId(), id, byId.getProcessInstanceId(), dto.getRemark(), OrderTypeEnum.REPAIR.getValue());
        }
        if (!rejectRes.getSignal()) {
            throw new BizException(rejectRes.getMessage());
        }
        Integer newStatus = OrderStatusEnum.EXECUTING.getValue();
        // 租户型外委工单，且当前身份是客户  执行验收驳回时判断供应商验收是否为自动，如果不是自动，则驳回后仍然为待验收状态
        if (isOutsourceAndNoData && IdTypeEnum.CUSTOMER.getValue().equals(tenantIsolation.getIdType())) {
            Result<Boolean> isAutoAcceptRes = orderProcessService.queryVendorAutoAcceptByProcessInstanceId(byId.getProcessInstanceId());
            Boolean isAutoAccept = isAutoAcceptRes.getResult();
            if (!isAutoAccept) {
                newStatus = OrderStatusEnum.WAIT_ACCEPT.getValue();
            }
        }
        // 更新维修单据
        this.update(Wrappers.<DeviceRepairEntity>lambdaUpdate()
                .set(DeviceRepairEntity::getStatus, newStatus)
                .eq(DeviceRepairEntity::getId, dto.getId())
        );

        // 发送站内信通知
        asyncSendNoticeService.sendOrderNoticeSingle(byId.getProcessInstanceId(), byId.getOrderNumber(),
                byId.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, OrderStatusEnum.typeOfValue(newStatus),
                TimeoutEnum.typeOfValue(byId.getTimeout()), OutsourceEnum.YES.getValue().equals(byId.getOutsourceStatus()) ? byId.getCustomerName() : null);

        return Result.ok();
    }

    private Result<Long> validAcceptAuth(DeviceRepairEntity byId, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isNull(byId)) {
            return Result.error("找不到维修工单");
        }
        if (!ObjectUtil.equals(OrderStatusEnum.WAIT_ACCEPT.getValue(), byId.getStatus())) {
            return Result.error("验收失败，工单不是待验收状态");
        }
        // 如果是内部验收
        if (ObjectUtil.equals(byId.getTenantId(), tenantIsolation.getTenantId())) {
            if (!ObjectUtil.equals(OrderStatusEnum.WAIT_ACCEPT.getValue(), byId.getStatus())) {
                return Result.error("验收失败，工单不是待验收状态");
            }
        }
        // 如果是外委的供应商验收
        if (ObjectUtil.equals(byId.getOutsourceSupplierId(), tenantIsolation.getTenantId())) {
            return Result.error("供应商无法执行验收动作!");
        }
        return Result.ok();
    }

    @Override
    public Result<DeviceRepairVo> getDetail(Long id, TenantIsolation tenantIsolation) {
        DeviceRepairVo deviceRepairVo = deviceRepairMapper.getVoById(id);
        if (ObjectUtil.isNull(deviceRepairVo)) {
            return Result.error("未找到维修工单！");
        }
        // 设置报修故障图片
        QueryWrapper<FileEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_id", id);
        List<FileEntity> fileEntities = fileMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(fileEntities)) {
            Map<Integer, List<FileEntity>> fileCollect = fileEntities.stream().collect(Collectors.groupingBy(FileEntity::getBizType));
            fileCollect.forEach((k, v) -> {
                FileBizTypeEnum fileBizTypeEnum = FileBizTypeEnum.typeOfValue(k);
                switch (fileBizTypeEnum) {
                    case DEVICE_REPAIR_PICTURE:
                        deviceRepairVo.setFaultImages(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    case DEVICE_EXECUTE_REPAIR_PICTURE:
                        deviceRepairVo.setRepairImages(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    case DEVICE_REPAIR_VIDEO:
                        deviceRepairVo.setFaultVideos(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    case DEVICE_REPAIR_DATA_ATTACHMENT:
                        deviceRepairVo.setFileList(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    default:
                        log.warn("设备维修不支持的文件类型");
                }
            });
        }
        // 查询设备维修进度
        Result<List<OrderProcessRecordEntity>> listByProcessInstanceId = orderProcessRecordService.getListByProcessInstanceId(tenantIsolation.getTenantId(), deviceRepairVo.getProcessInstanceId());
        deviceRepairVo.setOrderProcessRecordList(listByProcessInstanceId.getResult());

        if (ObjectUtil.isNotNull(deviceRepairVo.getFaultType())) {
            FaultTypeEntity faultTypeEntity = faultTypeService.getById(deviceRepairVo.getFaultType());
            if (ObjectUtil.isNotNull(faultTypeEntity)) {
                deviceRepairVo.setFaultTypeName(faultTypeEntity.getTypeName());
            }
        }

        // 查询当前用户所有待办任务信息
        Long userId = JwtUserInfoUtils.getUserId();
        Result<List<TaskDto>> listResult = orderProcessService.queryTodoTaskList(userId);
        if (!listResult.getSignal()) {
            throw new BizException(listResult.getMessage());
        }
        List<TaskDto> todoTaskDtoList = listResult.getResult();
        List<String> todoProcessInstanceIdList = todoTaskDtoList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
        // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
        List<String> processInstanceIdList = Arrays.asList(deviceRepairVo.getProcessInstanceId());
        Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(processInstanceIdList);
        if (!mapResult.getSignal()) {
            throw new BizException(mapResult.getMessage());
        }
        Map<String, String> lastProcessMap = mapResult.getResult();
        Result<Map<String, Boolean>> forwardMapRes = orderProcessService.queryForwardPermissionByProcessInstances(processInstanceIdList);
        if (!forwardMapRes.getSignal()) {
            throw new BizException(forwardMapRes.getMessage());
        }
        Map<String, Boolean> forwardMap = forwardMapRes.getResult();
        // 是否是超级角色
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
        Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
        if (!haveAuthResult.getSignal()) {
            throw new BizException(haveAuthResult.getMessage());
        }
        Boolean isReportHandleRole = haveAuthResult.getResult();

        Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;

        Result<Map<String, Boolean>> acceptMapResult = orderProcessService.queryAcceptStateByProcessInstances(processInstanceIdList, tenantIsolation.getIdType());
        if (!acceptMapResult.getSignal()) {
            throw new BizException(acceptMapResult.getMessage());
        }
        Map<String, Boolean> acceptMap = acceptMapResult.getResult();
        String lastUserId = lastProcessMap.get(deviceRepairVo.getProcessInstanceId());
        this.validRepairOrderOperationAuth(
                deviceRepairVo,
                todoProcessInstanceIdList.contains(deviceRepairVo.getProcessInstanceId()),
                ObjectUtil.equals(lastUserId, tenantIsolation.getTenantId() + ":" + userId.toString()),
                userId,
                forwardMap.getOrDefault(deviceRepairVo.getProcessInstanceId(), false),
                isSuperRole,
                acceptMap,
                tenantIsolation.getIdType());

        //获取人员工作量
        List<WorkloadEntity> workloadList = workloadMapper.listByOrderId(OrderTypeEnum.REPAIR.getValue(), id);
        deviceRepairVo.setWorkloadList(workloadList);
        return Result.ok(deviceRepairVo);

    }

    @Override
    public Result copy(DeviceRepairCopyDto dto, TenantIsolation tenantIsolation) {
        DeviceRepairEntity entity = this.getById(dto.getDeviceRepairId());
        if (ObjectUtil.isNull(entity)) {
            return Result.error("找不到原始客户工单！");
        }
        DeviceRepairEntity repeat = this.getOne(Wrappers.<DeviceRepairEntity>lambdaQuery()
                .notIn(DeviceRepairEntity::getStatus, Arrays.asList(OrderStatusEnum.REVOKE.getValue(), OrderStatusEnum.REJECT.getValue(), OrderStatusEnum.ACCEPTED.getValue()))
                .eq(DeviceRepairEntity::getOutsourceOriginId, dto.getDeviceRepairId())
                .eq(DeviceRepairEntity::getOutsourceStatus, DictConstant.HAS_OUTSOURCE)
                .eq(DeviceRepairEntity::getDeleted, 0)
                .last(" limit 1 ")
        );
        if (ObjectUtil.isNotNull(repeat)) {
            return Result.error("当前工单正在被外委中，不可重复外委！");
        }
        long id = IdGenerator.generateId();
        LocalDate today = LocalDate.now();
        String serialNumber = serialNumberService.getNext(tenantIsolation.getTenantId(), today, DeviceRepair.SERIALNUMBER);
        Result<String> tenantNameResult = userCenterService.getTenantNameById(tenantIsolation.getTenantId());
        if (!tenantNameResult.getSignal()) {
            return Result.error(tenantNameResult.getMessage());
        }
        String tenantName = tenantNameResult.getResult();
        entity.setId(id)
                .setOrderNumber(serialNumber)
                .setOutsourceStatus(DictConstant.HAS_OUTSOURCE)
                .setAggregatorOutsource(YesNoEnum.YES.getValue())
                .setOutsourceSupplierId(dto.getOutsourceSupplierId())
                .setOutsourceSupplierName(dto.getOutsourceSupplierName())
                .setOutsourceOriginId(dto.getDeviceRepairId())
                .setStatus(OrderStatusEnum.WAIT_DISPATCH.getValue())
                .setCustomerName(tenantName)
                .setReceiveUserId(null)
                .setFaultType(null)
                .setFaultReason(null)
                .setRepairProcess(null)
                .setRepairStartTime(null)
                .setRepairEndTime(null)
                .setRepairUseTime(null)
                .setRepairSaveExp(null)
                .setCreatorId(null)
                .setCreator(null)
                .setCreateTime(null)
                .setUpdatorId(null)
                .setUpdator(null)
                .setUpdateTime(null)
                .setTimeout(TimeoutEnum.NORMAL.getValue())
                .setTenantId(tenantIsolation.getTenantId());
        DeviceRepairDto deviceRepairDto = BeanUtilsIntensifier.copyBean(entity, DeviceRepairDto.class);
        Result<Object> error = this.validOutsourceCreateAuth(deviceRepairDto, tenantIsolation);
        if (!error.getSignal()) {
            return error;
        }
        Result<StartFlowDto> startFlowDtoResult = orderProcessService.startFlow(
                entity.getTenantId(),
                OrderTypeEnum.REPAIR.getValue(),
                id,
                new ResponsibleDto(JwtUserInfoUtils.getUserId(), JwtUserInfoUtils.getUserName(), false),
                entity.getOutsourceSupplierId(),
                false
                , entity.getAggregatorOutsource());
        if (!startFlowDtoResult.getSignal()) {
            throw new BizException(startFlowDtoResult.getMessage());
        }
        entity.setProcessInstanceId(startFlowDtoResult.getResult().getProcessInstanceId());
        if (startFlowDtoResult.getResult().getIsAutoDispatch()){
            entity.setStatus(OrderStatusEnum.WAIT_RECEIVE.getValue());
        }
        this.save(entity);

        QueryWrapper<FileEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_id", dto.getDeviceRepairId());
        List<FileEntity> fileEntities = fileMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(fileEntities)) {
            Map<Integer, List<FileEntity>> fileCollect = fileEntities.stream().collect(Collectors.groupingBy(FileEntity::getBizType));
            fileCollect.forEach((k, v) -> {
                FileBizTypeEnum fileBizTypeEnum = FileBizTypeEnum.typeOfValue(k);
                switch (fileBizTypeEnum) {
                    case DEVICE_REPAIR_PICTURE:
                        if (CollectionUtil.isNotEmpty(v)) {
                            v.forEach(item -> {
                                item.setId(IdGenerator.generateId());
                                item.setBizId(id);
                                item.setBizType(FileBizTypeEnum.DEVICE_REPAIR_PICTURE.getValue());
                                item.setTenantId(tenantIsolation.getTenantId());
                                fileMapper.insert(item);
                            });
                        }
                        break;
                   case DEVICE_REPAIR_VIDEO:
                       if (CollectionUtil.isNotEmpty(v)) {
                           v.forEach(item -> {
                               item.setId(IdGenerator.generateId());
                               item.setBizId(id);
                               item.setBizType(FileBizTypeEnum.DEVICE_REPAIR_VIDEO.getValue());
                               item.setTenantId(tenantIsolation.getTenantId());
                               fileMapper.insert(item);
                           });
                       }
                       break;
                    default:
                        log.warn("设备维修复制工单时不支持的文件类型");
                }
            });
        }

        // 发送站内信
        asyncSendNoticeService.sendOrderNoticeSingle(entity.getProcessInstanceId(), entity.getOrderNumber(),
                entity.getOutsourceStatus(), tenantIsolation.getTenantId(), tenantIsolation.getIdType(), OrderTypeEnum.REPAIR, startFlowDtoResult.getResult().getIsAutoDispatch()?OrderStatusEnum.WAIT_RECEIVE:OrderStatusEnum.WAIT_DISPATCH,
                TimeoutEnum.typeOfValue(entity.getTimeout()), OutsourceEnum.YES.getValue().equals(entity.getOutsourceStatus()) ? entity.getCustomerName() : null);


        return Result.ok();
    }

    /**
     * 查询客户主页 维修工单数量统计信息
     *
     * @param tenantIsolation
     * @return
     */
    @Override
    public Result queryStatusCount(TenantIsolation tenantIsolation) {
        if (!ObjectUtil.equals(tenantIsolation.getIdType(), DictConstant.ID_TYPE_CUSTOMER)) {
            log.info("查询客户主页【维修工单数量统计信息】只能以客户身份进行！");
            return Result.ok();
        }
        List<DeviceRepairEntity> list = this.list(Wrappers.<DeviceRepairEntity>lambdaQuery()
                .eq(DeviceRepairEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceRepairEntity::getDeleted, 0)
        );
        return getDeviceRepairCustomerStatDtoResult(list);
    }

    /**
     * 查询集成商主页 维修工单数量统计信息
     *
     * @param tenantIsolation
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public Result queryStatusCountAggregator(TenantIsolation tenantIsolation, String startDate, String endDate) {
        // 查询外委委派给当前租户的工单
        List<DeviceRepairEntity> list = this.list(Wrappers.<DeviceRepairEntity>lambdaQuery()
                .eq(DeviceRepairEntity::getOutsourceSupplierId, tenantIsolation.getTenantId())
                .eq(DeviceRepairEntity::getOutsourceStatus, DictConstant.HAS_OUTSOURCE)
                .lt(DeviceRepairEntity::getCreateTime, endDate)
                .gt(DeviceRepairEntity::getCreateTime, startDate)
                .eq(DeviceRepairEntity::getDeleted, 0)
        );
        return getDeviceRepairCustomerStatDtoResult(list);
    }

    private Result getDeviceRepairCustomerStatDtoResult(List<DeviceRepairEntity> list) {
        Map<Integer, List<DeviceRepairEntity>> collect = list.stream().collect(Collectors.groupingBy(DeviceRepairEntity::getStatus));
        int unassignedCount = collect.getOrDefault(OrderStatusEnum.WAIT_DISPATCH.getValue(), new ArrayList<>()).size();
        int inRepairCount = collect.getOrDefault(OrderStatusEnum.EXECUTING.getValue(), new ArrayList<>()).size();
        int notAcceptCount = collect.getOrDefault(OrderStatusEnum.WAIT_ACCEPT.getValue(), new ArrayList<>()).size();
        Set<Integer> statusSet = new HashSet<>();
        statusSet.add(OrderStatusEnum.WAIT_DISPATCH.getValue());
        statusSet.add(OrderStatusEnum.EXECUTING.getValue());
        statusSet.add(OrderStatusEnum.WAIT_ACCEPT.getValue());
        Map<Integer, List<DeviceRepairEntity>> outSourceStatusMap = list.stream().filter(i ->
                statusSet.contains(i.getStatus())
        ).collect(Collectors.groupingBy(DeviceRepairEntity::getOutsourceStatus));
        DeviceRepairCustomerStatDto deviceRepairCustomerStatDto = DeviceRepairCustomerStatDto.builder()
                .allWaitProcessCount(unassignedCount + inRepairCount + notAcceptCount)
                .waitProcessInsourceCount(outSourceStatusMap.getOrDefault(DictConstant.NOT_OUTSOURCE, new ArrayList<>()).size())
                .waitProcessOutsourceCount(outSourceStatusMap.getOrDefault(DictConstant.HAS_OUTSOURCE, new ArrayList<>()).size())
                .waitDispatchCount(unassignedCount)
                .repairingCount(inRepairCount)
                .waitAcceptCount(notAcceptCount).build();
        return Result.ok(deviceRepairCustomerStatDto);
    }


    /**
     * 查询主页 维修工单工作日历数据
     *
     * @param tenantIsolation
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public Result<Map<String, Integer>> queryCalendarCount(TenantIsolation tenantIsolation, String startDate, String endDate) {
        Map<String, Integer> map = new HashMap<>();
        List<DeviceRepairEntity> list = new ArrayList<>();
        if (ObjectUtil.equals(tenantIsolation.getIdType(), DictConstant.ID_TYPE_CUSTOMER)) {
            list = this.list(Wrappers.<DeviceRepairEntity>lambdaQuery()
                    .eq(DeviceRepairEntity::getTenantId, tenantIsolation.getTenantId())
                    .lt(DeviceRepairEntity::getCreateTime, endDate)
                    .gt(DeviceRepairEntity::getCreateTime, startDate)
                    .eq(DeviceRepairEntity::getDeleted, 0)
            );
        } else {
            //查询建立关系的租户
            List<Long> customerIds = new ArrayList<>();
            CustomerRelationEntity customerRelationEntity = new CustomerRelationEntity();
            customerRelationEntity.setSupplierId(tenantIsolation.getTenantId());
            customerRelationEntity.setIdType(tenantIsolation.getIdType());
            customerRelationEntity.setStatus(RelationStatusEnum.APPROVE.getValue());
            List<CustomerRelationEntity> customerRelationEntities = customerRelationMapper.listCustomerByEntity(customerRelationEntity);
            if (CollUtil.isNotEmpty(customerRelationEntities)) {
                customerIds = customerRelationEntities.stream().map(CustomerRelationEntity::getCustomerId).collect(Collectors.toList());
            }
            if (CollUtil.isEmpty(customerIds)) {
                return Result.ok(map);
            }
            list = this.list(Wrappers.<DeviceRepairEntity>lambdaQuery()
                    .lt(DeviceRepairEntity::getCreateTime, endDate)
                    .gt(DeviceRepairEntity::getCreateTime, startDate)
                    .in(DeviceRepairEntity::getTenantId, customerIds)
                    .eq(DeviceRepairEntity::getOutsourceSupplierId, tenantIsolation.getTenantId())
                    .eq(DeviceRepairEntity::getOutsourceStatus, DictConstant.HAS_OUTSOURCE)
                    .eq(DeviceRepairEntity::getDeleted, 0)
            );
        }
        Map<String, List<DeviceRepairEntity>> collect = list.stream().collect(Collectors.groupingBy(i ->
                DateUtil.formatDate(
                        Date.from(
                                i.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()
                        )
                )
        ));
        collect.forEach((k, v) -> {
            map.put(k, v.size());
        });
        return Result.ok(map);
    }

    /**
     * 查询供应商主页 所有待维修的工单数量
     *
     * @param tenantIsolation
     * @return
     */
    @Override
    public Result<Integer> querySupplierAllWaitRepairCount(TenantIsolation tenantIsolation) {
        if (!ObjectUtil.equals(tenantIsolation.getIdType(), DictConstant.ID_TYPE_SUPPLIER)) {
            log.info("查询供应商主页【所有待维修的工单数量】只能以供应商身份进行！");
            return Result.ok();
        }
        // 查询未删除、外委、外委供应商为当前租户、状态处于待分派和待接收的单据
        List<DeviceRepairEntity> list = this.list(Wrappers.<DeviceRepairEntity>lambdaQuery()
                .in(DeviceRepairEntity::getStatus, Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(), OrderStatusEnum.WAIT_ACCEPT.getValue(), OrderStatusEnum.EXECUTING.getValue()))
                .eq(DeviceRepairEntity::getOutsourceSupplierId, tenantIsolation.getTenantId())
                .eq(DeviceRepairEntity::getOutsourceStatus, DictConstant.HAS_OUTSOURCE)
                .eq(DeviceRepairEntity::getDeleted, 0)
        );
        return Result.ok(list.size());
    }


    /**
     * 查询供应商主页 近30天客户报修工单数TOP10
     *
     * @param tenantIsolation
     * @return
     */
    @Override
    public Result<List<DeviceRepairSupplierStatDto>> queryCustomerRepairCountTop10(TenantIsolation tenantIsolation) {
        if (!ObjectUtil.equals(tenantIsolation.getIdType(), DictConstant.ID_TYPE_SUPPLIER)) {
            log.info("查询供应商主页【近30天客户报修工单数TOP10】只能以供应商身份进行！");
            return Result.ok();
        }
        LocalDateTime beginDay = DateUtil.offsetDay(new Date(), -30).toLocalDateTime();
        LocalDateTime endDay = LocalDateTime.now();
        DeviceRepairQueryDto queryDto = DeviceRepairQueryDto.builder().tenantId(tenantIsolation.getTenantId())
                .startDate(beginDay)
                .idType(tenantIsolation.getIdType())
                .endDate(endDay).build();
        // 查询未删除、外委、外委供应商为当前租户、状态不为撤销、且创建时间为当月的维修工单
        List<DeviceRepairDto> deviceRepairDtoList = deviceRepairMapper.queryRepairSupplierStat(queryDto);
        Map<String, List<DeviceRepairDto>> collect = deviceRepairDtoList.stream().filter(i -> StrUtil.isNotBlank(i.getCustomerName())).collect(Collectors.groupingBy(i -> i.getCustomerName()));
        List<DeviceRepairSupplierStatDto> statDtoList = new ArrayList<>();
        collect.forEach((k, v) -> {
            DeviceRepairSupplierStatDto build = DeviceRepairSupplierStatDto.builder().customerName(k).repairTimes(v.size()).build();
            statDtoList.add(build);
        });
        CollectionUtil.sort(statDtoList, Comparator.comparing(
                DeviceRepairSupplierStatDto::getRepairTimes
        ).reversed());
        List<DeviceRepairSupplierStatDto> subList = ListUtil.sub(statDtoList, 0, 10);
        return Result.ok(subList);
    }

    /**
     * 查询供应商主页  近30天设备报修次数TOP10
     *
     * @param tenantIsolation
     * @return
     */
    @Override
    public Result<List<DeviceRepairSupplierStatDto>> queryDeviceRepairCountTop10(TenantIsolation tenantIsolation) {
        if (!ObjectUtil.equals(tenantIsolation.getIdType(), DictConstant.ID_TYPE_SUPPLIER)) {
            log.info("查询供应商主页【近30天设备报修次数TOP10】只能以供应商身份进行！");
            return Result.ok();
        }
        LocalDateTime beginDay = DateUtil.offsetDay(new Date(), -30).toLocalDateTime();
        LocalDateTime endDay = LocalDateTime.now();
        DeviceRepairQueryDto queryDto = DeviceRepairQueryDto.builder().tenantId(tenantIsolation.getTenantId())
                .startDate(beginDay)
                .idType(tenantIsolation.getIdType())
                .endDate(endDay).build();
        // 查询未删除、外委、外委供应商为当前租户、状态不为撤销、且创建时间为当月的维修工单
        List<DeviceRepairDto> deviceRepairDtoList = deviceRepairMapper.queryRepairSupplierStat(queryDto);
        Map<String, List<DeviceRepairDto>> collect = deviceRepairDtoList.stream().filter(i -> StrUtil.isNotBlank(i.getCustomerName())).collect(Collectors.groupingBy(i -> i.getCustomerName() + "#@#@" + i.getDeviceName()));
        List<DeviceRepairSupplierStatDto> statDtoList = new ArrayList<>();
        collect.forEach((k, v) -> {
            String[] split = k.split("#@#@");
            DeviceRepairSupplierStatDto build = DeviceRepairSupplierStatDto.builder()
                    .customerName(split[0])
                    .deviceName(split[1])
                    .repairTimes(v.size()).build();
            statDtoList.add(build);
        });
        CollectionUtil.sort(statDtoList, Comparator.comparing(
                DeviceRepairSupplierStatDto::getRepairTimes
        ).reversed().thenComparing(DeviceRepairSupplierStatDto::getCustomerName));
        List<DeviceRepairSupplierStatDto> subList = ListUtil.sub(statDtoList, 0, 10);
        return Result.ok(subList);
    }

    @Override
    public Result queryCustomerServiceOverview(TenantIsolation tenantIsolation) {
        Long tenantId = tenantIsolation.getTenantId();
        CustomerServiceOverviewVo vo = CustomerServiceOverviewVo.builder()
                .weekCustomerCount(deviceRepairMapper.queryServiceCustomerCount(tenantId, 1))
                .monthCustomerCount(deviceRepairMapper.queryServiceCustomerCount(tenantId, 2))
                .weekOrderCount(deviceRepairMapper.queryServiceOrderCount(tenantId, 1, false))
                .monthOrderCount(deviceRepairMapper.queryServiceOrderCount(tenantId, 2, false))
                .weekFinishOrderCount(deviceRepairMapper.queryServiceOrderCount(tenantId, 1, true))
                .monthFinishOrderCount(deviceRepairMapper.queryServiceOrderCount(tenantId, 2, true))
                .build();
        return Result.ok(vo);
    }

    /**
     * 查询供应商主页  日期范围内维修工单数量
     *
     * @param tenantIsolation
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public Result<Map<String, Integer>> querySupplierDateCount(TenantIsolation tenantIsolation, Date startDate, Date endDate) {
        Map<String, Integer> map = new HashMap<>();
        if (!ObjectUtil.equals(tenantIsolation.getIdType(), DictConstant.ID_TYPE_SUPPLIER)) {
            log.info("查询供应商主页【维修工单工作日历数据】只能以供应商身份进行！");
            return Result.ok();
        }

        //查询建立关系的租户
        List<Long> customerIds = new ArrayList<>();
        CustomerRelationEntity customerRelationEntity = new CustomerRelationEntity();
        customerRelationEntity.setSupplierId(tenantIsolation.getTenantId());
        customerRelationEntity.setIdType(tenantIsolation.getIdType());
        customerRelationEntity.setStatus(RelationStatusEnum.APPROVE.getValue());
        List<CustomerRelationEntity> customerRelationEntities = customerRelationMapper.listCustomerByEntity(customerRelationEntity);
        if (CollUtil.isNotEmpty(customerRelationEntities)) {
            customerIds = customerRelationEntities.stream().map(CustomerRelationEntity::getCustomerId).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(customerIds)) {
            return Result.ok(map);
        }
        List<DeviceRepairEntity> list = this.list(Wrappers.<DeviceRepairEntity>lambdaQuery()
                .ne(DeviceRepairEntity::getStatus, OrderStatusEnum.REVOKE.getValue())
                .lt(DeviceRepairEntity::getCreateTime, endDate)
                .gt(DeviceRepairEntity::getCreateTime, startDate)
                .in(DeviceRepairEntity::getTenantId, customerIds)
                .eq(DeviceRepairEntity::getOutsourceSupplierId, tenantIsolation.getTenantId())
                .eq(DeviceRepairEntity::getOutsourceStatus, DictConstant.HAS_OUTSOURCE)
                .eq(DeviceRepairEntity::getDeleted, 0)
        );
        Map<String, List<DeviceRepairEntity>> collect = list.stream().collect(Collectors.groupingBy(i ->
                DateUtil.formatDate(
                        Date.from(
                                i.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()
                        )
                )
        ));
        collect.forEach((k, v) -> {
            map.put(k, v.size());
        });
        return Result.ok(map);
    }

    @Override
    public Result<DeviceCountVo> countDeviceOrder(TenantIsolation tenantIsolation, Long deviceId) {
        DeviceCountVo deviceCountVo = new DeviceCountVo();
        Result<DeviceMaintenanceCountVo> deviceMaintenanceCountVoResult = maintenanceOrderService.countDeviceMaintenanceOrder(tenantIsolation, deviceId);
        if (deviceMaintenanceCountVoResult.getSignal()) {
            deviceCountVo.setOutSourceMaintenanceCount(deviceMaintenanceCountVoResult.getResult().getOutsourceCount());
            deviceCountVo.setInSourceMaintenanceCount(deviceMaintenanceCountVoResult.getResult().getInsourceCount());
        }
        Result<DeviceInspectCountDto> deviceInspectCountDtoResult = inspectOrderService.countDeviceInspectOrder(tenantIsolation.getTenantId(), tenantIsolation.getIdType(), deviceId);
        if (deviceInspectCountDtoResult.getSignal()) {
            deviceCountVo.setOutSourceInspectCount(deviceInspectCountDtoResult.getResult().getOutsourceCount());
            deviceCountVo.setInSourceInspectCount(deviceInspectCountDtoResult.getResult().getInsourceCount());
        }
        Page<DeviceRepairQueryDto> page = new Page<>(1, 10);
        DeviceRepairQueryDto queryDto = new DeviceRepairQueryDto();
        queryDto.setTenantId(tenantIsolation.getTenantId());
        queryDto.setIdType(tenantIsolation.getIdType());
        queryDto.setDeviceId(deviceId);
        queryDto.setOutsourceStatus(DictConstant.NOT_OUTSOURCE);
        Page<DeviceRepairDto> innerPage = deviceRepairMapper.queryRepairByCondition(page, queryDto);
        deviceCountVo.setInSourceRepairCount((int) innerPage.getTotal());
        queryDto.setOutsourceStatus(DictConstant.HAS_OUTSOURCE);
        Page<DeviceRepairDto> outPage = deviceRepairMapper.queryRepairByCondition(page, queryDto);
        deviceCountVo.setOutSourceRepairCount((int) outPage.getTotal());
        return Result.ok(deviceCountVo);
    }

    @Autowired
    private MaintenanceResultMapper maintenanceResultMapper;

    @Autowired
    private InspectResultMapper inspectResultMapper;
    @Autowired
    private InspectOrderMapper inspectOrderMapper;

    @Override
    public Result<DeviceOrderRecordVo> getDeviceOrderRecord(TenantIsolation tenantIsolation, Long deviceId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DeviceOrderRecordVo deviceOrderRecordVo = new DeviceOrderRecordVo();
        List<DeviceRepairEntity> repairList = this.lambdaQuery().eq(DeviceRepairEntity::getDeviceId, deviceId)
                .in(DeviceRepairEntity::getStatus, Lists.newArrayList(OrderStatusEnum.ACCEPTED.getValue(), OrderStatusEnum.REJECT.getValue()))
                .eq(ObjectUtil.equals(tenantIsolation.getIdType(), IdTypeEnum.SUPPLIER.getValue()), DeviceRepairEntity::getOutsourceStatus, OutsourceEnum.YES.getValue())
                .orderByDesc(DeviceRepairEntity::getUpdateTime)
                .list();
        deviceOrderRecordVo.setRepairCount(repairList.size());
        if (CollUtil.isNotEmpty(repairList)) {
            deviceOrderRecordVo.setLastRepairTime(repairList.get(0).getUpdateTime().format(formatter));
        }
        LocalDateTime now = LocalDateTime.now();
        QueryWrapper<MaintenanceResultEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", deviceId);
        List<Long> maintenanceOrderIdList = maintenanceResultMapper.selectList(queryWrapper).stream().map(i -> i.getMaintenanceOrderId()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(maintenanceOrderIdList)) {
            List<MaintenanceOrderEntity> finishMaintenanceOrderList = maintenanceOrderService.lambdaQuery().in(MaintenanceOrderEntity::getId, maintenanceOrderIdList)
                    .in(MaintenanceOrderEntity::getStatus, Lists.newArrayList(OrderStatusEnum.ACCEPTED.getValue(), OrderStatusEnum.REJECT.getValue()))
                    .eq(ObjectUtil.equals(tenantIsolation.getIdType(), IdTypeEnum.SUPPLIER.getValue()), MaintenanceOrderEntity::getOutsource, OutsourceEnum.YES.getValue())
                    .list();
            deviceOrderRecordVo.setMaintenanceCount(finishMaintenanceOrderList.size());
            List<MaintenanceOrderEntity> notFinishMaintenanceOrderList = maintenanceOrderService.lambdaQuery().in(MaintenanceOrderEntity::getId, maintenanceOrderIdList)
                    .in(MaintenanceOrderEntity::getStatus, Lists.newArrayList(OrderStatusEnum.WAIT_DISPATCH.getValue(), OrderStatusEnum.EXECUTING.getValue(), OrderStatusEnum.WAIT_ACCEPT.getValue()))
                    .eq(ObjectUtil.equals(tenantIsolation.getIdType(), IdTypeEnum.SUPPLIER.getValue()), MaintenanceOrderEntity::getOutsource, OutsourceEnum.YES.getValue())
                    .orderByAsc(MaintenanceOrderEntity::getMaintenanceBegin)
                    .list();
            if (CollUtil.isNotEmpty(notFinishMaintenanceOrderList)) {
                LocalDateTime maintenanceBegin = notFinishMaintenanceOrderList.get(0).getPlanMaintenanceTime();
                deviceOrderRecordVo.setNextMaintenanceTime(maintenanceBegin.format(formatter));
                deviceOrderRecordVo.setNextMaintenanceIsExpire(maintenanceBegin.compareTo(now) < 0);
            }
        }
        QueryWrapper<InspectResultEntity> inspectWrapper = new QueryWrapper<>();
        inspectWrapper.eq("device_id", deviceId);
        List<Long> inspectOrderIdList = inspectResultMapper.selectList(inspectWrapper).stream().map(i -> i.getInspectOrderId()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(inspectOrderIdList)) {
            List<InspectOrderEntity> finishInspectOrderList = inspectOrderMapper.selectList(
                    new QueryWrapper<InspectOrderEntity>().in("id", inspectOrderIdList)
                            .eq(ObjectUtil.equals(tenantIsolation.getIdType(), IdTypeEnum.SUPPLIER.getValue()), "outsource", OutsourceEnum.YES.getValue())
                            .in("status", Lists.newArrayList(OrderStatusEnum.ACCEPTED.getValue(), OrderStatusEnum.REJECT.getValue()))
            );
            deviceOrderRecordVo.setInspectCount(finishInspectOrderList.size());
            List<InspectOrderEntity> notFinishInspectOrderList = inspectOrderMapper.selectList(new QueryWrapper<InspectOrderEntity>().in("id", inspectOrderIdList)
                    .eq(ObjectUtil.equals(tenantIsolation.getIdType(), IdTypeEnum.SUPPLIER.getValue()), "outsource", OutsourceEnum.YES.getValue())
                    .in("status", Lists.newArrayList(OrderStatusEnum.WAIT_DISPATCH.getValue(), OrderStatusEnum.EXECUTING.getValue(), OrderStatusEnum.WAIT_ACCEPT.getValue()))
            );
            if (CollUtil.isNotEmpty(notFinishInspectOrderList)) {
                LocalDateTime inspectBegin = notFinishInspectOrderList.get(0).getPlanInspectTime();

                deviceOrderRecordVo.setNextInspectTime(inspectBegin.format(formatter));
                deviceOrderRecordVo.setNextInspectIsExpire(inspectBegin.compareTo(now) < 0);
            }
        }

        return Result.ok(deviceOrderRecordVo);
    }

    private void validRepairOrderOperationAuth(DeviceRepairDto dto, boolean isTodo, boolean canUndo, Long userId, boolean forwardFlag, Boolean isSuperRole, Map<String, Boolean> acceptMap, Integer idType) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.typeOfValue(dto.getStatus());
        Boolean isOutsource = OutsourceEnum.YES.getValue().equals(dto.getOutsourceStatus());
        Boolean isCustomer = IdTypeEnum.CUSTOMER.getValue().equals(idType);
        Integer tenantType = dto.getOutsourceSupplierType();
        Integer createSource = dto.getCreateSource();
        Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType);
        switch (orderStatusEnum) {
            case WAIT_DISPATCH:
                if (isSuperRole) {
                    if (isOutsource) {
                        if (isCustomer) {
                            if (isDataRecord) {
                                dto.getOperations().add(OperationEnum.DISPATCH.getName());
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                if (IdTypeEnum.CUSTOMER.getValue().equals(createSource)) {
                                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                                }
                                break;
                            }
                        } else {
                            dto.getOperations().add(OperationEnum.DISPATCH.getName());
                            if (IdTypeEnum.SUPPLIER.getValue().equals(createSource)) {
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                            }
                            break;
                        }
                    } else {
                        // 内部工单
                        dto.getOperations().add(OperationEnum.DISPATCH.getName());
                        dto.getOperations().add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.DISPATCH.getName());
                }
                if (ObjectUtil.equals(userId, dto.getCreatorId())) {
                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                }
                break;
            case WAIT_RECEIVE:
                if (isSuperRole) {
                    if (isOutsource) {
                        if (isCustomer) {
                            if (isDataRecord) {
                                dto.getOperations().add(OperationEnum.RECEIVE.getName());
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                if (IdTypeEnum.CUSTOMER.getValue().equals(createSource)) {
                                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                                }
                                break;
                            }
                        } else {
                            dto.getOperations().add(OperationEnum.RECEIVE.getName());
                            if (IdTypeEnum.SUPPLIER.getValue().equals(createSource)) {
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                            }
                            break;
                        }
                    } else {
                        // 内部工单
                        dto.getOperations().add(OperationEnum.RECEIVE.getName());
                        dto.getOperations().add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.RECEIVE.getName());
                }
                if (ObjectUtil.equals(userId, dto.getCreatorId())) {
                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                }
                break;
            case EXECUTING:
                if (isSuperRole) {
                    if (isOutsource) {
                        if (isCustomer) {
                            if (!isDataRecord) {
                                // 当前是客户&&委外&&非数据型
                                break;
                            }
                        }
                    }
                    dto.getOperations().add(OperationEnum.EXECUTE.getName());
                    dto.getOperations().add(OperationEnum.FORWARD.getName());
                    dto.getOperations().add(OperationEnum.UNDO_DISPATCH.getName());
                    break;
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.EXECUTE.getName());
                    if (forwardFlag) {
                        dto.getOperations().add(OperationEnum.FORWARD.getName());
                    }
                }
                if (canUndo) {
                    dto.getOperations().add(OperationEnum.UNDO_DISPATCH.getName());
                }
                break;
            case WAIT_ACCEPT:
                if (isSuperRole) {
                    if (isOutsource) {
                        // 外委客户身份
                        if (isCustomer) {
                            dto.getOperations().add(OperationEnum.ACCEPT.getName());
                            dto.getOperations().add(OperationEnum.REJECT.getName());
                            if (isDataRecord) {
                                // 当前是客户&&委外&&数据型
                                dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                                break;
                            } else {
                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i -> i.get(dto.getProcessInstanceId())).orElse(false)) {
                                    dto.getOperations().clear();
                                }
                                break;
                            }
                        } else {
                            // 外委工单的 供应商身份  自带所有操作
                            dto.getOperations().add(OperationEnum.ACCEPT.getName());
                            dto.getOperations().add(OperationEnum.REJECT.getName());
                            dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                            // 供应商身份 判断验收按钮  需要单独对委外客户状态进行判断，如果委外客户状态不是自定义的，需要单独判断是否有权限，如果是自定义的则是走单独的流程，管理员一定有所有权限
                            if (!SupplierTypeEnum.SELF_DEFINING.getValue().equals(dto.getOutsourceCustomerType())) {
                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i -> i.get(dto.getProcessInstanceId())).orElse(false)) {
                                    dto.getOperations().clear();
                                }
                                break;
                            }
                            break;
                        }
                    }
                    // 超级管理员 的 内部工单  赋予全部权限
                    dto.getOperations().add(OperationEnum.ACCEPT.getName());
                    dto.getOperations().add(OperationEnum.REJECT.getName());
                    dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                    break;
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.ACCEPT.getName());
                    dto.getOperations().add(OperationEnum.REJECT.getName());
                }
                if (canUndo) {
                    dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                }
                break;
            default:
                log.warn("当前状态无操作权限，不进行操作权限判断！");
        }
    }

    private void validRepairOrderOperationAuth(DeviceRepairVo dto, boolean isTodo, boolean canUndo, Long userId, boolean forwardFlag, Boolean isSuperRole, Map<String, Boolean> acceptMap, Integer idType) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.typeOfValue(dto.getStatus());
        Boolean isOutsource = OutsourceEnum.YES.getValue().equals(dto.getOutsourceStatus());
        Boolean isCustomer = IdTypeEnum.CUSTOMER.getValue().equals(idType);
        Integer tenantType = dto.getOutsourceSupplierType();
        Integer createSource = dto.getCreateSource();
        Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType);
        switch (orderStatusEnum) {
            case WAIT_DISPATCH:
                if (isSuperRole) {
                    if (isOutsource) {
                        if (isCustomer) {
                            if (isDataRecord) {
                                dto.getOperations().add(OperationEnum.DISPATCH.getName());
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                if (IdTypeEnum.CUSTOMER.getValue().equals(createSource)) {
                                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                                }
                                break;
                            }
                        } else {
                            dto.getOperations().add(OperationEnum.DISPATCH.getName());
                            if (IdTypeEnum.SUPPLIER.getValue().equals(createSource)) {
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                            }
                            break;
                        }
                    } else {
                        // 内部工单
                        dto.getOperations().add(OperationEnum.DISPATCH.getName());
                        dto.getOperations().add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.DISPATCH.getName());
                }
                if (ObjectUtil.equals(userId, dto.getCreatorId())) {
                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                }
                break;
            case WAIT_RECEIVE:
                if (isSuperRole) {
                    if (isOutsource) {
                        if (isCustomer) {
                            if (isDataRecord) {
                                dto.getOperations().add(OperationEnum.RECEIVE.getName());
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                                break;
                            } else {
                                if (IdTypeEnum.CUSTOMER.getValue().equals(createSource)) {
                                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                                }
                                break;
                            }
                        } else {
                            dto.getOperations().add(OperationEnum.RECEIVE.getName());
                            if (IdTypeEnum.SUPPLIER.getValue().equals(createSource)) {
                                dto.getOperations().add(OperationEnum.REVOKE.getName());
                            }
                            break;
                        }
                    } else {
                        // 内部工单
                        dto.getOperations().add(OperationEnum.RECEIVE.getName());
                        dto.getOperations().add(OperationEnum.REVOKE.getName());
                        break;
                    }
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.RECEIVE.getName());
                }
                if (ObjectUtil.equals(userId, dto.getCreatorId())) {
                    dto.getOperations().add(OperationEnum.REVOKE.getName());
                }
                break;
            case EXECUTING:
                if (isSuperRole) {
                    if (isOutsource) {
                        if (isCustomer) {
                            if (!isDataRecord) {
                                // 当前是客户&&委外&&非数据型
                                break;
                            }
                        }
                    }
                    dto.getOperations().add(OperationEnum.EXECUTE.getName());
                    dto.getOperations().add(OperationEnum.FORWARD.getName());
                    dto.getOperations().add(OperationEnum.UNDO_DISPATCH.getName());
                    break;
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.EXECUTE.getName());
                    if (forwardFlag) {
                        dto.getOperations().add(OperationEnum.FORWARD.getName());
                    }
                }
                if (canUndo) {
                    dto.getOperations().add(OperationEnum.UNDO_DISPATCH.getName());
                }
                break;
            case WAIT_ACCEPT:
                if (isSuperRole) {
                    if (isOutsource) {
                        // 外委客户身份
                        if (isCustomer) {
                            dto.getOperations().add(OperationEnum.ACCEPT.getName());
                            dto.getOperations().add(OperationEnum.REJECT.getName());
                            if (isDataRecord) {
                                // 当前是客户&&委外&&数据型
                                dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                                break;
                            } else {
                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i -> i.get(dto.getProcessInstanceId())).orElse(false)) {
                                    dto.getOperations().clear();
                                }
                                break;
                            }
                        } else {
                            // 外委工单的 供应商身份  自带所有操作
                            dto.getOperations().add(OperationEnum.ACCEPT.getName());
                            dto.getOperations().add(OperationEnum.REJECT.getName());
                            dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                            // 供应商身份 判断验收按钮  需要单独对委外客户状态进行判断，如果委外客户状态不是自定义的，需要单独判断是否有权限，如果是自定义的则是走单独的流程，管理员一定有所有权限
                            if (!SupplierTypeEnum.SELF_DEFINING.getValue().equals(dto.getOutsourceCustomerType())) {
                                // 外委工单非数据型工单，需要进行当前验收状态判断，如果传入的验收MAP不为空  且验收状态不对，去除所有按钮
                                if (!Optional.ofNullable(acceptMap).map(i -> i.get(dto.getProcessInstanceId())).orElse(false)) {
                                    dto.getOperations().clear();
                                }
                                break;
                            }
                            break;
                        }
                    }
                    // 超级管理员 的 内部工单  赋予全部权限
                    dto.getOperations().add(OperationEnum.ACCEPT.getName());
                    dto.getOperations().add(OperationEnum.REJECT.getName());
                    dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                    break;
                }
                if (isTodo) {
                    dto.getOperations().add(OperationEnum.ACCEPT.getName());
                    dto.getOperations().add(OperationEnum.REJECT.getName());
                }
                if (canUndo) {
                    dto.getOperations().add(OperationEnum.UNDO_EXECUTE.getName());
                }
                break;
            default:
                log.warn("当前状态无操作权限，不进行操作权限判断！");
        }
    }

    private void validRepairOrderRevokeAuth(DeviceRepairDto dto, TenantIsolation tenantIsolation) {
        DeviceRepairEntity entity = BeanUtilsIntensifier.copyBean(dto, DeviceRepairEntity.class);
        Result<Long> hasRevokeAuth = this.validRevokeAuth(entity, tenantIsolation);
        if (hasRevokeAuth.getSignal()) {
            if (CollUtil.isEmpty(dto.getOperations())) {
                List<String> authList = new ArrayList<>();
                authList.add(OperationEnum.REVOKE.getName());
                dto.setOperations(authList);
            } else {
                dto.getOperations().add(OperationEnum.REVOKE.getName());
            }
        }
    }

    /**
     * 管理员角色查询所有工单
     *
     * @param queryDto
     * @param page
     * @param tenantIsolation
     * @return
     */
    public Result<DeviceRepairOrderPage> pageOrderByAdmin(DeviceRepairQueryDto queryDto, Page<DeviceRepairQueryDto> page, TenantIsolation tenantIsolation) {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        queryDto.setTenantId(tenantId);
        queryDto.setIdType(idType);
        Page<DeviceRepairDto> repairDtoPage = deviceRepairMapper.queryRepairByCondition(page, queryDto);
        if (CollUtil.isNotEmpty(repairDtoPage.getRecords())) {
            List<Long> repairIdList = repairDtoPage.getRecords().stream().map(i -> i.getId()).collect(Collectors.toList());
            QueryWrapper<WorkloadEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("order_type", OrderTypeEnum.REPAIR.getValue());
            wrapper.in("order_id", repairIdList);
            wrapper.eq("deleted", 0);
            List<WorkloadEntity> workloadEntityList = workloadMapper.selectList(wrapper);
            Map<Long, List<WorkloadEntity>> workLoadMap = workloadEntityList.stream().collect(Collectors.groupingBy(i -> i.getOrderId()));

            QueryWrapper<FileEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("biz_id", repairIdList);
            List<FileEntity> fileEntities = fileMapper.selectList(queryWrapper);
            Map<Long, List<FileEntity>> fileCollect = fileEntities.stream().collect(Collectors.groupingBy(FileEntity::getBizId));
            // 查询当前用户所有待办任务信息
            Long userId = JwtUserInfoUtils.getUserId();
            Result<List<TaskDto>> listResult = orderProcessService.queryTodoTaskList(userId);
            if (!listResult.getSignal()) {
                throw new BizException(listResult.getMessage());
            }
            List<TaskDto> todoTaskDtoList = listResult.getResult();
            List<String> todoProcessInstanceIdList = todoTaskDtoList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
            // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
            List<String> processInstanceIdList = repairDtoPage.getRecords().stream().map(i -> i.getProcessInstanceId()).collect(Collectors.toList());
            Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(processInstanceIdList);
            if (!mapResult.getSignal()) {
                throw new BizException(mapResult.getMessage());
            }
            Map<String, String> lastProcessMap = mapResult.getResult();
            Result<Map<String, Boolean>> forwardMapRes = orderProcessService.queryForwardPermissionByProcessInstances(processInstanceIdList);
            if (!forwardMapRes.getSignal()) {
                throw new BizException(forwardMapRes.getMessage());
            }
            Map<String, Boolean> forwardMap = forwardMapRes.getResult();
            // 是否是超级角色
            Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenantIsolation);
            Result<Boolean> haveAuthResult = userRoleAuthService.haveAuthReportRepair(tenantIsolation.getTenantId(), userId, tenantIsolation.getIdType());
            if (!haveAuthResult.getSignal()) {
                throw new BizException(haveAuthResult.getMessage());
            }
            Boolean isReportHandleRole = haveAuthResult.getResult();

            Boolean isSuperRole = currentUserIsAdministrator || isReportHandleRole;

            Result<Map<String, Boolean>> acceptMapResult = orderProcessService.queryAcceptStateByProcessInstances(processInstanceIdList, idType);
            if (!acceptMapResult.getSignal()) {
                throw new BizException(acceptMapResult.getMessage());
            }
            Map<String, Boolean> acceptMap = acceptMapResult.getResult();
            repairDtoPage.getRecords().forEach(item -> {
                item.setWorkloadList(workLoadMap.getOrDefault(item.getId(), new ArrayList<>()));
                String lastUserId = lastProcessMap.get(item.getProcessInstanceId());
                this.validRepairOrderOperationAuth(item, todoProcessInstanceIdList.contains(item.getProcessInstanceId()), ObjectUtil.equals(lastUserId, tenantId + ":" + userId.toString()), userId, forwardMap.getOrDefault(item.getProcessInstanceId(), false), isSuperRole, acceptMap, idType);
                item.setStatusName(Optional.ofNullable(OrderStatusEnum.typeOfValue(item.getStatus())).map(OrderStatusEnum::getName).orElse(""));
                List<FileVo> fileVos = BeanUtil.copyToList(fileCollect.getOrDefault(item.getId(), new ArrayList<>()), FileVo.class);
                item.setIsSuperRole(isSuperRole);
                item.setFaultImages(fileVos.stream()
                        .filter(i -> FileBizTypeEnum.DEVICE_REPAIR_PICTURE.getValue().equals(i.getBizType()))
                        .sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                item.setFaultVideos(fileVos.stream()
                        .filter(i -> FileBizTypeEnum.DEVICE_REPAIR_VIDEO.getValue().equals(i.getBizType()))
                        .sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
            });
        }
        queryDto.setStatusList(null);
        List<DeviceRepairDto> repairDtoList = deviceRepairMapper.listRepairByCondition(queryDto);
        Multiset<Integer> counter = HashMultiset.create();
        if (repairDtoList != null) {
            repairDtoList.forEach(t -> {
                counter.add(t.getStatus());
            });
        }
        OrderStatusCountVo countVo = new OrderStatusCountVo();
        countVo.setWaitDispatchCount(counter.count(OrderStatusEnum.WAIT_DISPATCH.getValue()) );
        countVo.setWaitReceiveCount(counter.count(OrderStatusEnum.WAIT_RECEIVE.getValue()) );
        countVo.setExecutingCount(counter.count(OrderStatusEnum.EXECUTING.getValue()));
        countVo.setWaitAcceptCount(counter.count(OrderStatusEnum.WAIT_ACCEPT.getValue()));
        countVo.setAcceptedCount(counter.count(OrderStatusEnum.ACCEPTED.getValue()));
        countVo.setRejectCount(counter.count(OrderStatusEnum.REJECT.getValue()));
        countVo.setRevokeCount(counter.count(OrderStatusEnum.REVOKE.getValue()));
        DeviceRepairOrderPage deviceRepairOrderPage = BeanUtilsIntensifier.copyBean(repairDtoPage, DeviceRepairOrderPage.class);
        deviceRepairOrderPage.setOrderStatusCount(countVo);
        return Result.ok(deviceRepairOrderPage);
    }

    /**
     * 查询我经手的工单
     *
     * @param queryDto
     * @param page
     * @param tenantIsolation
     * @return
     */
    public Result<Page<DeviceRepairDto>> pageOrderWithMe(DeviceRepairQueryDto queryDto, Page<DeviceRepairQueryDto> page, TenantIsolation tenantIsolation) {
        Long userId = JwtUserInfoUtils.getUserId();
        queryDto.setTenantId(tenantIsolation.getTenantId());
        queryDto.setExecuteUserId(userId);
        queryDto.setIdType(tenantIsolation.getIdType());
        Page<DeviceRepairDto> repairDtoPage = deviceRepairMapper.queryRepairWithMe(page, queryDto);
        // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
        List<String> processInstanceIdList = repairDtoPage.getRecords().stream().map(i -> i.getProcessInstanceId()).collect(Collectors.toList());
        Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(processInstanceIdList);
        if (!mapResult.getSignal()) {
            throw new BizException(mapResult.getMessage());
        }
        Map<String, String> lastProcessMap = mapResult.getResult();
        repairDtoPage.getRecords().forEach(item -> {
            String lastUserId = lastProcessMap.get(item.getProcessInstanceId());
            this.validRepairOrderOperationAuth(item, false, ObjectUtil.equals(lastUserId, tenantIsolation.getTenantId() + ":" + userId.toString()), userId, false, false, null, tenantIsolation.getIdType());
            item.setStatusName(Optional.ofNullable(OrderStatusEnum.typeOfValue(item.getStatus())).map(OrderStatusEnum::getName).orElse(""));
        });
        return Result.ok(repairDtoPage);
    }

    /**
     * 查询个人待处理维修工单数量
     * inSourceRepairCount-内部待处理数量、outSourceRepairCount-委外待处理数量
     *
     * @param tenantIsolation
     * @return
     */
    @Override
    public DeviceCountVo getWaitRepairCount(TenantIsolation tenantIsolation) {
        DeviceCountVo deviceCountVo = new DeviceCountVo();
        Page<DeviceRepairQueryDto> page = new Page<>(1, 10);
        DeviceRepairQueryDto queryDto = new DeviceRepairQueryDto();
        queryDto.setOutsourceStatus(DictConstant.NOT_OUTSOURCE);
        Page<DeviceRepairDto> innerPage = this.getWaitProcessPage(queryDto, page, tenantIsolation);
        deviceCountVo.setInSourceRepairCount((int) innerPage.getTotal());
        DeviceRepairQueryDto queryDto2 = new DeviceRepairQueryDto();
        queryDto2.setOutsourceStatus(DictConstant.HAS_OUTSOURCE);
        Page<DeviceRepairDto> outPage = this.getWaitProcessPage(queryDto2, page, tenantIsolation);
        deviceCountVo.setOutSourceRepairCount((int) outPage.getTotal());
        return deviceCountVo;
    }

    private Page<DeviceRepairDto> getWaitProcessPage(DeviceRepairQueryDto queryDto, Page<DeviceRepairQueryDto> page, TenantIsolation tenantIsolation) {
        // 获取当前用户拥有的角色信息
        Integer idType = tenantIsolation.getIdType();
        Long userId = JwtUserInfoUtils.getUserId();
        Result<List<TaskDto>> listResult = orderProcessService.queryTodoTaskList(userId);
        if (!listResult.getSignal()) {
            throw new BizException(listResult.getMessage());
        }
        List<TaskDto> todoTaskDtoList = listResult.getResult();
        if (CollUtil.isEmpty(todoTaskDtoList)) {
            return new Page<>(page.getCurrent(), page.getPages());
        }
        List<String> todoProcessInstanceIdList = todoTaskDtoList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
        queryDto.setTodoProcessInstanceIdList(todoProcessInstanceIdList);
        queryDto.setTenantId(tenantIsolation.getTenantId());
        queryDto.setIdType(idType);
        return deviceRepairMapper.queryRepairWithAuth(page, queryDto);
    }

    /**
     * 查询待处理的工单
     *
     * @param queryDto
     * @param page
     * @param tenantIsolation
     * @return
     */
    public Result<Page<DeviceRepairDto>> pageOrderWaitProcess(DeviceRepairQueryDto queryDto, Page<DeviceRepairQueryDto> page, TenantIsolation tenantIsolation) {
        // 获取当前用户拥有的角色信息
        Long userId = JwtUserInfoUtils.getUserId();
        Result<List<TaskDto>> listResult = orderProcessService.queryTodoTaskList(userId);
        if (!listResult.getSignal()) {
            throw new BizException(listResult.getMessage());
        }
        List<TaskDto> todoTaskDtoList = listResult.getResult();
        if (CollUtil.isEmpty(todoTaskDtoList)) {
            return Result.ok(new Page<>(page.getCurrent(), page.getPages()));
        }
        List<String> todoProcessInstanceIdList = todoTaskDtoList.stream().map(TaskDto::getProcessInstanceId).collect(Collectors.toList());
        queryDto.setTodoProcessInstanceIdList(todoProcessInstanceIdList);
        queryDto.setTenantId(tenantIsolation.getTenantId());
        queryDto.setIdType(tenantIsolation.getIdType());
        Page<DeviceRepairDto> repairDtoPage = deviceRepairMapper.queryRepairWithAuth(page, queryDto);
        if (CollUtil.isNotEmpty(repairDtoPage.getRecords())) {
            List<Long> repairIdList = repairDtoPage.getRecords().stream().map(i -> i.getId()).collect(Collectors.toList());
            QueryWrapper<WorkloadEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("order_type", OrderTypeEnum.REPAIR.getValue());
            wrapper.in("order_id", repairIdList);
            wrapper.eq("deleted", 0);
            List<WorkloadEntity> workloadEntityList = workloadMapper.selectList(wrapper);
            Map<Long, List<WorkloadEntity>> workLoadMap = workloadEntityList.stream().collect(Collectors.groupingBy(i -> i.getOrderId()));
            QueryWrapper<FileEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("biz_id", repairIdList);
            List<FileEntity> fileEntities = fileMapper.selectList(queryWrapper);
            Map<Long, List<FileEntity>> fileCollect = fileEntities.stream().collect(Collectors.groupingBy(FileEntity::getBizId));
            // 查询当前分页列表中最后一个执行人信息 判断是否可以撤回
            List<String> processInstanceIdList = repairDtoPage.getRecords().stream().map(i -> i.getProcessInstanceId()).collect(Collectors.toList());
            Result<Map<String, String>> mapResult = orderProcessService.queryLastOperatorByProcessInstances(processInstanceIdList);
            if (!mapResult.getSignal()) {
                throw new BizException(mapResult.getMessage());
            }
            Map<String, String> lastProcessMap = mapResult.getResult();

            Result<Map<String, Boolean>> forwardMapRes = orderProcessService.queryForwardPermissionByProcessInstances(processInstanceIdList);
            if (!forwardMapRes.getSignal()) {
                throw new BizException(forwardMapRes.getMessage());
            }
            Map<String, Boolean> forwardMap = forwardMapRes.getResult();

            repairDtoPage.getRecords().forEach(item -> {
                item.setWorkloadList(workLoadMap.getOrDefault(item.getId(), new ArrayList<>()));
                String lastUserId = lastProcessMap.get(item.getProcessInstanceId());
                this.validRepairOrderOperationAuth(item, true, ObjectUtil.equals(lastUserId, tenantIsolation.getTenantId() + ":" + userId.toString()), userId, forwardMap.getOrDefault(item.getProcessInstanceId(), false), false, null, tenantIsolation.getIdType());
                item.setStatusName(Optional.ofNullable(OrderStatusEnum.typeOfValue(item.getStatus())).map(OrderStatusEnum::getName).orElse(""));
                List<FileVo> fileVos = BeanUtil.copyToList(fileCollect.getOrDefault(item.getId(), new ArrayList<>()), FileVo.class);
                item.setFaultImages(fileVos.stream()
                        .filter(i -> FileBizTypeEnum.DEVICE_REPAIR_PICTURE.getValue().equals(i.getBizType()))
                        .sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                item.setFaultVideos(fileVos.stream()
                        .filter(i -> FileBizTypeEnum.DEVICE_REPAIR_VIDEO.getValue().equals(i.getBizType()))
                        .sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
            });
        }
        return Result.ok(repairDtoPage);
    }


    @Override
    public Result saveDataAttachment(TenantIsolation tenantIsolation, DeviceRepairSaveFileDto dto) {
        for (FileVo fileVo : dto.getFileList()) {
            FileEntity fileEntity = BeanUtilsIntensifier.copyBean(fileVo, FileEntity.class);
            fileEntity.setId(IdGenerator.generateId());
            fileEntity.setBizId(dto.getId());
            fileEntity.setBizType(FileBizTypeEnum.DEVICE_REPAIR_DATA_ATTACHMENT.getValue());
            fileEntity.setTenantId(tenantIsolation.getTenantId());
            fileMapper.insert(fileEntity);
        }
        return Result.ok();
    }

    /**
     * 工单超时监测
     *
     * @return
     */
    @Override
    @Transactional
    public Result orderTimeoutMonitor() {
        //查询所有未结束、未超时的保养工单
        List<DeviceRepairEntity> orderList = this.list(Wrappers.<DeviceRepairEntity>lambdaQuery()
                .eq(DeviceRepairEntity::getTimeout, TimeoutEnum.NORMAL.getValue())
                .and(wrapper -> wrapper.eq(DeviceRepairEntity::getOutsourceCustomerType, SupplierTypeEnum.TENANT.getValue())
                        .or().isNull(DeviceRepairEntity::getOutsourceCustomerType))
                .in(DeviceRepairEntity::getStatus, Arrays.asList(OrderStatusEnum.WAIT_DISPATCH.getValue(),
                        OrderStatusEnum.EXECUTING.getValue(),
                        OrderStatusEnum.WAIT_ACCEPT.getValue())));
        if (CollectionUtil.isEmpty(orderList)) {
            return Result.ok();
        }

        Map<Long, List<DeviceRepairEntity>> supplierOrderMap = orderList.stream()
                .filter(order -> IdTypeEnum.SUPPLIER.getValue().equals(order.getCreateSource()))
                .collect(Collectors.groupingBy(DeviceRepairEntity::getOutsourceSupplierId));
        Map<Long, List<DeviceRepairEntity>> customerOrderMap = orderList.stream()
                .filter(order -> IdTypeEnum.CUSTOMER.getValue().equals(order.getCreateSource()))
                .collect(Collectors.groupingBy(DeviceRepairEntity::getTenantId));

        // 合并所有需要处理的租户ID
        Set<Long> tenantIds = new HashSet<>();
        tenantIds.addAll(supplierOrderMap.keySet());
        tenantIds.addAll(customerOrderMap.keySet());

        //获取租户保养超时配置(启用)
        List<TimeoutConfigEntity> allTimeoutConfigList = timeoutConfigService.list(Wrappers.<TimeoutConfigEntity>lambdaQuery()
                .eq(TimeoutConfigEntity::getOrderType, OrderTypeEnum.REPAIR.getValue())
                .eq(TimeoutConfigEntity::getStatus, YesNoEnum.YES.getValue())
                .in(TimeoutConfigEntity::getTenantId, tenantIds));
        Map<Long, List<TimeoutConfigEntity>> tenantTimeoutMap = allTimeoutConfigList.stream()
                .collect(Collectors.groupingBy(TimeoutConfigEntity::getTenantId));

        LocalDateTime now = LocalDateTime.now();

        //遍历租户超时配置，判断工单是否超时
        tenantTimeoutMap.forEach((tenantId, tenantTimeoutList) -> {
            if (CollectionUtil.isEmpty(tenantTimeoutList)) {
                return;
            }
            List<DeviceRepairEntity> supplierOrders = supplierOrderMap.getOrDefault(tenantId, new ArrayList<>());
            List<DeviceRepairEntity> customerOrders = customerOrderMap.getOrDefault(tenantId, new ArrayList<>());

            List<DeviceRepairEntity> tenantOrderList = new ArrayList<>();
            tenantOrderList.addAll(supplierOrders);
            tenantOrderList.addAll(customerOrders);
            if (CollectionUtil.isEmpty(tenantOrderList)) {
                return;
            }
            handle(tenantTimeoutList, tenantOrderList, now);
        });
        return Result.ok();
    }

    private void handle(List<TimeoutConfigEntity> timeoutConfigList, List<DeviceRepairEntity> tenantOrderList,
                        LocalDateTime now) {
        Map<Integer, List<DeviceRepairEntity>> sourceOrderMap = tenantOrderList.stream()
                .collect(Collectors.groupingBy(DeviceRepairEntity::getOutsourceStatus));
        timeoutConfigList.forEach(timeoutConfig -> {
            Integer timeoutSecond = timeoutConfig.getTimeoutSecond();
            Integer configType = timeoutConfig.getConfigType();
            Long tenantId = timeoutConfig.getTenantId();
            Integer idType = timeoutConfig.getIdType();

            //获取工单通知渠道
            List<MessageStrategyConfigVo> messageNoticeConfigList = messageNoticeStrategyMapper.queryOrderTimeoutStrategyConfig(
                    timeoutConfig.getTenantId(), OrderTypeEnum.REPAIR.getValue(), configType, idType);

            List<DeviceRepairEntity> orderList = Lists.newArrayList();
            if (TimeoutConfigTypeEnum.INSIDE.getValue().equals(configType)) {
                //内部工单
                if (sourceOrderMap.get(OutsourceEnum.NO.getValue()) != null) {
                    orderList = sourceOrderMap.get(OutsourceEnum.NO.getValue());
                }
            } else if (TimeoutConfigTypeEnum.OUTSIDE.getValue().equals(configType)) {
                //外委工单
                if (sourceOrderMap.get(OutsourceEnum.YES.getValue()) != null) {
                    // 根据idType筛选createSource
                    orderList = sourceOrderMap.get(OutsourceEnum.YES.getValue()).stream()
                            .filter(order -> order.getCreateSource().equals(idType))
                            .collect(Collectors.toList());
                }
            }
            //超时判断
            orderList.forEach(order -> {
                LocalDateTime createTime = order.getCreateTime();
                //超时时间
                LocalDateTime timeoutTime = createTime.plusSeconds(timeoutSecond);
                if (now.isAfter(timeoutTime)) {
                    //设置超时
                    deviceRepairMapper.orderTimeout(order.getId());

                    // 保存告警记录
                    saveAlramRecord(order, now, tenantId, idType);

                    //发送通知
                    if (CollectionUtil.isEmpty(messageNoticeConfigList)) {
                        return;
                    }
                    messageNoticeConfigList.forEach(messageNoticeConfig -> sendMessageNotice(order, messageNoticeConfig, now));
                }
            });
        });
    }

    private void saveAlramRecord(DeviceRepairEntity deviceRepair, LocalDateTime alarmTime,
                                 Long tenantId, Integer idType) {
        AlarmRecordEntity alarmRecord = new AlarmRecordEntity();
        alarmRecord.setOrderId(deviceRepair.getId());
        alarmRecord.setAlarmType(AlarmTypeEnum.ORDER_TIMEOUT.getValue());
        alarmRecord.setAlarmTime(alarmTime);
        alarmRecord.setOrderNo(deviceRepair.getOrderNumber());
        alarmRecord.setOrderCustomerId(deviceRepair.getTenantId());
        alarmRecord.setOrderSource(OrderTypeEnum.REPAIR.getValue());
        alarmRecord.setOrderStatus(deviceRepair.getStatus());
        alarmRecord.setTenantId(tenantId);
        alarmRecord.setIdType(idType);
        alarmRecord.setCreatorId(1L);
        alarmRecord.setCreator("定时任务");
        Result saveAlarmResult = alarmRecordService.saveAlarmRecord(alarmRecord);
        if (!saveAlarmResult.getSignal()) {
            log.error("保存维修工单超时告警记录失败：{} {}", deviceRepair.getId(), saveAlarmResult.getMessage());
        }
    }

    private void sendMessageNotice(DeviceRepairEntity deviceRepair, MessageStrategyConfigVo messageNoticeConfig, LocalDateTime now) {
        Integer idType = messageNoticeConfig.getIdType();
        String customerName = "";
        if (IdTypeEnum.SUPPLIER.getValue().equals(idType) && IdTypeEnum.SUPPLIER.getValue().equals(deviceRepair.getCreateSource())) {
            customerName = deviceRepair.getCustomerName();
        }

        OrderMessageDto orderMessageDto = OrderMessageDto.builder()
                .orderTypeEnum(OrderTypeEnum.REPAIR)
                .orderStatusEnum(OrderStatusEnum.typeOfValue(deviceRepair.getStatus()))
                .messageTypeEnum(MessageTypeEnum.ORDER_TIMEOUT)
                .timeoutEnum(TimeoutEnum.TIMEOUT)
                .orderNo(deviceRepair.getOrderNumber())
                .outsource(deviceRepair.getOutsourceStatus())
                .tenantId(messageNoticeConfig.getTenantId())
                .idType(messageNoticeConfig.getIdType())
                .customerName(customerName)
                .now(now)
                .build();

        asyncSendNoticeService.sendOrderTimeoutNotice(messageNoticeConfig, orderMessageDto);
    }

}
