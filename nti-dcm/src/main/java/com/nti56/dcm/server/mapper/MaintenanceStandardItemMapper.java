package com.nti56.dcm.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nti56.dcm.server.entity.MaintenanceStandardItemEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 保养标准明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29 11:05:12
 */
public interface MaintenanceStandardItemMapper extends BaseMapper<MaintenanceStandardItemEntity> {

    @Select("SELECT * FROM maintenance_standard_item WHERE maintenance_standard_id = #{standardId} AND deleted = 0")
    List<MaintenanceStandardItemEntity> listByStandardId(@Param("standardId") Long standardId);

    void initTenantStandardItem(@Param("tenantId") Long tenantId, @Param("standardId") Long standardId);

    @Delete("DELETE FROM maintenance_standard_item WHERE maintenance_standard_module_id = #{moduleId}")
    void deleteByModuleId(@Param("moduleId") Long moduleId);

}
