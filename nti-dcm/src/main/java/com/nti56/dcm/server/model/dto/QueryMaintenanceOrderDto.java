package com.nti56.dcm.server.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/3/5 9:56<br/>
 * @since JDK 1.8
 */
@Data
public class QueryMaintenanceOrderDto {

    /**
     * 保养工单编号
     */
    private String orderNumber;

    /**
     * 保养计划编号
     */
    private String planNumber;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备编号
     */
    private String deviceNumber;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 工单状态，1-待分派，2-待接收，3-保养中，4-待验收，5-验收通过，6-验收不通过，7-已撤销
     */
    private Integer status;

    /**
     * 是否逾期 0-未逾期，1-逾期）
     */
    private Integer overdue;

    /**
     * 委外状态：0-内部，1-委外
     */
    private Integer outsource;

    /**
     * 委外供应商ID
     */
    private Long supplierId;

    /**
     * 保养时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime maintenanceTimeBegin;

    /**
     * 保养时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime maintenanceTimeEnd;

    /**
     * 保养结束时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime maintenanceFinishTimeBegin;

    /**
     * 保养结束时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime maintenanceFinishTimeEnd;

    /**
     * 身份，1-供应商，2-客户
     */
    private Integer idType;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 客户ID
     */
    private Long customerId;

    private List<Integer> statusList;

    /**
     * 创建来源 1-供应商/集成商 2-客户
     */
    private Integer createSource;

    @Schema(description = "工单最后更新时间 查询范围——开始时间")
    private LocalDateTime lastModifyStartDate;

    @Schema(description = "工单最后更新时间 查询范围——结束时间")
    private LocalDateTime lastModifyEndDate;

    /**
     * 计划等级 1-紧急 2-重要 3-普通 4-其他
     */
    private Integer level;

    /**
     * 是否超时 1-正常 2-超时
     */
    private Integer timeout;

    /**
     * 流程实例ID列表
     */
    private Set<String> processInstanceIdList;

    /**
     * 快速委外关联原始单据工单编号
     */
    private String outsourceOriginOrderNumber;
    /**
     * 快速委外关联原始单据工客户id
     */
    private Long outsourceOriginCustomerId;
}
