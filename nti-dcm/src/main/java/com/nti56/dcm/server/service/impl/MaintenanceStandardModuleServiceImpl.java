package com.nti56.dcm.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.dcm.server.entity.MaintenanceStandardModuleEntity;
import com.nti56.dcm.server.mapper.MaintenanceStandardItemMapper;
import com.nti56.dcm.server.mapper.MaintenanceStandardModuleMapper;
import com.nti56.dcm.server.service.IMaintenanceStandardModuleService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 保养标准模块表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-07-04 09:39:39
 * @since JDK 1.8
 */
@Service
public class MaintenanceStandardModuleServiceImpl extends ServiceImpl<MaintenanceStandardModuleMapper, MaintenanceStandardModuleEntity>
        implements IMaintenanceStandardModuleService {

    @Autowired
    MaintenanceStandardModuleMapper mapper;

    @Autowired
    private MaintenanceStandardItemMapper maintenanceStandardItemMapper;

    @Override
    public Result<MaintenanceStandardModuleEntity> create(TenantIsolation tenantIsolation, MaintenanceStandardModuleEntity entity) {
        if (!unique(null, tenantIsolation.getTenantId(), entity.getMaintenanceStandardId(), entity.getModuleName())) {
            return Result.error("模块名称不允许重复！");
        }
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<List<MaintenanceStandardModuleEntity>> listByStandardId(TenantIsolation tenantIsolation, Long standardId) {
        List<MaintenanceStandardModuleEntity> list = this.list(Wrappers.<MaintenanceStandardModuleEntity>lambdaQuery()
                .eq(MaintenanceStandardModuleEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(MaintenanceStandardModuleEntity::getMaintenanceStandardId, standardId));
        return Result.ok(list);
    }

    @Override
    public Result<Void> update(TenantIsolation tenantIsolation, MaintenanceStandardModuleEntity entity) {
        if (!unique(entity.getId(), tenantIsolation.getTenantId(), entity.getMaintenanceStandardId(), entity.getModuleName())) {
            return Result.error("模块名称不允许重复！");
        }
        if (mapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    @Transactional
    public Result<Void> deleteById(Long entityId) {
        if (mapper.deleteById(entityId) > 0) {
            maintenanceStandardItemMapper.deleteByModuleId(entityId);
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Long getModuleIdByModuleName(Long tenantId, Long standardId, String moduleName) {
        MaintenanceStandardModuleEntity moduleEntity = this.getOne(Wrappers.<MaintenanceStandardModuleEntity>lambdaQuery()
                .eq(MaintenanceStandardModuleEntity::getTenantId, tenantId)
                .eq(MaintenanceStandardModuleEntity::getMaintenanceStandardId, standardId)
                .eq(MaintenanceStandardModuleEntity::getModuleName, moduleName)
                .last("limit 1"));
        if (moduleEntity != null) {
            return moduleEntity.getId();
        }

        Long moduleId = IdGenerator.generateId();
        MaintenanceStandardModuleEntity newModule = MaintenanceStandardModuleEntity.builder()
                .id(moduleId)
                .maintenanceStandardId(standardId)
                .moduleName(moduleName)
                .tenantId(tenantId)
                .build();
        mapper.insert(newModule);
        return moduleId;
    }

    public boolean unique(Long id, Long tenantId, Long standardId, String name){
        Long count = new LambdaQueryChainWrapper<>(mapper)
                .ne(id != null, MaintenanceStandardModuleEntity::getId, id)
                .eq(MaintenanceStandardModuleEntity::getTenantId, tenantId)
                .eq(MaintenanceStandardModuleEntity::getMaintenanceStandardId, standardId)
                .eq(MaintenanceStandardModuleEntity::getModuleName, name)
                .count();
        if (count > 0) {
            return false;
        }
        return true;
    }

}
