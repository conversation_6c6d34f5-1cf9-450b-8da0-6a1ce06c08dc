package com.nti56.dcm.server.mapper;

import com.nti56.dcm.server.entity.InspectOrderEntity;
import com.nti56.dcm.server.entity.MaintenanceOrderEntity;
import com.nti56.dcm.server.model.dto.AuthDto;
import com.nti56.dcm.server.model.dto.InspectOrderDto;
import com.nti56.dcm.server.model.dto.InspectOrderParams;
import com.nti56.flowable.common.dto.TaskDto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.mybatis.MyBaseMapper;

/**
 * <p>
 * 点巡检工单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23 15:13:54
 */
public interface InspectOrderMapper extends MyBaseMapper<InspectOrderEntity> {


    List<InspectOrderEntity> listByPlanIds(@Param("planIds") List<Long> planIds);

    List<InspectOrderDto> listByParams(
        @Param("tenantId") Long tenantId, 
        @Param("idType") Integer idType, 
        @Param("params") InspectOrderParams params,
        @Param("lastModifyStartDateTime") LocalDateTime lastModifyStartDateTime, 
        @Param("lastModifyEndDateTime") LocalDateTime lastModifyEndDateTime
    );

    Page<InspectOrderDto> pageByParams(
        @Param("tenantId") Long tenantId, 
        @Param("idType") Integer idType, 
        @Param("params") InspectOrderParams params, 
        @Param("page") Page<InspectOrderEntity> page, 
        @Param("lastModifyStartDateTime") LocalDateTime lastModifyStartDateTime, 
        @Param("lastModifyEndDateTime") LocalDateTime lastModifyEndDateTime
    );

    @Update("UPDATE inspect_order SET status = #{status}, update_time = now(), inspect_end = now()  WHERE id = #{inspectOrderId}")
    Integer updateStatusById(@Param("inspectOrderId") Long inspectOrderId, @Param("status") Integer status);

    Page<InspectOrderDto> pageByAuth(
        @Param("tenantId") Long tenantId,
        @Param("idType") Integer idType,
        @Param("todoTaskList") List<TaskDto> todoTaskList, 
        @Param("executeUserId") Long executeUserId, 
        @Param("params") InspectOrderParams params,
        @Param("page") Page<InspectOrderEntity> page
    );

    List<InspectOrderDto> listByAuth(
        @Param("tenantId") Long tenantId,
        @Param("idType") Integer idType,
        @Param("todoTaskList") List<TaskDto> todoTaskList, 
        @Param("executeUserId") Long executeUserId, 
        @Param("params") InspectOrderParams params
    );

    Integer countByAuth(
        @Param("tenantId") Long tenantId,
        @Param("idType") Integer idType,
        @Param("todoTaskList") List<TaskDto> todoTaskList, 
        @Param("executeUserId") Long executeUserId, 
        @Param("params") InspectOrderParams params
    );

    @Update("UPDATE inspect_order SET execute_user_id = #{userId}, update_time = now() WHERE id = #{inspectOrderId}")
    Integer updateExecuteUserIdById(@Param("inspectOrderId") Long inspectOrderId, @Param("userId") Long userId);

    @Update("UPDATE inspect_order SET deleted = 1 WHERE inspect_plan_id = #{inspectPlanId} AND status IN (1,2,3,4)")
    Integer deleteUnFinishOrderByPlanId(@Param("inspectPlanId") Long inspectPlanId);

    Page<InspectOrderDto> pageByProcessUserId(
        @Param("tenantId") Long tenantId,
        @Param("idType") Integer idType, 
        @Param("userId") Long userId, 
        @Param("params") InspectOrderParams params,
        @Param("page") Page<InspectOrderEntity> page
    );

    List<InspectOrderEntity> listOrderByDate(@Param("tenantId") Long tenantId, @Param("idType") Integer idType, @Param("begin") LocalDateTime begin, @Param("end") LocalDateTime end);

    List<InspectOrderEntity> listAll(@Param("tenantId") Long tenantId, @Param("idType") Integer idType);

    @Select("SELECT * FROM inspect_order WHERE copy_order_id = #{copyOrderId} AND deleted = 0 AND status in (1,2,3,4)")
    List<InspectOrderEntity> listProcessingCopyOrder(@Param("copyOrderId") Long copyOrderId);

    @Select("SELECT * FROM inspect_order WHERE timeout = 1 AND deleted = 0 AND status in (1,2,3,4) and (outsource_customer_type  = 1 or outsource_customer_type is null)")
    List<InspectOrderEntity> listProcessingOrder();

    @Update("UPDATE inspect_order SET process_instance_id = #{processInstanceId} WHERE id = #{inspectOrderId}")
    Integer updateProcessInstanceIdById(@Param("inspectOrderId") Long inspectOrderId, @Param("processInstanceId") String processInstanceId);
    @Update("UPDATE inspect_order SET support_supplier_id = #{supportSupplierId} WHERE id = #{inspectOrderId}")
    Integer updateSupportSupplierIdById(@Param("inspectOrderId") Long inspectOrderId, @Param("supportSupplierId") Long supportSupplierId);
    @Update("UPDATE inspect_order SET timeout = 2, updator_id = 1, updator = 'admin', update_time = now() WHERE id = #{orderId}")
    Integer orderTimeout(@Param("orderId") Long orderId);

    @Select("SELECT count(*) FROM inspect_order WHERE inspect_plan_id = #{inspectPlanId} AND deleted = 0 AND status IN (1,2,3,4)")
    Integer countUnfinishOrderByPlanId(@Param("inspectPlanId")Long inspectPlanId);

    Integer updateBatchOverdueByIds(@Param("ids")List<Long> ids);

    List<InspectOrderEntity> listEndInspect(@Param("startDate")LocalDateTime startDate, @Param("endDate")LocalDateTime endDate,@Param("tenantId") Long tenantId);
}
