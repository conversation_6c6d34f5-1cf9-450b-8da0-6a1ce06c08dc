package com.nti56.dcm.server.model.dto;

import com.nti56.dcm.server.entity.MaintenanceItemEntity;
import com.nti56.dcm.server.entity.WorkloadEntity;
import com.nti56.dcm.server.model.vo.FileVo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/3/5 15:50<br/>
 * @since JDK 1.8
 */
@Data
public class MaintenanceOrderExecuteDto {

    /**
     * 保养开始时间
     */
    private LocalDateTime maintenanceBegin;

    /**
     * 保养结束时间
     */
    private LocalDateTime maintenanceEnd;

    /**
     *委外执行工单时，支持的供应商id
     */
    private Long supportSupplierId;
    /**
     * 工单id
     */
    @NotNull(message = "工单id不能为空")
    private Long maintenanceOrderId;

    /**
     * 人员工作量列表
     */
    private List<WorkloadEntity> workloadList;

}
