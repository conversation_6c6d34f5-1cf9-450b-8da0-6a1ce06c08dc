package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备维修工单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("device_repair")
public class DeviceRepairEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 维修设备id
     */
    private Long deviceId;

    /**
     * 维修工单编号
     */
    private String orderNumber;


    /**
     * 维修设备编号
     */
    private String deviceNo;

    /**
     * 故障时间
     */
    private LocalDateTime faultTime;

    /**
     * 故障等级
     */
    private Integer faultLevel;

    /**
     * 故障描述
     */
    private String faultDescribe;

    /**
     * 委外状态（0-内部，1-委外）
     */
    private Integer outsourceStatus;

    /**
     * 集成商委外供应商(0-否，1-是)
     */
    private Integer aggregatorOutsource;
    /**
     *委外执行工单时，支持的供应商id
     */
    private Long supportSupplierId;

    /**
     * 委外供应商id
     */
    private Long outsourceSupplierId;

    /**
     * 委外供应商名称
     */
    private String outsourceSupplierName;

    /**
     * 委外供应商类型，1-租户型，2 为数据型供应商
     */
    private Integer outsourceSupplierType;

    /**
     * 委外客户类型，1-租户型，2 为数据型供应商
     */
    private Integer outsourceCustomerType;
    /**
     * 外委工单创建来源 1-供应商/集成商 2-客户
     */
    private Integer createSource;

    /**
     * 委外租户id
     */
    private Long outsourceTenantId;

    /**
     * 快速委外关联原始单据id
     */
    private Long outsourceOriginId;

    /**
     * 维修状态
     * 参考OrderStatusEnum
     */
    private Integer status;

    /**
     * 工单接收人id
     */
    private Long receiveUserId;
    /**
     * 故障类型
     */
    private Long faultType;
    /**
     * 报修设备部位id
     */
    private Long repairDeviceBomId;
    /**
     * 报修设备部位名称
     */
    private String repairDeviceBomName;

    /**
     * 执行设备部位id
     */
    private Long executeDeviceBomId;
    /**
     * 执行设备部位名称
     */
    private String executeDeviceBomName;

    /**
     * 故障原因
     */
    private String faultReason;

    /**
     * 处理过程
     */
    private String repairProcess;

    /**
     * 维修开始时间
     */
    private LocalDateTime repairStartTime;

    /**
     * 维修结束时间
     */
    private LocalDateTime repairEndTime;

    /**
     * 维修耗时（时分格式）
     */
    private String repairUseTime;

    /**
     * 是否加入经验库(0-否，1-是)
     */
    private Integer repairSaveExp;
    /**
     * 供应商内部设备类型ID
     */
    private Long innerDeviceTypeId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     *创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     *更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 是否超时 1-正常 2-超时
     */
    private Integer timeout;

    /**
     * 流程实例id
     */
    private String processInstanceId;


}
