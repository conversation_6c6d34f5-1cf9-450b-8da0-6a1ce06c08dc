package com.nti56.dcm.server.model.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nti56.nlink.common.util.PageParam;

import lombok.Data;

@Data
public class InspectOrderParams extends PageParam{
    
    /**
    * 租户id
    */
    private Long tenantId;
    
    /**
     * 设备id
     */
    private Long deviceId;
    
    /**
     * 设备编码
     */
    private String deviceNumber;

    /**
     * 工单编码
     */
    private String orderNumber;

    /**
     * 计划编码
     */
    private String planNumber;

    /**
     * 计划名称
     */
    private String planName;


    /**
     * 检查类型，1-点检，2-巡检
     */
    private Integer inspectType;

    /**
     * 是否外委，1-是，0-否
     */
    private Integer outsource;

    /**
    * 委外供应商id
    */
    private Long supplierId;

    /**
     * 工单状态
     */
    private Integer status;

    /**
     * 工单状态列表
     */
    private List<Integer> statusList;

    /**
     * 计划等级 1-紧急 2-重要 3-普通 4-其他
     */
    private Integer level;
    
    /**
     * 客户租户id
     */
    private Long customerId;
    
    /**
     * 计划检查时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planInspectTimeBegin;

    /**
     * 计划检查时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planInspectTimeEnd;

    /**
     * 检查结束时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inspectFinishTimeBegin;

    /**
     * 检查结束时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inspectFinishTimeEnd;


    /**
     * 最后更新时间 查询范围——开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastModifyStartDate;

    /**
     * 最后更新时间 查询范围——结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastModifyEndDate;

    /**
     * 是否超时，1-正常，2-超时
     */
    private Integer timeout;

    /**
     * 是否逾期 0-未逾期，1-逾期）
     */
    private Integer overdue;

    /**
     * 快速委外关联原始单据工单编号
     */
    private String outsourceOriginOrderNumber;
    /**
     * 快速委外关联原始单据工客户id
     */
    private Long outsourceOriginCustomerId;
}
