package com.nti56.dcm.server.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * <p>
 * 点巡检标准项目表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_standard_item")
public class InspectStandardItemEntity{
    /**
    * 所属标准id
    */
    @ExcelIgnore
    private Long inspectStandardId;

    /**
     * 所属模块ID
     */
    @ExcelIgnore
    private Long inspectStandardModuleId;

    /**
    * 项目名称
    */
    @ExcelProperty(value = "项目名称", index = 1)
    private String itemName;

    /**
    * 标准描述
    */
    @ExcelProperty(value = "备注", index = 2)
    private String standardDesc;

    /**
    * 检查方法
    */
    @ExcelIgnore
    private String inspectMethod;
    
    /**
     * 记录类型，1-单选，2-多选,3-数值，4-文本
     */
    @ExcelIgnore
    private Integer recordType;

    /**
    * 可选项，格式JsonStringArray
    */
    @ExcelIgnore
    private String options;


    /**
    * 正常选项，格式JsonStringArray
    */
    @ExcelProperty(value = "正常选项", index = 4)
    private String normalOption;

    /**
    * 异常选项，格式JsonStringArray
    */
    @ExcelProperty(value = "异常选项", index = 5)
    private String errorOption;

    /**
    * 最大值
    */
    @ExcelProperty(value = "数值上限", index = 6)
    private String maxValue;

    /**
    * 最小值
    */
    @ExcelProperty(value = "数值下限", index = 7)
    private String minValue;

    /**
    * 是否必检，1-是，0-否
    */
    @ExcelIgnore
    private Integer requiredInspect;

    /**
    * 是否必须拍照，1-是，0-否
    */
    @ExcelIgnore
    private Integer requiredPicture;

    /**
     * 完整部位名称
     */
    @ExcelProperty(value = "模块名称", index = 0)
    private String fullBomName;

    /**
     *id
    */
    @ExcelIgnore
    private Long id;
    /**
     *租户id
    */
    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private Long tenantId;
    /**
     *版本号
    */
    @ExcelIgnore
    private Integer version;
    /**
     *删除
    */
    @ExcelIgnore
    private Integer deleted;
    /**
     *创建人ID
    */
    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private Long creatorId;
    /**
     *创建人
    */
    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private String creator;
    /**
     *创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private LocalDateTime createTime;
    /**
     *更新人ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ExcelIgnore
    private Long updatorId;
    /**
     *更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ExcelIgnore
    private String updator;
    /**
     *更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ExcelIgnore
    private LocalDateTime updateTime;
    /**
     *工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private Long engineeringId;
    /**
     *模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private Long moduleId;
    /**
     *空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private Long spaceId;
}
