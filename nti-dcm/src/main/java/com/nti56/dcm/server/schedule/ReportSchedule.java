package com.nti56.dcm.server.schedule;

import com.nti56.dcm.server.domain.enums.ReportTypeEnum;
import com.nti56.dcm.server.service.IReportRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * 报表定时任务
 */
@Slf4j
@Component
public class ReportSchedule {

    @Autowired
    private IReportRecordService reportRecordService;

    private static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final DateTimeFormatter yyyyMM = DateTimeFormatter.ofPattern("yyyy-MM");

    /**
     * 每天凌晨1点执行，获取前一天的工单统计数据
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void dailyReportTask() {
        log.info("开始执行每日工单统计任务");
        
        // 获取前一天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        
        // 获取前一天的开始时间和结束时间
        LocalDateTime startTime = yesterday.atStartOfDay();
        LocalDateTime endTime = yesterday.atTime(LocalTime.of(23, 59, 59));

        // 获取工单统计数据
        reportRecordService.reportStatistics(
                ReportTypeEnum.DAY.getValue(),
                yyyyMMdd.format(yesterday),
                startTime,
                endTime);

        log.info("开始执行每日用户使用情况任务");
        // 云管使用情况报表
        reportRecordService.useDcmReport(ReportTypeEnum.DAY.getValue(),
                yyyyMMdd.format(yesterday),
                startTime.toLocalDate(),
                endTime.toLocalDate());
    }
    
    /**
     * 每周一凌晨2点执行，获取上一周的工单统计数据
     */
    @Scheduled(cron = "0 0 2 ? * MON")
    public void weeklyReportTask() {
        log.info("开始执行每周工单统计任务");
        
        // 获取当前日期
        LocalDate now = LocalDate.now();
        
        // 获取上一周的第一天（周一）和最后一天（周日）
        LocalDate weekStart = now.minusWeeks(1).with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
        LocalDate weekEnd = weekStart.plusDays(6);
        
        // 获取上一周的开始时间和结束时间
        LocalDateTime startTime = weekStart.atStartOfDay();
        LocalDateTime endTime = weekEnd.atTime(LocalTime.MAX);

        // 获取工单统计数据
        reportRecordService.reportStatistics(
                ReportTypeEnum.WEEK.getValue(),
                yyyyMMdd.format(weekStart) + "至" + yyyyMMdd.format(weekEnd),
                startTime,
                endTime);

        log.info("开始执行每周用户使用情况任务");
        // 云管使用情况报表
        reportRecordService.useDcmReport(ReportTypeEnum.WEEK.getValue(),
                yyyyMMdd.format(weekStart) + "至" + yyyyMMdd.format(weekEnd),
                weekStart,
                weekEnd);
    }
    
    /**
     * 每月1号凌晨3点执行，获取上一个月的工单统计数据
     */
    @Scheduled(cron = "0 0 3 1 * ?")
    public void monthlyReportTask() {
        log.info("开始执行每月工单统计任务");
        
        // 获取当前日期
        LocalDate now = LocalDate.now();
        
        // 获取上一个月的第一天和最后一天
        LocalDate monthStart = now.minusMonths(1).withDayOfMonth(1);
        LocalDate monthEnd = monthStart.with(TemporalAdjusters.lastDayOfMonth());
        
        // 获取上一个月的开始时间和结束时间
        LocalDateTime startTime = monthStart.atStartOfDay();
        LocalDateTime endTime = monthEnd.atTime(LocalTime.MAX);
        
        // 获取工单统计数据
        reportRecordService.reportStatistics(
                ReportTypeEnum.MONTH.getValue(),
                yyyyMM.format(monthStart),
                startTime,
                endTime);

        log.info("开始执行每月用户使用情况任务");
        // 云管使用情况报表
        reportRecordService.useDcmReport(ReportTypeEnum.MONTH.getValue(),
                yyyyMM.format(monthStart),
                monthStart,
                monthEnd);
    }
}
