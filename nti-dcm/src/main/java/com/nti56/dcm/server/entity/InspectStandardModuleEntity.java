package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 点巡检标准模块表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-07-04 09:39:39
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("inspect_standard_module")
public class InspectStandardModuleEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 所属标准id
    */
    @NotNull(message = "所属标准id不能为空")
    private Long inspectStandardId;

    /**
    * 模块名称
    */
    @NotEmpty(message = "模块名称不能为空")
    private String moduleName;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人ID
    */
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新人ID
    */
    private Long updatorId;

    /**
    * 更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
