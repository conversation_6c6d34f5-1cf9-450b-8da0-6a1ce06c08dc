package com.nti56.dcm.server.model.dto;

import java.util.List;

import com.nti56.dcm.server.domain.enums.OrderStatusEnum;
import com.nti56.dcm.server.entity.InspectOrderEntity;

import lombok.Data;

@Data
public class InspectOrderDto extends InspectOrderEntity{
    
    /**
     * 计划编号
     */
    private String planNumber;
    
    /**
     * 计划名称
     */
    private String planName;

    /**
     * 检查类型，1-点检，2-巡检
     */
    private Integer inspectType;

    /**
     * 操作按钮列表
     */
    private List<String> operations;

    /**
     * 快速委外关联原始单据工单编号
     */
    private String outsourceOriginOrderNumber;
    /**
     * 快速委外关联原始单据工客户id
     */
    private Long outsourceOriginCustomerId;
    /**
     * 快速委外关联原始单据客户名称
     */
    private String outsourceOriginCustomerName;
    /**
     * 工单状态描述
     * @return
     */
    public String getStatusDesc(){
        if(this.getStatus() == null){
            return "";
        }
        OrderStatusEnum status = OrderStatusEnum.typeOfValue(this.getStatus());
        if(status == null){
            return "";
        }
        return status.getNameDesc();
    }

}
