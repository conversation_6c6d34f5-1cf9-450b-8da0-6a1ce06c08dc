package com.nti56.dcm.server.model.vo;

import com.nti56.dcm.server.entity.MaintenanceOrderEntity;
import lombok.Data;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/3/5 9:25<br/>
 * @since JDK 1.8
 */
@Data
public class MaintenanceOrderVo extends MaintenanceOrderEntity {

    /**
     * 保养计划编号
     */
    private String planNumber;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 设备编号
     */
    private String deviceNumber;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 来自客户
     */
    private String customerName;

    /**
     * 操作按钮列表
     */
    private List<String> operations;


    /**
     * 快速委外关联原始单据工单编号
     */
    private String outsourceOriginOrderNumber;
    /**
     * 快速委外关联原始单据工客户id
     */
    private Long outsourceOriginCustomerId;
    /**
     * 快速委外关联原始单据客户名称
     */
    private String outsourceOriginCustomerName;
}
