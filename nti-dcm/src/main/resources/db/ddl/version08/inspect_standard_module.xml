<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="zjc" id="20250704161422">
        <sql>
             CREATE TABLE `inspect_standard_module` (
                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                 `tenant_id` bigint NOT NULL COMMENT '租户id',
                 `inspect_standard_id` bigint DEFAULT NULL COMMENT '所属标准id',
                 `module_name` varchar(64) NOT NULL COMMENT '模块名称',
                 `version` int DEFAULT '1' COMMENT '版本号',
                 `deleted` int DEFAULT '0' COMMENT '删除',
                 `creator_id` bigint DEFAULT NULL COMMENT '创建人ID',
                 `creator` varchar(90) DEFAULT NULL COMMENT '创建人',
                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                 `updator_id` bigint DEFAULT NULL COMMENT '更新人ID',
                 `updator` varchar(90) DEFAULT NULL COMMENT '更新人',
                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                 `engineering_id` bigint DEFAULT NULL COMMENT '工程ID',
                 `module_id` bigint DEFAULT NULL COMMENT '模块ID',
                 `space_id` bigint DEFAULT NULL COMMENT '空间ID',
                 PRIMARY KEY (`id`)
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点巡检标准模块表';
        </sql>
    </changeSet>

</databaseChangeLog>
