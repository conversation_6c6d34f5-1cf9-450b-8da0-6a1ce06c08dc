<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1708572345">
        <sql>
            CREATE TABLE inspect_result_item (
                id BIGINT AUTO_INCREMENT COMMENT 'id',
                tenant_id BIGINT NOT NULL COMMENT '租户id',
                
                inspect_result_id BIGINT NOT NULL COMMENT '所属检查结果id',
                inspect_standard_item_id BIGINT COMMENT '检查项目id',
                result_value VARCHAR(255) COMMENT '检查值，选项名（多选的话逗号分隔）、数值、文本',
                is_error TINYINT COMMENT '是否异常，1-异常，0-正常',

                version INT   DEFAULT 1 COMMENT '版本号' ,
                deleted INT   DEFAULT 0 COMMENT '删除' ,

                creator_id BIGINT COMMENT '创建人ID' ,
                creator VARCHAR(90)    COMMENT '创建人' ,
                create_time DATETIME    COMMENT '创建时间' ,
                updator_id BIGINT COMMENT '更新人ID' ,
                updator VARCHAR(90)    COMMENT '更新人' ,
                update_time DATETIME    COMMENT '更新时间' ,

                engineering_id BIGINT  COMMENT '工程ID' ,
                module_id BIGINT  COMMENT '模块ID' ,
                space_id BIGINT  COMMENT '空间ID',
                
                PRIMARY KEY (id)
            )  COMMENT = '点巡检结果项目表';
        </sql>
    </changeSet>

    <changeSet id="202507081104" author="zjc">
        <sql>
            alter table inspect_result_item add column `remark` varchar(255) DEFAULT NULL COMMENT '备注' after `is_error`;
        </sql>
    </changeSet>

</databaseChangeLog>
