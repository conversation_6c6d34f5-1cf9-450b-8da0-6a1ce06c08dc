<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="sushangqun" id="1708572346">
        <sql>
            CREATE TABLE inspect_standard_item (
                id BIGINT AUTO_INCREMENT COMMENT 'id',
                tenant_id BIGINT NOT NULL COMMENT '租户id',
                
                inspect_standard_id BIGINT NOT NULL COMMENT '所属标准id',
                item_name VARCHAR(255) NOT NULL COMMENT '项目名称',
                standard_desc TEXT COMMENT '标准描述',
                inspect_method VARCHAR(255) COMMENT '检查方法',
                record_type TINYINT COMMENT '记录类型，1-单选，2-多选,3-数值，4-文本',
                options TEXT COMMENT '可选项，格式JsonStringArray',
                normal_option TEXT COMMENT '正常选项，格式JsonStringArray',
                error_option TEXT COMMENT '异常选项，格式JsonStringArray',
                max_value VARCHAR(255) COMMENT '最大值',
                min_value VARCHAR(255) COMMENT '最小值',
                required_inspect TINYINT COMMENT '是否必检，1-是，0-否',
                
                version INT   DEFAULT 1 COMMENT '版本号' ,
                deleted INT   DEFAULT 0 COMMENT '删除' ,

                creator_id BIGINT COMMENT '创建人ID' ,
                creator VARCHAR(90)    COMMENT '创建人' ,
                create_time DATETIME    COMMENT '创建时间' ,
                updator_id BIGINT COMMENT '更新人ID' ,
                updator VARCHAR(90)    COMMENT '更新人' ,
                update_time DATETIME    COMMENT '更新时间' ,

                engineering_id BIGINT  COMMENT '工程ID' ,
                module_id BIGINT  COMMENT '模块ID' ,
                space_id BIGINT  COMMENT '空间ID',
                
                PRIMARY KEY (id)
            )  COMMENT = '点巡检标准项目表';
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1715823473">
        <sql>
            ALTER TABLE inspect_standard_item ADD COLUMN required_picture TINYINT COMMENT '是否必须拍照，1-是，0-否';
        </sql>
    </changeSet>

    <changeSet author="sushangqun" id="1720666616">
        <sql>
            ALTER TABLE inspect_standard_item ADD COLUMN full_bom_name TEXT COMMENT '完整部位名称';
        </sql>
    </changeSet>

    <changeSet author="zhangjuncheng" id="1720666617">
        <sql>
            ALTER TABLE inspect_standard_item ADD COLUMN inspect_standard_module_id BIGINT NOT NULL COMMENT '所属模块ID' after inspect_standard_id;
        </sql>
    </changeSet>

</databaseChangeLog>
