<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="zjc" id="202402281459">
        <sql>
            CREATE TABLE `maintenance_standard_item` (
            `id` bigint NOT NULL COMMENT '保养明细ID',

            `maintenance_standard_id` bigint DEFAULT NULL COMMENT '保养标准ID',

            `position` varchar(255) DEFAULT NULL COMMENT '保养部位',
            `standard_desc` varchar(255) DEFAULT NULL COMMENT '保养标准',
            `remark` varchar(255) DEFAULT NULL COMMENT '备注',

            `creator` varchar(255) DEFAULT NULL COMMENT '创建人',
            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
            `updator` varchar(255) DEFAULT NULL COMMENT '修改人',
            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
            `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
            `engineering_id` bigint DEFAULT NULL COMMENT '工程ID',
            `module_id` bigint DEFAULT NULL COMMENT '模块ID',
            `space_id` bigint DEFAULT NULL COMMENT '空间ID',
            `version` int DEFAULT '1' COMMENT '版本号',
            `deleted` int DEFAULT '0' COMMENT '删除',
            PRIMARY KEY (`id`)
            ) COMMENT '保养标准明细';
        </sql>
    </changeSet>

    <changeSet id="202407151458" author="zjc">
        <sql>
            ALTER TABLE maintenance_standard_item ADD COLUMN full_bom_name TEXT COMMENT '完整部位名称';
        </sql>
    </changeSet>

    <changeSet id="202411121058" author="zjc">
        <sql>
            alter table maintenance_standard_item modify column `id` bigint NOT NULL AUTO_INCREMENT COMMENT '保养标准明细ID';
        </sql>
    </changeSet>

    <changeSet author="zhangjuncheng" id="202407041058">
        <sql>
            ALTER TABLE maintenance_standard_item ADD COLUMN maintenance_standard_module_id BIGINT NOT NULL COMMENT '所属模块ID' after maintenance_standard_id;
        </sql>
    </changeSet>

</databaseChangeLog>
