<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.dcm.server.mapper.DeviceRepairMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nti56.dcm.server.entity.DeviceRepairEntity">
        <id column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_no" property="deviceNo"/>
        <result column="order_number" property="orderNumber"/>
        <result column="fault_time" property="faultTime"/>
        <result column="fault_level" property="faultLevel"/>
        <result column="fault_describe" property="faultDescribe"/>
        <result column="outsource_status" property="outsourceStatus"/>
        <result column="outsource_supplier_id" property="outsourceSupplierId"/>
        <result column="outsource_supplier_name" property="outsourceSupplierName"/>
        <result column="outsource_tenant_id" property="outsourceTenantId"/>
        <result column="outsource_origin_id" property="outsourceOriginId"/>
        <result column="status" property="status"/>
        <result column="fault_type" property="faultType"/>
        <result column="fault_reason" property="faultReason"/>
        <result column="repair_process" property="repairProcess"/>
        <result column="repair_start_time" property="repairStartTime"/>
        <result column="repair_end_time" property="repairEndTime"/>
        <result column="repair_use_time" property="repairUseTime"/>
        <result column="repair_save_exp" property="repairSaveExp"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updator" property="updator"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="engineering_id" property="engineeringId"/>
        <result column="module_id" property="moduleId"/>
        <result column="space_id" property="spaceId"/>
        <result column="version" property="version"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , device_id, device_no,order_number, fault_time, fault_level, fault_describe, outsource_status, outsource_supplier_id, outsource_supplier_name, outsource_tenant_id, outsource_origin_id, status, fault_type, fault_reason, repair_process, repair_start_time, repair_end_time, repair_use_time, repair_save_exp, creator, create_time, updator, update_time, tenant_id, engineering_id, module_id, space_id, version, deleted
    </sql>
    <sql id="selectByCondition">
        SELECT
        dr.*,
        d.device_name deviceName,
        d.device_type_id  deviceTypeId,
        dt.type_name  deviceTypeName,
        dr2.order_number outsourceOriginOrderNumber,
        dr2.tenant_id outsourceOriginCustomerId,
        dr2.customer_name outsourceOriginCustomerName
        FROM
        device_repair dr
        LEFT JOIN device d ON dr.device_id = d.id
        LEFT JOIN device_type dt ON d.device_type_id = dt.id
        LEFT JOIN device_repair dr2 on dr.outsource_origin_id = dr2.id
        WHERE
        dr.deleted = 0
        <if test="dto.idType != null and dto.idType == 2">
            and dr.tenant_id = #{dto.tenantId}
            <if test="dto.outsourceStatus != null ">
                and dr.outsource_status = #{dto.outsourceStatus}
            </if>
        </if>
        <if test="dto.idType != null and dto.idType == 1">
            <if test="dto.outsourceStatus ==0 ">
                and dr.outsource_supplier_id = #{dto.tenantId}
                and dr.outsource_status = 1
                <if test="dto.customerId != null ">
                    and dr.tenant_id = #{dto.customerId}
                </if>
            </if>
            <if test="dto.outsourceStatus == 1 ">
                and dr.tenant_id = #{dto.tenantId}
                and dr.outsource_status = 1
            </if>
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and(d.device_name LIKE CONCAT('%',#{dto.deviceName},'%') or d.device_name LIKE CONCAT('%',#{dto.deviceName},'%')  )
        </if>
        <if test="dto.deviceId != null ">
            and dr.device_id = #{dto.deviceId}
        </if>
        <if test="dto.deviceNo != null and dto.deviceNo != ''">
            and dr.device_no LIKE CONCAT('%',#{dto.deviceNo},'%')
        </if>
        <if test="dto.orderNumber != null ">
            and dr.order_number LIKE CONCAT('%',#{dto.orderNumber},'%')
        </if>
        <if test="dto.outsourceOriginOrderNumber != null ">
            and dr2.order_number LIKE CONCAT('%',#{dto.outsourceOriginOrderNumber},'%')
        </if>
        <if test="dto.outsourceOriginCustomerId != null ">
            and dr2.tenant_id = #{dto.outsourceOriginCustomerId}
        </if>
        <if test="dto.status != null ">
            and dr.status = #{dto.status}
        </if>

        <if test="dto.startDate != null ">
            and dr.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null ">
            and dr.create_time &lt;= #{dto.endDate}
        </if>

        <if test="dto.repairFinishTimeBegin != null ">
            and dr.repair_end_time &gt;= #{dto.repairFinishTimeBegin}
        </if>
        <if test="dto.repairFinishTimeEnd != null ">
            and dr.repair_end_time &lt;= #{dto.repairFinishTimeEnd}
        </if>

        <if test="dto.lastModifyStartDate != null ">
            and dr.update_time &gt;= #{dto.lastModifyStartDate}
        </if>
        <if test="dto.lastModifyEndDate != null ">
            and dr.update_time &lt;= #{dto.lastModifyEndDate}
        </if>
        <if test='dto.timeout != null'>
            AND dr.timeout = #{dto.timeout}
        </if>
        <if test="dto.outsourceSupplierId != null ">
            and dr.outsource_supplier_id = #{dto.outsourceSupplierId}
        </if>
        <if test="dto.statusList != null and dto.statusList.size > 0">
            AND dr.status in
            <foreach collection="dto.statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        order by dr.create_time desc
    </sql>

    <select id="queryRepairByCondition" resultType="com.nti56.dcm.server.model.dto.DeviceRepairDto">
        <include refid="selectByCondition"></include>
    </select>

    <select id="listRepairByCondition" resultType="com.nti56.dcm.server.model.dto.DeviceRepairDto">
        <include refid="selectByCondition"></include>
    </select>

    <select id="queryRepairWithAuth" resultType="com.nti56.dcm.server.model.dto.DeviceRepairDto">
        SELECT
        dr.*,
        d.device_name deviceName,
        d.device_type_id deviceTypeId,
        dt.type_name deviceTypeName,
        dr2.order_number outsourceOriginOrderNumber,
        dr2.tenant_id outsourceOriginCustomerId,
        dr2.customer_name outsourceOriginCustomerName
        FROM
        device_repair dr
        LEFT JOIN device d ON dr.device_id = d.id
        LEFT JOIN device_type dt ON d.device_type_id = dt.id
        LEFT JOIN device_repair dr2 on dr.outsource_origin_id = dr2.id
        WHERE
        dr.deleted = 0
        <if test="dto.idType != null and dto.idType == 2">
            and dr.tenant_id = #{dto.tenantId}
            <if test="dto.outsourceStatus != null ">
                and dr.outsource_status = #{dto.outsourceStatus}
            </if>
        </if>
        <if test="dto.idType != null and dto.idType == 1">
            <if test="dto.outsourceStatus ==0 ">
                and dr.outsource_supplier_id = #{dto.tenantId}
                and dr.outsource_status = 1
                <if test="dto.customerId != null ">
                    and dr.tenant_id = #{dto.customerId}
                </if>
            </if>
            <if test="dto.outsourceStatus == 1 ">
                and dr.tenant_id = #{dto.tenantId}
                and dr.outsource_status = 1
            </if>
        </if>
     <if test="dto.todoProcessInstanceIdList != null and dto.todoProcessInstanceIdList.size > 0">
        AND dr.process_instance_id in
        <foreach collection="dto.todoProcessInstanceIdList" item="processInstanceId" open="(" close=")" separator=",">
            #{processInstanceId}
        </foreach>
    </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and ( d.device_name LIKE CONCAT('%',#{dto.deviceName},'%') or d.device_name LIKE CONCAT('%',#{dto.deviceName},'%') )
        </if>
        <if test="dto.deviceNo != null and dto.deviceNo != ''">
            and dr.device_no LIKE CONCAT('%',#{dto.deviceNo},'%')
        </if>
        <if test="dto.orderNumber != null and dto.orderNumber != ''">
            and dr.order_number LIKE CONCAT('%',#{dto.orderNumber},'%')
        </if>
        <if test="dto.status != null and dto.status != ''">
            and dr.status = #{dto.status}
        </if>

        <if test="dto.outsourceOriginOrderNumber != null ">
            and dr2.order_number LIKE CONCAT('%',#{dto.outsourceOriginOrderNumber},'%')
        </if>
        <if test="dto.outsourceOriginCustomerId != null ">
            and dr2.tenant_id = #{dto.outsourceOriginCustomerId}
        </if>
        <if test="dto.startDate != null ">
            and dr.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null ">
            and dr.create_time &lt;= #{dto.endDate}
        </if>

        <if test="dto.repairFinishTimeBegin != null ">
            and dr.repair_end_time &gt;= #{dto.repairFinishTimeBegin}
        </if>
        <if test="dto.repairFinishTimeEnd != null ">
            and dr.repair_end_time &lt;= #{dto.repairFinishTimeEnd}
        </if>

        <if test='dto.timeout != null'>
            AND dr.timeout = #{dto.timeout}
        </if>
        <if test="dto.outsourceSupplierId != null ">
            and dr.outsource_supplier_id = #{dto.outsourceSupplierId}
        </if>
        <if test="dto.statusList != null and dto.statusList.size > 0">
            AND dr.status in
            <foreach collection="dto.statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        order by dr.create_time desc
    </select>
    <select id="queryRepairWithMe" resultType="com.nti56.dcm.server.model.dto.DeviceRepairDto">
        SELECT
        dr.*,
        d.device_name deviceName,
        d.device_type_id deviceTypeId,
        dt.type_name deviceTypeName,
        dr2.order_number outsourceOriginOrderNumber,
        dr2.tenant_id outsourceOriginCustomerId,
        dr2.customer_name outsourceOriginCustomerName
        FROM
        device_repair dr
        LEFT JOIN device d ON dr.device_id = d.id
        LEFT JOIN device_type dt ON d.device_type_id = dt.id
        LEFT JOIN device_repair dr2 on dr.outsource_origin_id = dr2.id
        WHERE dr.deleted = 0
        and ((select count(1) from order_process_record p where p.process_instance_id = dr.process_instance_id and p.executor_id =
        #{dto.executeUserId}) > 0 or dr.creator = #{dto.executeUserId} )

        <if test="dto.idType != null and dto.idType == 2">
            and dr.tenant_id = #{dto.tenantId}
            <if test="dto.outsourceStatus != null ">
                and dr.outsource_status = #{dto.outsourceStatus}
            </if>
        </if>
        <if test="dto.idType != null and dto.idType == 1">
            <if test="dto.outsourceStatus ==0 ">
                and dr.outsource_supplier_id = #{dto.tenantId}
                and dr.outsource_status = 1
                <if test="dto.customerId != null ">
                    and dr.tenant_id = #{dto.customerId}
                </if>
            </if>
            <if test="dto.outsourceStatus == 1 ">
                and dr.tenant_id = #{dto.tenantId}
                and dr.outsource_status = 1
            </if>
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and ( d.device_name LIKE CONCAT('%',#{dto.deviceName},'%') or d.device_name LIKE CONCAT('%',#{dto.deviceName},'%')  )
        </if>
        <if test="dto.deviceNo != null and dto.deviceNo != ''">
            and dr.device_no LIKE CONCAT('%',#{dto.deviceNo},'%')
        </if>
        <if test="dto.orderNumber != null and dto.orderNumber != ''">
            and dr.order_number LIKE CONCAT('%',#{dto.orderNumber},'%')
        </if>
        <if test='dto.timeout != null'>
            AND dr.timeout = #{dto.timeout}
        </if>
        <if test="dto.status != null ">
            and dr.status = #{dto.status}
        </if>
        <if test="dto.outsourceSupplierId != null ">
            and dr.outsource_supplier_id = #{dto.outsourceSupplierId}
        </if>

        <if test="dto.outsourceOriginOrderNumber != null ">
            and dr2.order_number LIKE CONCAT('%',#{dto.outsourceOriginOrderNumber},'%')
        </if>
        <if test="dto.outsourceOriginCustomerId != null ">
            and dr2.tenant_id = #{dto.outsourceOriginCustomerId}
        </if>
        <if test="dto.startDate != null ">
            and dr.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null ">
            and dr.create_time &lt;= #{dto.endDate}
        </if>

        <if test="dto.repairFinishTimeBegin != null ">
            and dr.repair_end_time &gt;= #{dto.repairFinishTimeBegin}
        </if>
        <if test="dto.repairFinishTimeEnd != null ">
            and dr.repair_end_time &lt;= #{dto.repairFinishTimeEnd}
        </if>

        <if test="dto.statusList != null and dto.statusList.size > 0">
            AND dr.status in
            <foreach collection="dto.statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        order by dr.create_time desc
    </select>
    <select id="queryRepairSupplierStat" resultType="com.nti56.dcm.server.model.dto.DeviceRepairDto">
        SELECT
        IFNULL( d.device_name,"已删除设备") deviceName,
        dr.customer_name AS customerName
        FROM
        device_repair dr left join device d on dr.device_id = d.id
        WHERE dr.deleted = 0 and dr.status != 7
        <if test="dto.idType != null and dto.idType == 1">
            and dr.outsource_supplier_id = #{dto.tenantId}
            and dr.outsource_status = 1
        </if>

        <if test="dto.startDate != null ">
            and dr.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null ">
            and dr.create_time &lt;= #{dto.endDate}
        </if>

    </select>
    <select id="queryRepairWithDevice" resultType="com.nti56.dcm.server.model.dto.DeviceRepairDto">
        SELECT
        dr.*,d.device_name,d.device_type_id
        FROM
        device_repair dr left join device d on dr.device_id = d.id
        WHERE
        dr.deleted = 0 and dr.device_id = #{dto.deviceId}
        and dr.outsource_status = #{dto.outsourceStatus}
        <if test="dto.orderNumber != null and dto.orderNumber != ''">
            and dr.order_number LIKE CONCAT('%',#{dto.orderNumber},'%')
        </if>
        <if test="dto.status != null ">
            and dr.status = #{dto.status}
        </if>
        order by dr.create_time desc
    </select>
    <select id="getVoById" resultType="com.nti56.dcm.server.model.vo.DeviceRepairVo">
        select  dr.*,d.device_name deviceName,d.device_type_id deviceTypeId,dt.type_name as innerDeviceTypeName,dtt.type_name as deviceTypeName  from
            device_repair dr left join device d on dr.device_id = d.id
            left join device_type dt on dr.inner_device_type_id = dt.id
            left join device_type dtt on d.device_type_id = dtt.id
        where dr.id = #{id}
    </select>
    <select id="listEndDeviceRepair" resultType="com.nti56.dcm.server.entity.DeviceRepairEntity">
        select * from device_repair where repair_end_time is not null
                                      and deleted = 0
        and( tenant_id = #{tenantId} or outsource_supplier_id = #{tenantId} )
        <if test="startDate != null">
            and repair_end_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and repair_end_time &lt;= #{endDate}
        </if>
    </select>

    <select id="queryServiceCustomerCount" resultType="java.lang.Integer">
        select count(DISTINCT(a.tenant_id))
        from (
                 select mo.tenant_id, mo.create_time from maintenance_order mo
                 where mo.deleted = 0 and mo.status = 5 and mo.supplier_id = #{tenantId}
                 union all
                 select io.tenant_id, io.create_time from inspect_order io
                 where io.deleted = 0 and io.status = 5 and io.supplier_id = #{tenantId}
                 union all
                 select dr.tenant_id, dr.create_time from device_repair dr
                 where dr.deleted = 0 and dr.status = 5 and dr.outsource_supplier_id = #{tenantId}
             ) a
        join customer_relation cr on a.tenant_id = cr.customer_id
        where cr.status = 1 and cr.id_type = 1 and cr.supplier_id = #{tenantId}
              <if test="period == 1">
                  and WEEK(a.create_time) = WEEK(CURDATE())
              </if>
              <if test="period == 2">
                  and MONTH(a.create_time) = MONTH(CURDATE())
              </if>
    </select>

    <select id="queryServiceOrderCount" resultType="java.lang.Integer">
        select count(a.id)
        from (
                select mo.id, mo.tenant_id, mo.create_time from maintenance_order mo
                where mo.deleted = 0 and mo.supplier_id = #{tenantId}
                      <if test="finish == true">
                          and mo.status = 5
                      </if>
                union all
                select io.id, io.tenant_id, io.create_time from inspect_order io
                where io.deleted = 0 and io.supplier_id = #{tenantId}
                      <if test="finish == true">
                           and io.status = 5
                      </if>
                union all
                select dr.id, dr.tenant_id, dr.create_time from device_repair dr
                where dr.deleted = 0 and dr.outsource_supplier_id = #{tenantId}
                      <if test="finish == true">
                           and dr.status = 5
                      </if>
             ) a
        join customer_relation cr on a.tenant_id = cr.customer_id
        where cr.status = 1 and cr.id_type = 1 and cr.supplier_id = #{tenantId}
              <if test="period == 1">
                   and WEEK(a.create_time) = WEEK(CURDATE())
              </if>
              <if test="period == 2">
                   and MONTH(a.create_time) = MONTH(CURDATE())
              </if>
    </select>

</mapper>
