<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.dcm.server.mapper.MaintenanceOrderMapper">

    <sql id="selectByDto">
        select distinct o.*,
        p.plan_name, p.plan_number,
        o2.order_number outsourceOriginOrderNumber,
        o2.tenant_id outsourceOriginCustomerId,
        o2.customer_name outsourceOriginCustomerName
        from maintenance_order o
        left join maintenance_plan p on o.maintenance_plan_id = p.id
        LEFT JOIN maintenance_result r ON (r.maintenance_order_id = o.id)
        LEFT JOIN device d ON (d.id = r.device_id)
        left join maintenance_order o2 on o.outsource_origin_id = o2.id
        where o.deleted = 0
        <if test="dto.idType != null and dto.idType == 2">
            <if test="dto.tenantId != null">
                and o.tenant_id = #{dto.tenantId}
            </if>
            <if test="dto.outsource != null">
                and o.outsource = #{dto.outsource}
            </if>
        </if>
        <if test="dto.idType != null and dto.idType == 1">
            <if test='dto.outsource == 0'>
                and o.supplier_id = #{dto.tenantId}
                and o.outsource = 1
                <if test="dto.customerId != null ">
                    and o.tenant_id = #{dto.customerId}
                </if>
            </if>
            <if test='dto.outsource == 1'>
                and o.tenant_id = #{dto.tenantId}
                and o.outsource = 1
            </if>
        </if>
        <if test="dto.supplierId != null">
            and o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.orderNumber != null and dto.orderNumber != ''">
            and o.order_number like CONCAT('%', #{dto.orderNumber},'%')
        </if>

        <if test="dto.outsourceOriginOrderNumber != null and dto.outsourceOriginOrderNumber != ''">
            and o2.order_number like CONCAT('%', #{dto.outsourceOriginOrderNumber},'%')
        </if>

        <if test="dto.outsourceOriginCustomerId != null">
            and o2.tenant_id = #{dto.outsourceOriginCustomerId}
        </if>
        <if test="dto.planNumber != null and dto.planNumber != ''">
            and p.plan_number like CONCAT('%', #{dto.planNumber},'%')
        </if>
        <if test="dto.planName != null and dto.planName != ''">
            and p.plan_name like CONCAT('%', #{dto.planName},'%')
        </if>
        <if test="dto.deviceId != null">
            AND (
                d.id = #{dto.deviceId}
            )
        </if>
        <if test="dto.deviceNumber != null and dto.deviceNumber != ''">
            and (
                d.serial_number like CONCAT('%', #{dto.deviceNumber},'%')
                OR pd.serial_number like CONCAT('%', #{dto.deviceNumber},'%')
            )
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and (
                d.device_name like CONCAT('%', #{dto.deviceName},'%')
                or pd.device_name like CONCAT('%', #{dto.deviceName},'%')
            )
        </if>
        <if test="dto.status != null">
            and o.status = #{dto.status}
        </if>
        <if test="dto.statusList != null and dto.statusList.size > 0">
            and o.status in
            <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.overdue != null">
            and o.overdue = #{dto.overdue}
        </if>
        <if test="dto.maintenanceTimeBegin != null">
            <![CDATA[
                        AND o.plan_maintenance_time >= #{dto.maintenanceTimeBegin}
                    ]]>
        </if>
        <if test="dto.maintenanceTimeEnd != null">
            <![CDATA[
                        AND o.plan_maintenance_time <= #{dto.maintenanceTimeEnd}
                    ]]>
        </if>
        <if test="dto.maintenanceFinishTimeBegin != null">
            <![CDATA[
                        AND o.maintenance_end >= #{dto.maintenanceFinishTimeBegin}
                    ]]>
        </if>
        <if test="dto.maintenanceFinishTimeEnd != null">
            <![CDATA[
                        AND o.maintenance_end <= #{dto.maintenanceFinishTimeEnd}
                    ]]>
        </if>
        <if test="dto.lastModifyStartDate != null ">
            and o.update_time &gt;= #{dto.lastModifyStartDate}
        </if>
        <if test="dto.lastModifyEndDate != null ">
            and o.update_time &lt;= #{dto.lastModifyEndDate}
        </if>
        <if test="dto.level != null">
            and o.level = #{dto.level}
        </if>
        <if test="dto.timeout != null">
            and o.timeout = #{dto.timeout}
        </if>
        order by o.create_time desc
    </sql>

    <select id="pageMaintenanceOrder" resultType="com.nti56.dcm.server.model.vo.MaintenanceOrderVo">
        <include refid="selectByDto"></include>
    </select>

    <select id="listMaintenanceOrder" resultType="com.nti56.dcm.server.model.vo.MaintenanceOrderVo">
        <include refid="selectByDto"></include>
    </select>

    <select id="pageWaitProcessOrderByAuth" resultType="com.nti56.dcm.server.model.vo.MaintenanceOrderVo">
        select distinct o.*,
               p.plan_name, p.plan_number,
        o2.order_number outsourceOriginOrderNumber,
        o2.tenant_id outsourceOriginCustomerId,
        o2.customer_name outsourceOriginCustomerName
        from maintenance_order o
        left join maintenance_plan p on o.maintenance_plan_id = p.id
        LEFT JOIN maintenance_result r ON (r.maintenance_order_id = o.id)
        LEFT JOIN device d ON (d.id = r.device_id)
        left join maintenance_order o2 on o.outsource_origin_id = o2.id
        where o.deleted = 0
        <if test="dto.idType != null and dto.idType == 2">
            <if test="dto.tenantId != null">
                and o.tenant_id = #{dto.tenantId}
            </if>
            <if test="dto.outsource != null">
                and o.outsource = #{dto.outsource}
            </if>
        </if>
        <if test="dto.idType != null and dto.idType == 1">
            <if test='dto.outsource == 0'>
                and o.supplier_id = #{dto.tenantId}
                and o.outsource = 1
                <if test="dto.customerId != null ">
                    and o.tenant_id = #{dto.customerId}
                </if>
            </if>
            <if test='dto.outsource == 1'>
                and o.tenant_id = #{dto.tenantId}
                and o.outsource = 1
            </if>
        </if>
        <if test="dto.supplierId != null">
            and o.supplier_id = #{dto.supplierId}
        </if>

            <if test="dto.orderNumber != null and dto.orderNumber != ''">
                and o.order_number like CONCAT('%', #{dto.orderNumber},'%')
            </if>
            <if test="dto.planNumber != null and dto.planNumber != ''">
                and p.plan_number like CONCAT('%', #{dto.planNumber},'%')
            </if>
            <if test="dto.planName != null and dto.planName != ''">
                and p.plan_name like CONCAT('%', #{dto.planName},'%')
            </if>
            <if test="dto.deviceNumber != null and dto.deviceNumber != ''">
                and d.serial_number like CONCAT('%', #{dto.deviceNumber},'%')
            </if>
            <if test="dto.deviceName != null and dto.deviceName != ''">
                and d.device_name like CONCAT('%', #{dto.deviceName},'%')
            </if>
            <if test="dto.status != null">
                and o.status = #{dto.status}
            </if>

        <if test="dto.outsourceOriginOrderNumber != null and dto.outsourceOriginOrderNumber != ''">
            and o2.order_number like CONCAT('%', #{dto.outsourceOriginOrderNumber},'%')
        </if>

        <if test="dto.outsourceOriginCustomerId != null">
            and o2.tenant_id = #{dto.outsourceOriginCustomerId}
        </if>
            <if test="dto.overdue != null">
                and o.overdue = #{dto.overdue}
            </if>
            <if test='dto.statusList != null and dto.statusList.size() > 0'>
                AND o.status IN (
                    <foreach item="status" collection="dto.statusList" separator=",">
                        #{status}
                    </foreach>
                )
            </if>
            <if test="dto.maintenanceTimeBegin != null">
                <![CDATA[
                        AND o.plan_maintenance_time >= #{dto.maintenanceTimeBegin}
                    ]]>
            </if>
            <if test="dto.maintenanceTimeEnd != null">
                <![CDATA[
                        AND o.plan_maintenance_time <= #{dto.maintenanceTimeEnd}
                    ]]>
            </if>
            <if test="dto.level != null">
                and o.level = #{dto.level}
            </if>
            <if test="dto.timeout != null">
                and o.timeout = #{dto.timeout}
            </if>
            <if test='dto.processInstanceIdList != null and dto.processInstanceIdList.size() > 0'>
                AND o.process_instance_id IN (
                <foreach item="processInstanceId" collection="dto.processInstanceIdList" separator=",">
                    #{processInstanceId}
                </foreach>
                )
            </if>
        order by o.create_time desc
    </select>

    <select id="pageProcessOrder" resultType="com.nti56.dcm.server.model.vo.MaintenanceOrderVo">
        select distinct o.*,
               p.plan_name, p.plan_number,
        o2.order_number outsourceOriginOrderNumber,
        o2.tenant_id outsourceOriginCustomerId,
        o2.customer_name outsourceOriginCustomerName
        from maintenance_order o
        left join maintenance_plan p on o.maintenance_plan_id = p.id
        LEFT JOIN maintenance_result r ON (r.maintenance_order_id = o.id)
        LEFT JOIN device d ON (d.id = r.device_id)
        left join maintenance_order o2 on o.outsource_origin_id = o2.id
        where o.deleted = 0
              and ((select count(1) from order_process_record op where op.process_instance_id = o.process_instance_id
                    and op.executor_id = #{currentUserId}) > 0)
        <if test="dto.idType != null and dto.idType == 2">
            <if test="dto.tenantId != null">
                and o.tenant_id = #{dto.tenantId}
            </if>
            <if test="dto.outsource != null">
                and o.outsource = #{dto.outsource}
            </if>
        </if>
        <if test="dto.idType != null and dto.idType == 1">
            <if test='dto.outsource == 0'>
                and o.supplier_id = #{dto.tenantId}
                and o.outsource = 1
                <if test="dto.customerId != null ">
                    and o.tenant_id = #{dto.customerId}
                </if>
            </if>
            <if test='dto.outsource == 1'>
                and o.tenant_id = #{dto.tenantId}
                and o.outsource = 1
            </if>
        </if>
        <if test="dto.supplierId != null">
            and o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.orderNumber != null and dto.orderNumber != ''">
            and o.order_number like CONCAT('%', #{dto.orderNumber},'%')
        </if>
        <if test="dto.outsourceOriginOrderNumber != null and dto.outsourceOriginOrderNumber != ''">
            and o2.order_number like CONCAT('%', #{dto.outsourceOriginOrderNumber},'%')
        </if>

        <if test="dto.outsourceOriginCustomerId != null">
            and o2.tenant_id = #{dto.outsourceOriginCustomerId}
        </if>
        <if test="dto.planNumber != null and dto.planNumber != ''">
            and p.plan_number like CONCAT('%', #{dto.planNumber},'%')
        </if>
        <if test="dto.planName != null and dto.planName != ''">
            and p.plan_name like CONCAT('%', #{dto.planName},'%')
        </if>
        <if test="dto.deviceNumber != null and dto.deviceNumber != ''">
            and d.serial_number like CONCAT('%', #{dto.deviceNumber},'%')
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and d.device_name like CONCAT('%', #{dto.deviceName},'%')
        </if>
        <if test="dto.status != null">
            and o.status = #{dto.status}
        </if>
        <if test="dto.overdue != null">
            and o.overdue = #{dto.overdue}
        </if>
        <if test='dto.statusList != null and dto.statusList.size() > 0'>
            AND o.status IN (
                <foreach item="status" collection="dto.statusList" separator=",">
                    #{status}
                </foreach>
            )
        </if>
        <if test="dto.maintenanceTimeBegin != null">
            <![CDATA[
                   AND o.plan_maintenance_time >= #{dto.maintenanceTimeBegin}
                ]]>
        </if>
        <if test="dto.maintenanceTimeEnd != null">
            <![CDATA[
                   AND o.plan_maintenance_time <= #{dto.maintenanceTimeEnd}
                ]]>
        </if>
        <if test="dto.level != null">
            and o.level = #{dto.level}
        </if>
        <if test="dto.timeout != null">
            and o.timeout = #{dto.timeout}
        </if>
        order by o.create_time desc
    </select>

    <select id="pageSupplierReceiveOrder" resultType="com.nti56.dcm.server.model.vo.MaintenanceOrderVo">
        select distinct o.*
        from maintenance_order o
        where o.deleted = 0
              and o.supplier_id = #{dto.tenantId}
              and ( o.status = 2 or o.status = 3)
              <if test="dto.orderNumber != null and dto.orderNumber != ''">
                  and o.order_number like CONCAT('%', #{dto.orderNumber},'%')
              </if>
        order by o.create_time desc
    </select>

    <select id="getOrderDetailVo" resultType="com.nti56.dcm.server.model.vo.MaintenanceOrderDetailVo">
        select o.*, p.plan_desc, p.plan_number
        from maintenance_order o
        left join maintenance_plan p on o.maintenance_plan_id = p.id
        where o.deleted = 0 and o.id = #{id}
    </select>

    <select id="listOrderByDate" resultType="com.nti56.dcm.server.entity.MaintenanceOrderEntity">
        SELECT o.*
        FROM maintenance_order o
        WHERE o.deleted = 0
        <if test='idType == 1'>
            AND o.supplier_id = #{tenantId}
            <if test="customerIds != null and customerIds.size > 0">
                AND o.tenant_id in
                <foreach collection="customerIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test='idType == 2'>
            AND o.tenant_id = #{tenantId}
        </if>
        <![CDATA[
                AND o.plan_maintenance_time >= #{begin}
                AND o.plan_maintenance_time <= #{end}
            ]]>
    </select>

    <select id="pageOrderByDevice" resultType="com.nti56.dcm.server.model.vo.MaintenanceOrderVo">
        select distinct o.*,
        p.plan_name, p.plan_number
        from maintenance_order o
        left join maintenance_plan p on o.maintenance_plan_id = p.id
        LEFT JOIN maintenance_result r ON (r.maintenance_order_id = o.id)
        LEFT JOIN device d ON (d.id = r.device_id)
        where o.deleted = 0
          and d.id = #{dto.deviceId}
          and o.outsource = #{dto.outsource}
        <if test="dto.orderNumber != null and dto.orderNumber != ''">
            and o.order_number like CONCAT('%', #{dto.orderNumber},'%')
        </if>
        <if test="dto.status != null">
            and o.status = #{dto.status}
        </if>
        <if test="dto.overdue != null">
            and o.overdue = #{dto.overdue}
        </if>
        <if test="dto.maintenanceTimeBegin != null">
            <![CDATA[
                        AND o.plan_maintenance_time >= #{dto.maintenanceTimeBegin}
                    ]]>
        </if>
        <if test="dto.maintenanceTimeEnd != null">
            <![CDATA[
                        AND o.plan_maintenance_time <= #{dto.maintenanceTimeEnd}
                    ]]>
        </if>
        order by o.create_time desc
    </select>
    <select id="listEndMaintenance" resultType="com.nti56.dcm.server.entity.MaintenanceOrderEntity">
        select * from maintenance_order where maintenance_end is not null
        and deleted = 0
        and( tenant_id = #{tenantId} or supplier_id = #{tenantId} )
        <if test="startDate != null">
            and maintenance_end &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and maintenance_end &lt;= #{endDate}
        </if>
    </select>

    <update id="timeoutBatchById">
        update maintenance_order
        set timeout = 2, updator_id = 1, updator = 'admin', update_time = #{now}
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

</mapper>