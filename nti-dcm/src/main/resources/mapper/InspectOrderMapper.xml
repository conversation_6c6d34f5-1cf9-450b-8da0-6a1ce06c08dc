<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.dcm.server.mapper.InspectOrderMapper">

    <select id="listByPlanIds" resultType="com.nti56.dcm.server.entity.InspectOrderEntity">
        SELECT * 
        FROM inspect_order o
        WHERE o.deleted = 0
            AND o.inspect_plan_id IN (
                <foreach item="planId" collection="planIds" separator=",">
                    #{planId}
                </foreach>
            )
    </select>

    <select id="listAll" resultType="com.nti56.dcm.server.entity.InspectOrderEntity">
        SELECT o.*
        FROM inspect_order o
        WHERE
            o.deleted != 1
            <if test='idType == 1'>
                AND o.supplier_id = #{tenantId}
            </if>
            <if test='idType == 2'>
                AND o.customer_id = #{tenantId}
            </if>
    </select>

    <select id="listOrderByDate" resultType="com.nti56.dcm.server.entity.InspectOrderEntity">
        SELECT o.*
        FROM inspect_order o
        WHERE o.deleted != 1
            <if test='idType == 1'>
                AND o.supplier_id = #{tenantId}
            </if>
            <if test='idType == 2'>
                AND o.customer_id = #{tenantId}
            </if>
            <![CDATA[
                AND o.plan_inspect_time >= #{begin}
                AND o.plan_inspect_time <= #{end}
            ]]>
    </select>

    <sql id="selectByParams">
        SELECT distinct o.*, p.plan_number, p.plan_name,
        o2.order_number outsourceOriginOrderNumber,
        o2.customer_id outsourceOriginCustomerId,
        o2.customer_name outsourceOriginCustomerName
        FROM inspect_order o
            LEFT JOIN inspect_plan p ON(o.inspect_plan_id = p.id)
            LEFT JOIN inspect_result r ON(r.inspect_order_id = o.id)
            LEFT JOIN device d ON(d.id = r.device_id)
        left join inspect_order o2 on o.copy_order_id = o2.id
        WHERE
            o.deleted != 1
        <if test='idType == 1'>
            <if test='params.outsource == 0'>
                AND o.supplier_id = #{tenantId}
                AND o.outsource = 1
            </if>
            <if test='params.outsource == 1'>
                AND o.customer_id = #{tenantId}
                AND o.outsource = 1
            </if>
        </if>
            <if test='idType == 2'>
                AND o.customer_id = #{tenantId}
                <if test='params.outsource != null'>
                    AND o.outsource = #{params.outsource}
                </if>
            </if>
            <if test='params.supplierId != null'>
                AND o.supplier_id = #{params.supplierId}
            </if>
            <if test='params.customerId != null'>
                AND o.customer_id = #{params.customerId}
            </if>
            <if test='params.level != null'>
                AND o.level = #{params.level}
            </if>
            <if test='params.timeout != null'>
                AND o.timeout = #{params.timeout}
            </if>
            <if test='params.overdue != null'>
                AND o.overdue = #{params.overdue}
            </if>
            <if test='params.deviceId != null'>
                AND (
                    d.id = #{params.deviceId}
                )
            </if>
            <if test='params.deviceNumber != null'>
                <if test='params.deviceNumber != ""'>
                    AND (
                        d.serial_number LIKE CONCAT('%',#{params.deviceNumber},'%')
                        OR pd.serial_number LIKE CONCAT('%',#{params.deviceNumber},'%')
                    )
                </if>
            </if>
            <if test='params.orderNumber != null'>
                <if test='params.orderNumber != ""'>
                    AND o.order_number LIKE CONCAT('%',#{params.orderNumber},'%')
                </if>
            </if>
        <if test='params.outsourceOriginOrderNumber != null'>
            <if test='params.outsourceOriginOrderNumber != ""'>
                AND o2.order_number LIKE CONCAT('%',#{params.outsourceOriginOrderNumber},'%')
            </if>
        </if>
        <if test='params.outsourceOriginCustomerId != null'>
            AND o2.customer_id = #{params.outsourceOriginCustomerId}
        </if>
            <if test='params.planNumber != null'>
                <if test='params.planNumber != ""'>
                    AND p.plan_number LIKE CONCAT('%',#{params.planNumber},'%')
                </if>
            </if>
            <if test='params.planName != null'>
                <if test='params.planName != ""'>
                    AND p.plan_name LIKE CONCAT('%',#{params.planName},'%')
                </if>
            </if>
            <if test='params.statusList != null and params.statusList.size() > 0'>
                AND o.status IN (
                    <foreach item="status" collection="params.statusList" separator=",">
                        #{status}
                    </foreach>
                )
            </if>
            <if test='params.inspectType != null'>
                AND o.inspect_type = #{params.inspectType}
            </if>
            <if test='params.status != null'>
                AND o.status = #{params.status}
            </if>
            <if test="params.planInspectTimeBegin != null">
                <![CDATA[
                    AND o.plan_inspect_time >= #{params.planInspectTimeBegin}
                ]]>
            </if>
            <if test="params.planInspectTimeEnd != null">
                <![CDATA[
                    AND o.plan_inspect_time <= #{params.planInspectTimeEnd}
                ]]>
            </if>
            <if test="params.inspectFinishTimeBegin != null">
                <![CDATA[
                        AND o.inspect_end >= #{params.inspectFinishTimeBegin}
                    ]]>
            </if>
            <if test="params.inspectFinishTimeEnd != null">
                <![CDATA[
                        AND o.inspect_end <= #{params.inspectFinishTimeEnd}
                    ]]>
            </if>
            <if test="lastModifyStartDateTime != null ">
                <![CDATA[
                and o.update_time >= #{lastModifyStartDateTime}
                ]]>
            </if>
            <if test="lastModifyEndDateTime != null ">
                <![CDATA[
                and o.update_time <= #{lastModifyEndDateTime}
                ]]>
            </if>
        ORDER BY o.create_time DESC
    </sql>

    <select id="pageByParams" resultType="com.nti56.dcm.server.model.dto.InspectOrderDto">
        <include refid="selectByParams"></include>
    </select>

    <select id="listByParams" resultType="com.nti56.dcm.server.model.dto.InspectOrderDto">
        <include refid="selectByParams"></include>
    </select>

    <sql id="fromByAuthSql">
        FROM inspect_order o
            LEFT JOIN inspect_plan p ON(o.inspect_plan_id = p.id)
        left join inspect_order o2 on o.copy_order_id = o2.id
        WHERE
            o.deleted != 1

        <if test='idType == 1'>
            <if test='params.outsource == 0'>
                AND o.supplier_id = #{tenantId}
                AND o.outsource = 1
            </if>
            <if test='params.outsource == 1'>
                AND o.customer_id = #{tenantId}
                AND o.outsource = 1
            </if>
        </if>
            <if test='idType == 2'>
                AND o.customer_id = #{tenantId}
                <if test='params.outsource != null'>
                    AND o.outsource = #{params.outsource}
                </if>
            </if>

            <if test='params.supplierId != null'>
                AND o.supplier_id = #{params.supplierId}
            </if>
            <if test='params.customerId != null'>
                AND o.customer_id = #{params.customerId}
            </if>
            <if test='params.level != null'>
                AND o.level = #{params.level}
            </if>
            <if test='params.timeout != null'>
                AND o.timeout = #{params.timeout}
            </if>
            <if test='params.overdue != null'>
                AND o.overdue = #{params.overdue}
            </if>

        <if test='params.outsourceOriginOrderNumber != null'>
            <if test='params.outsourceOriginOrderNumber != ""'>
                AND o2.order_number LIKE CONCAT('%',#{params.outsourceOriginOrderNumber},'%')
            </if>
        </if>
        <if test='params.outsourceOriginCustomerId != null'>
            AND o2.customer_id = #{params.outsourceOriginCustomerId}
        </if>
            <if test='todoTaskList != null'>
                <if test=' todoTaskList.size() > 0'>
                    AND o.process_instance_id IN (
                        <foreach collection="todoTaskList" item="task" separator=" , ">
                           #{task.processInstanceId}
                        </foreach>
                    )
                </if>
                <if test=' todoTaskList.size() == 0'>
                    AND 1 = 0
                </if>
            </if>
            <if test='todoTaskList == null'>
                AND 1 = 0
            </if>

            <if test='params.orderNumber != null'>
                <if test='params.orderNumber != ""'>
                    AND o.order_number LIKE CONCAT('%',#{params.orderNumber},'%')
                </if>
            </if>
            <if test='params.planNumber != null'>
                <if test='params.planNumber != ""'>
                    AND p.plan_number LIKE CONCAT('%',#{params.planNumber},'%')
                </if>
            </if>
            <if test='params.planName != null'>
                <if test='params.planName != ""'>
                    AND p.plan_name LIKE CONCAT('%',#{params.planName},'%')
                </if>
            </if>
            <if test='params.statusList != null and params.statusList.size() > 0'>
                AND o.status IN (
                    <foreach item="status" collection="params.statusList" separator=",">
                        #{status}
                    </foreach>
                )
            </if>
            <if test='params.inspectType != null'>
                AND o.inspect_type = #{params.inspectType}
            </if>
            <if test='params.status != null'>
                AND o.status = #{params.status}
            </if>
            <if test="params.planInspectTimeBegin != null">
                <![CDATA[
                    AND o.plan_inspect_time >= #{params.planInspectTimeBegin}
                ]]>
            </if>
            <if test="params.planInspectTimeEnd != null">
                <![CDATA[
                    AND o.plan_inspect_time <= #{params.planInspectTimeEnd}
                ]]>
            </if>
        ORDER BY o.create_time DESC
    </sql>

    <sql id="selectByAuthSql">
        SELECT o.*, p.plan_number, p.plan_name, o.inspect_type,
        o2.order_number outsourceOriginOrderNumber,
        o2.customer_id outsourceOriginCustomerId,
        o2.customer_name outsourceOriginCustomerName
        <include refid="fromByAuthSql"></include>
    </sql>

    <select id="pageByAuth" resultType="com.nti56.dcm.server.model.dto.InspectOrderDto">
        <include refid="selectByAuthSql"></include>
    </select>

    <select id="listByAuth" resultType="com.nti56.dcm.server.model.dto.InspectOrderDto">
        <include refid="selectByAuthSql"></include>
    </select>

    <select id="countByAuth" resultType="java.lang.Integer">
        SELECT count(*)
        <include refid="fromByAuthSql"></include>
    </select>

    <select id="pageByProcessUserId" resultType="com.nti56.dcm.server.model.dto.InspectOrderDto">
        SELECT distinct o.*, p.plan_number, p.plan_name,
        o2.order_number outsourceOriginOrderNumber,
        o2.customer_id outsourceOriginCustomerId,
        o2.customer_name outsourceOriginCustomerName
        FROM order_process_record pr
            LEFT JOIN inspect_order o ON(pr.order_id = o.id)
            LEFT JOIN inspect_plan p ON(o.inspect_plan_id = p.id)
        left join inspect_order o2 on o.copy_order_id = o2.id
        WHERE
            o.deleted != 1
            AND pr.executor_id = #{userId}
        <if test='idType == 1'>
            <if test='params.outsource == 0'>
                AND o.supplier_id = #{tenantId}
                AND o.outsource = 1
            </if>
            <if test='params.outsource == 1'>
                AND o.customer_id = #{tenantId}
                AND o.outsource = 1
            </if>
        </if>
        <if test='idType == 2'>
            AND o.customer_id = #{tenantId}
            <if test='params.outsource != null'>
                AND o.outsource = #{params.outsource}
            </if>
        </if>
            <if test='params.supplierId != null'>
                AND o.supplier_id = #{params.supplierId}
            </if>
            <if test='params.customerId != null'>
                AND o.customer_id = #{params.customerId}
            </if>
            <if test='params.level != null'>
                AND o.level = #{params.level}
            </if>
            <if test='params.timeout != null'>
                AND o.timeout = #{params.timeout}
            </if>
            <if test='params.overdue != null'>
                AND o.overdue = #{params.overdue}
            </if>

        <if test='params.outsourceOriginOrderNumber != null'>
            <if test='params.outsourceOriginOrderNumber != ""'>
                AND o2.order_number LIKE CONCAT('%',#{params.outsourceOriginOrderNumber},'%')
            </if>
        </if>
        <if test='params.outsourceOriginCustomerId != null'>
            AND o2.customer_id = #{params.outsourceOriginCustomerId}
        </if>
            <if test='params.orderNumber != null'>
                <if test='params.orderNumber != ""'>
                    AND o.order_number LIKE CONCAT('%',#{params.orderNumber},'%')
                </if>
            </if>
            <if test='params.planNumber != null'>
                <if test='params.planNumber != ""'>
                    AND p.plan_number LIKE CONCAT('%',#{params.planNumber},'%')
                </if>
            </if>
            <if test='params.planName != null'>
                <if test='params.planName != ""'>
                    AND p.plan_name LIKE CONCAT('%',#{params.planName},'%')
                </if>
            </if>
            <if test='params.statusList != null and params.statusList.size() > 0'>
                AND o.status IN (
                    <foreach item="status" collection="params.statusList" separator=",">
                        #{status}
                    </foreach>
                )
            </if>
            <if test='params.inspectType != null'>
                AND o.inspect_type = #{params.inspectType}
            </if>
            <if test='params.status != null'>
                AND o.status = #{params.status}
            </if>
            <if test="params.planInspectTimeBegin != null">
                <![CDATA[
                    AND o.plan_inspect_time >= #{params.planInspectTimeBegin}
                ]]>
            </if>
            <if test="params.planInspectTimeEnd != null">
                <![CDATA[
                    AND o.plan_inspect_time <= #{params.planInspectTimeEnd}
                ]]>
            </if>
        ORDER BY o.create_time DESC 
    </select>
    <select id="listEndInspect" resultType="com.nti56.dcm.server.entity.InspectOrderEntity">
        select * from inspect_order where inspect_end is not null
        and deleted = 0
        and( customer_id = #{tenantId} or supplier_id = #{tenantId} )
        <if test="startDate != null">
            and inspect_end &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and inspect_end &lt;= #{endDate}
        </if>
    </select>

    <update id="updateBatchOverdueByIds">
        UPDATE inspect_order 
        SET overdue = 2 
        WHERE id IN
        <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

</mapper>